{"version": 3, "sources": ["../../../../../../node_modules/@angular/material/fesm2022/chips.mjs"], "sourcesContent": ["import { FocusMonitor, _Id<PERSON>enerator, FocusKeyManager } from '@angular/cdk/a11y';\nimport { ENTER, SPACE, BACKSPACE, DELETE, TAB, hasModifierKey, UP_ARROW, DOWN_ARROW } from '@angular/cdk/keycodes';\nimport { _CdkPrivateStyleLoader, _VisuallyHiddenLoader } from '@angular/cdk/private';\nimport { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, ElementRef, booleanAttribute, numberAttribute, Directive, Input, ChangeDetectorRef, NgZone, EventEmitter, Injector, ANIMATION_MODULE_TYPE, Component, ViewEncapsulation, ChangeDetectionStrategy, ContentChildren, Output, ContentChild, ViewChild, afterNextRender, QueryList, forwardRef, NgModule } from '@angular/core';\nimport { Subject, merge } from 'rxjs';\nimport { _ as _StructuralStylesLoader } from './structural-styles-BQUT6wsL.mjs';\nimport { a as MAT_RIPPLE_GLOBAL_OPTIONS } from './ripple-BT3tzh6F.mjs';\nimport { M as MatRippleLoader } from './ripple-loader-Ce3DAhPW.mjs';\nimport { takeUntil, startWith, switchMap } from 'rxjs/operators';\nimport { Directionality } from '@angular/cdk/bidi';\nimport { NG_VALUE_ACCESSOR, NgControl, Validators, NgForm, FormGroupDirective } from '@angular/forms';\nimport { E as ErrorStateMatcher } from './error-options-Dm2JJUbF.mjs';\nimport { _ as _ErrorStateTracker } from './error-state-Dtb1IHM-.mjs';\nimport { k as MatFormFieldControl, h as MAT_FORM_FIELD } from './form-field-DqPi4knt.mjs';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\nimport { M as MatRippleModule } from './index-SYVYjXwK.mjs';\nimport '@angular/cdk/platform';\nimport '@angular/cdk/coercion';\nimport '@angular/cdk/observers/private';\n\n/** Injection token to be used to override the default options for the chips module. */\nconst _c0 = [\"*\", [[\"mat-chip-avatar\"], [\"\", \"matChipAvatar\", \"\"]], [[\"mat-chip-trailing-icon\"], [\"\", \"matChipRemove\", \"\"], [\"\", \"matChipTrailingIcon\", \"\"]]];\nconst _c1 = [\"*\", \"mat-chip-avatar, [matChipAvatar]\", \"mat-chip-trailing-icon,[matChipRemove],[matChipTrailingIcon]\"];\nfunction MatChip_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 3);\n    i0.ɵɵprojection(1, 1);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MatChip_Conditional_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 6);\n    i0.ɵɵprojection(1, 2);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MatChipOption_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 3);\n    i0.ɵɵprojection(1, 1);\n    i0.ɵɵelementStart(2, \"span\", 8);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 9);\n    i0.ɵɵelement(4, \"path\", 10);\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction MatChipOption_Conditional_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 6);\n    i0.ɵɵprojection(1, 2);\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c2 = \".mdc-evolution-chip,.mdc-evolution-chip__cell,.mdc-evolution-chip__action{display:inline-flex;align-items:center}.mdc-evolution-chip{position:relative;max-width:100%}.mdc-evolution-chip__cell,.mdc-evolution-chip__action{height:100%}.mdc-evolution-chip__cell--primary{flex-basis:100%;overflow-x:hidden}.mdc-evolution-chip__cell--trailing{flex:1 0 auto}.mdc-evolution-chip__action{align-items:center;background:none;border:none;box-sizing:content-box;cursor:pointer;display:inline-flex;justify-content:center;outline:none;padding:0;text-decoration:none;color:inherit}.mdc-evolution-chip__action--presentational{cursor:auto}.mdc-evolution-chip--disabled,.mdc-evolution-chip__action:disabled{pointer-events:none}@media(forced-colors: active){.mdc-evolution-chip--disabled,.mdc-evolution-chip__action:disabled{forced-color-adjust:none}}.mdc-evolution-chip__action--primary{font:inherit;letter-spacing:inherit;white-space:inherit;overflow-x:hidden}.mat-mdc-standard-chip .mdc-evolution-chip__action--primary::before{border-width:var(--mdc-chip-outline-width, 1px);border-radius:var(--mdc-chip-container-shape-radius, 8px);box-sizing:border-box;content:\\\"\\\";height:100%;left:0;position:absolute;pointer-events:none;top:0;width:100%;z-index:1;border-style:solid}.mat-mdc-standard-chip .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:12px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__action--primary::before{border-color:var(--mdc-chip-outline-color, var(--mat-sys-outline))}.mdc-evolution-chip__action--primary:not(.mdc-evolution-chip__action--presentational):not(.mdc-ripple-upgraded):focus::before{border-color:var(--mdc-chip-focus-outline-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__action--primary::before{border-color:var(--mdc-chip-disabled-outline-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-standard-chip.mdc-evolution-chip--selected .mdc-evolution-chip__action--primary::before{border-width:var(--mdc-chip-flat-selected-outline-width, 0)}.mat-mdc-basic-chip .mdc-evolution-chip__action--primary{font:inherit}.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}.mdc-evolution-chip__action--trailing{position:relative;overflow:visible}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__action--trailing{color:var(--mdc-chip-with-trailing-icon-trailing-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__action--trailing{color:var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-color, var(--mat-sys-on-surface))}.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mdc-evolution-chip__text-label{-webkit-user-select:none;user-select:none;white-space:nowrap;text-overflow:ellipsis;overflow:hidden}.mat-mdc-standard-chip .mdc-evolution-chip__text-label{font-family:var(--mdc-chip-label-text-font, var(--mat-sys-label-large-font));line-height:var(--mdc-chip-label-text-line-height, var(--mat-sys-label-large-line-height));font-size:var(--mdc-chip-label-text-size, var(--mat-sys-label-large-size));font-weight:var(--mdc-chip-label-text-weight, var(--mat-sys-label-large-weight));letter-spacing:var(--mdc-chip-label-text-tracking, var(--mat-sys-label-large-tracking))}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__text-label{color:var(--mdc-chip-label-text-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--selected:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__text-label{color:var(--mdc-chip-selected-label-text-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__text-label,.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled .mdc-evolution-chip__text-label{color:var(--mdc-chip-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-evolution-chip__graphic{align-items:center;display:inline-flex;justify-content:center;overflow:hidden;pointer-events:none;position:relative;flex:1 0 auto}.mat-mdc-standard-chip .mdc-evolution-chip__graphic{width:var(--mdc-chip-with-avatar-avatar-size, 24px);height:var(--mdc-chip-with-avatar-avatar-size, 24px);font-size:var(--mdc-chip-with-avatar-avatar-size, 24px)}.mdc-evolution-chip--selecting .mdc-evolution-chip__graphic{transition:width 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--selectable:not(.mdc-evolution-chip--selected):not(.mdc-evolution-chip--with-primary-icon) .mdc-evolution-chip__graphic{width:0}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:6px;padding-right:6px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:4px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:8px;padding-right:4px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:6px;padding-right:6px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:4px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:8px;padding-right:4px}.mdc-evolution-chip__checkmark{position:absolute;opacity:0;top:50%;left:50%;height:20px;width:20px}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__checkmark{color:var(--mdc-chip-with-icon-selected-icon-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__checkmark{color:var(--mdc-chip-with-icon-disabled-icon-color, var(--mat-sys-on-surface))}.mdc-evolution-chip--selecting .mdc-evolution-chip__checkmark{transition:transform 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1);transform:translate(-75%, -50%)}.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark{transform:translate(-50%, -50%);opacity:1}.mdc-evolution-chip__checkmark-svg{display:block}.mdc-evolution-chip__checkmark-path{stroke-width:2px;stroke-dasharray:29.7833385;stroke-dashoffset:29.7833385;stroke:currentColor}.mdc-evolution-chip--selecting .mdc-evolution-chip__checkmark-path{transition:stroke-dashoffset 150ms 45ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark-path{stroke-dashoffset:0}@media(forced-colors: active){.mdc-evolution-chip__checkmark-path{stroke:CanvasText !important}}.mat-mdc-standard-chip .mdc-evolution-chip__icon--trailing{height:18px;width:18px;font-size:18px}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing.mat-mdc-chip-remove{opacity:calc(var(--mat-chip-trailing-action-opacity, 1)*var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38))}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing.mat-mdc-chip-remove:focus{opacity:calc(var(--mat-chip-trailing-action-focus-opacity, 1)*var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38))}.mat-mdc-standard-chip{border-radius:var(--mdc-chip-container-shape-radius, 8px);height:var(--mdc-chip-container-height, 32px)}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled){background-color:var(--mdc-chip-elevated-container-color, transparent)}.mat-mdc-standard-chip.mdc-evolution-chip--disabled{background-color:var(--mdc-chip-elevated-disabled-container-color)}.mat-mdc-standard-chip.mdc-evolution-chip--selected:not(.mdc-evolution-chip--disabled){background-color:var(--mdc-chip-elevated-selected-container-color, var(--mat-sys-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled{background-color:var(--mdc-chip-flat-disabled-selected-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}@media(forced-colors: active){.mat-mdc-standard-chip{outline:solid 1px}}.mat-mdc-standard-chip .mdc-evolution-chip__icon--primary{border-radius:var(--mdc-chip-with-avatar-avatar-shape-radius, 24px);width:var(--mdc-chip-with-icon-icon-size, 18px);height:var(--mdc-chip-with-icon-icon-size, 18px);font-size:var(--mdc-chip-with-icon-icon-size, 18px)}.mdc-evolution-chip--selected .mdc-evolution-chip__icon--primary{opacity:0}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__icon--primary{color:var(--mdc-chip-with-icon-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--primary{color:var(--mdc-chip-with-icon-disabled-icon-color, var(--mat-sys-on-surface))}.mat-mdc-chip-highlighted{--mdc-chip-with-icon-icon-color:var(--mdc-chip-with-icon-selected-icon-color, var(--mat-sys-on-secondary-container));--mdc-chip-elevated-container-color:var(--mdc-chip-elevated-selected-container-color, var(--mat-sys-secondary-container));--mdc-chip-label-text-color:var(--mdc-chip-selected-label-text-color, var(--mat-sys-on-secondary-container));--mdc-chip-outline-width:var(--mdc-chip-flat-selected-outline-width, 0)}.mat-mdc-chip-focus-overlay{background:var(--mdc-chip-focus-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-chip-selected .mat-mdc-chip-focus-overlay,.mat-mdc-chip-highlighted .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-selected-focus-state-layer-color, var(--mat-sys-on-secondary-container))}.mat-mdc-chip:hover .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-hover-state-layer-color, var(--mat-sys-on-surface-variant));opacity:var(--mdc-chip-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip-focus-overlay .mat-mdc-chip-selected:hover,.mat-mdc-chip-highlighted:hover .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-selected-hover-state-layer-color, var(--mat-sys-on-secondary-container));opacity:var(--mdc-chip-selected-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip.cdk-focused .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-focus-state-layer-color, var(--mat-sys-on-surface-variant));opacity:var(--mdc-chip-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-chip-selected.cdk-focused .mat-mdc-chip-focus-overlay,.mat-mdc-chip-highlighted.cdk-focused .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-selected-focus-state-layer-color, var(--mat-sys-on-secondary-container));opacity:var(--mdc-chip-selected-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mdc-evolution-chip--disabled:not(.mdc-evolution-chip--selected) .mat-mdc-chip-avatar{opacity:var(--mdc-chip-with-avatar-disabled-avatar-opacity, 0.38)}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing{opacity:var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38)}.mdc-evolution-chip--disabled.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark{opacity:var(--mdc-chip-with-icon-disabled-icon-opacity, 0.38)}.mat-mdc-standard-chip.mdc-evolution-chip--disabled{opacity:var(--mat-chip-disabled-container-opacity, 1)}.mat-mdc-standard-chip.mdc-evolution-chip--selected .mdc-evolution-chip__icon--trailing,.mat-mdc-standard-chip.mat-mdc-chip-highlighted .mdc-evolution-chip__icon--trailing{color:var(--mat-chip-selected-trailing-icon-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing,.mat-mdc-standard-chip.mat-mdc-chip-highlighted.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing{color:var(--mat-chip-selected-disabled-trailing-icon-color, var(--mat-sys-on-surface))}.mat-mdc-chip-remove{opacity:var(--mat-chip-trailing-action-opacity, 1)}.mat-mdc-chip-remove:focus{opacity:var(--mat-chip-trailing-action-focus-opacity, 1)}.mat-mdc-chip-remove::after{background-color:var(--mat-chip-trailing-action-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-chip-remove:hover::after{opacity:var(--mat-chip-trailing-action-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip-remove:focus::after{opacity:var(--mat-chip-trailing-action-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-chip-selected .mat-mdc-chip-remove::after,.mat-mdc-chip-highlighted .mat-mdc-chip-remove::after{background-color:var(--mat-chip-selected-trailing-action-state-layer-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-standard-chip .mdc-evolution-chip__cell--primary,.mat-mdc-standard-chip .mdc-evolution-chip__action--primary,.mat-mdc-standard-chip .mat-mdc-chip-action-label{overflow:visible}.mat-mdc-standard-chip .mat-mdc-chip-graphic,.mat-mdc-standard-chip .mat-mdc-chip-trailing-icon{box-sizing:content-box}.mat-mdc-standard-chip._mat-animation-noopable,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__graphic,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__checkmark,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__checkmark-path{transition-duration:1ms;animation-duration:1ms}.mat-mdc-chip-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;opacity:0;border-radius:inherit;transition:opacity 150ms linear}._mat-animation-noopable .mat-mdc-chip-focus-overlay{transition:none}.mat-mdc-basic-chip .mat-mdc-chip-focus-overlay{display:none}.mat-mdc-chip .mat-ripple.mat-mdc-chip-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-chip-avatar{text-align:center;line-height:1;color:var(--mdc-chip-with-icon-icon-color, currentColor)}.mat-mdc-chip{position:relative;z-index:0}.mat-mdc-chip-action-label{text-align:left;z-index:1}[dir=rtl] .mat-mdc-chip-action-label{text-align:right}.mat-mdc-chip.mdc-evolution-chip--with-trailing-action .mat-mdc-chip-action-label{position:relative}.mat-mdc-chip-action-label .mat-mdc-chip-primary-focus-indicator{position:absolute;top:0;right:0;bottom:0;left:0;pointer-events:none}.mat-mdc-chip-action-label .mat-focus-indicator::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-mdc-chip-remove::before{margin:calc(var(--mat-focus-indicator-border-width, 3px)*-1);left:8px;right:8px}.mat-mdc-chip-remove::after{content:\\\"\\\";display:block;opacity:0;position:absolute;top:-3px;bottom:-3px;left:5px;right:5px;border-radius:50%;box-sizing:border-box;padding:12px;margin:-12px;background-clip:content-box}.mat-mdc-chip-remove .mat-icon{width:18px;height:18px;font-size:18px;box-sizing:content-box}.mat-chip-edit-input{cursor:text;display:inline-block;color:inherit;outline:0}@media(forced-colors: active){.mat-mdc-chip-selected:not(.mat-mdc-chip-multiple){outline-width:3px}}.mat-mdc-chip-action:focus .mat-focus-indicator::before{content:\\\"\\\"}.mdc-evolution-chip__icon,.mat-mdc-chip-remove .mat-icon{min-height:fit-content}\\n\";\nconst _c3 = [[[\"mat-chip-avatar\"], [\"\", \"matChipAvatar\", \"\"]], [[\"\", \"matChipEditInput\", \"\"]], \"*\", [[\"mat-chip-trailing-icon\"], [\"\", \"matChipRemove\", \"\"], [\"\", \"matChipTrailingIcon\", \"\"]]];\nconst _c4 = [\"mat-chip-avatar, [matChipAvatar]\", \"[matChipEditInput]\", \"*\", \"mat-chip-trailing-icon,[matChipRemove],[matChipTrailingIcon]\"];\nfunction MatChipRow_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 0);\n  }\n}\nfunction MatChipRow_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 2);\n    i0.ɵɵprojection(1);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MatChipRow_Conditional_4_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0, 1);\n  }\n}\nfunction MatChipRow_Conditional_4_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 7);\n  }\n}\nfunction MatChipRow_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MatChipRow_Conditional_4_Conditional_0_Template, 1, 0)(1, MatChipRow_Conditional_4_Conditional_1_Template, 1, 0, \"span\", 7);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵconditional(ctx_r0.contentEditInput ? 0 : 1);\n  }\n}\nfunction MatChipRow_Conditional_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0, 2);\n  }\n}\nfunction MatChipRow_Conditional_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 5);\n    i0.ɵɵprojection(1, 3);\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c5 = [\"*\"];\nconst _c6 = \".mat-mdc-chip-set{display:flex}.mat-mdc-chip-set:focus{outline:none}.mat-mdc-chip-set .mdc-evolution-chip-set__chips{min-width:100%;margin-left:-8px;margin-right:0}.mat-mdc-chip-set .mdc-evolution-chip{margin:4px 0 4px 8px}[dir=rtl] .mat-mdc-chip-set .mdc-evolution-chip-set__chips{margin-left:0;margin-right:-8px}[dir=rtl] .mat-mdc-chip-set .mdc-evolution-chip{margin-left:0;margin-right:8px}.mdc-evolution-chip-set__chips{display:flex;flex-flow:wrap;min-width:0}.mat-mdc-chip-set-stacked{flex-direction:column;align-items:flex-start}.mat-mdc-chip-set-stacked .mat-mdc-chip{width:100%}.mat-mdc-chip-set-stacked .mdc-evolution-chip__graphic{flex-grow:0}.mat-mdc-chip-set-stacked .mdc-evolution-chip__action--primary{flex-basis:100%;justify-content:start}input.mat-mdc-chip-input{flex:1 0 150px;margin-left:8px}[dir=rtl] input.mat-mdc-chip-input{margin-left:0;margin-right:8px}\\n\";\nconst MAT_CHIPS_DEFAULT_OPTIONS = new InjectionToken('mat-chips-default-options', {\n  providedIn: 'root',\n  factory: () => ({\n    separatorKeyCodes: [ENTER]\n  })\n});\n/**\n * Injection token that can be used to reference instances of `MatChipAvatar`. It serves as\n * alternative token to the actual `MatChipAvatar` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_CHIP_AVATAR = new InjectionToken('MatChipAvatar');\n/**\n * Injection token that can be used to reference instances of `MatChipTrailingIcon`. It serves as\n * alternative token to the actual `MatChipTrailingIcon` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_CHIP_TRAILING_ICON = new InjectionToken('MatChipTrailingIcon');\n/**\n * Injection token that can be used to reference instances of `MatChipRemove`. It serves as\n * alternative token to the actual `MatChipRemove` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_CHIP_REMOVE = new InjectionToken('MatChipRemove');\n/**\n * Injection token used to avoid a circular dependency between the `MatChip` and `MatChipAction`.\n */\nconst MAT_CHIP = new InjectionToken('MatChip');\n\n/**\n * Section within a chip.\n * @docs-private\n */\nclass MatChipAction {\n  _elementRef = inject(ElementRef);\n  _parentChip = inject(MAT_CHIP);\n  /** Whether the action is interactive. */\n  isInteractive = true;\n  /** Whether this is the primary action in the chip. */\n  _isPrimary = true;\n  /** Whether the action is disabled. */\n  get disabled() {\n    return this._disabled || this._parentChip?.disabled || false;\n  }\n  set disabled(value) {\n    this._disabled = value;\n  }\n  _disabled = false;\n  /** Tab index of the action. */\n  tabIndex = -1;\n  /**\n   * Private API to allow focusing this chip when it is disabled.\n   */\n  _allowFocusWhenDisabled = false;\n  /**\n   * Determine the value of the disabled attribute for this chip action.\n   */\n  _getDisabledAttribute() {\n    // When this chip action is disabled and focusing disabled chips is not permitted, return empty\n    // string to indicate that disabled attribute should be included.\n    return this.disabled && !this._allowFocusWhenDisabled ? '' : null;\n  }\n  /**\n   * Determine the value of the tabindex attribute for this chip action.\n   */\n  _getTabindex() {\n    return this.disabled && !this._allowFocusWhenDisabled || !this.isInteractive ? null : this.tabIndex.toString();\n  }\n  constructor() {\n    inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n    if (this._elementRef.nativeElement.nodeName === 'BUTTON') {\n      this._elementRef.nativeElement.setAttribute('type', 'button');\n    }\n  }\n  focus() {\n    this._elementRef.nativeElement.focus();\n  }\n  _handleClick(event) {\n    if (!this.disabled && this.isInteractive && this._isPrimary) {\n      event.preventDefault();\n      this._parentChip._handlePrimaryActionInteraction();\n    }\n  }\n  _handleKeydown(event) {\n    if ((event.keyCode === ENTER || event.keyCode === SPACE) && !this.disabled && this.isInteractive && this._isPrimary && !this._parentChip._isEditing) {\n      event.preventDefault();\n      this._parentChip._handlePrimaryActionInteraction();\n    }\n  }\n  static ɵfac = function MatChipAction_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatChipAction)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatChipAction,\n    selectors: [[\"\", \"matChipAction\", \"\"]],\n    hostAttrs: [1, \"mdc-evolution-chip__action\", \"mat-mdc-chip-action\"],\n    hostVars: 9,\n    hostBindings: function MatChipAction_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function MatChipAction_click_HostBindingHandler($event) {\n          return ctx._handleClick($event);\n        })(\"keydown\", function MatChipAction_keydown_HostBindingHandler($event) {\n          return ctx._handleKeydown($event);\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵattribute(\"tabindex\", ctx._getTabindex())(\"disabled\", ctx._getDisabledAttribute())(\"aria-disabled\", ctx.disabled);\n        i0.ɵɵclassProp(\"mdc-evolution-chip__action--primary\", ctx._isPrimary)(\"mdc-evolution-chip__action--presentational\", !ctx.isInteractive)(\"mdc-evolution-chip__action--trailing\", !ctx._isPrimary);\n      }\n    },\n    inputs: {\n      isInteractive: \"isInteractive\",\n      disabled: [2, \"disabled\", \"disabled\", booleanAttribute],\n      tabIndex: [2, \"tabIndex\", \"tabIndex\", value => value == null ? -1 : numberAttribute(value)],\n      _allowFocusWhenDisabled: \"_allowFocusWhenDisabled\"\n    }\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatChipAction, [{\n    type: Directive,\n    args: [{\n      selector: '[matChipAction]',\n      host: {\n        'class': 'mdc-evolution-chip__action mat-mdc-chip-action',\n        '[class.mdc-evolution-chip__action--primary]': '_isPrimary',\n        '[class.mdc-evolution-chip__action--presentational]': '!isInteractive',\n        '[class.mdc-evolution-chip__action--trailing]': '!_isPrimary',\n        '[attr.tabindex]': '_getTabindex()',\n        '[attr.disabled]': '_getDisabledAttribute()',\n        '[attr.aria-disabled]': 'disabled',\n        '(click)': '_handleClick($event)',\n        '(keydown)': '_handleKeydown($event)'\n      }\n    }]\n  }], () => [], {\n    isInteractive: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    tabIndex: [{\n      type: Input,\n      args: [{\n        transform: value => value == null ? -1 : numberAttribute(value)\n      }]\n    }],\n    _allowFocusWhenDisabled: [{\n      type: Input\n    }]\n  });\n})();\n\n/** Avatar image within a chip. */\nclass MatChipAvatar {\n  static ɵfac = function MatChipAvatar_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatChipAvatar)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatChipAvatar,\n    selectors: [[\"mat-chip-avatar\"], [\"\", \"matChipAvatar\", \"\"]],\n    hostAttrs: [\"role\", \"img\", 1, \"mat-mdc-chip-avatar\", \"mdc-evolution-chip__icon\", \"mdc-evolution-chip__icon--primary\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MAT_CHIP_AVATAR,\n      useExisting: MatChipAvatar\n    }])]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatChipAvatar, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-chip-avatar, [matChipAvatar]',\n      host: {\n        'class': 'mat-mdc-chip-avatar mdc-evolution-chip__icon mdc-evolution-chip__icon--primary',\n        'role': 'img'\n      },\n      providers: [{\n        provide: MAT_CHIP_AVATAR,\n        useExisting: MatChipAvatar\n      }]\n    }]\n  }], null, null);\n})();\n/** Non-interactive trailing icon in a chip. */\nclass MatChipTrailingIcon extends MatChipAction {\n  /**\n   * MDC considers all trailing actions as a remove icon,\n   * but we support non-interactive trailing icons.\n   */\n  isInteractive = false;\n  _isPrimary = false;\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMatChipTrailingIcon_BaseFactory;\n    return function MatChipTrailingIcon_Factory(__ngFactoryType__) {\n      return (ɵMatChipTrailingIcon_BaseFactory || (ɵMatChipTrailingIcon_BaseFactory = i0.ɵɵgetInheritedFactory(MatChipTrailingIcon)))(__ngFactoryType__ || MatChipTrailingIcon);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatChipTrailingIcon,\n    selectors: [[\"mat-chip-trailing-icon\"], [\"\", \"matChipTrailingIcon\", \"\"]],\n    hostAttrs: [\"aria-hidden\", \"true\", 1, \"mat-mdc-chip-trailing-icon\", \"mdc-evolution-chip__icon\", \"mdc-evolution-chip__icon--trailing\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MAT_CHIP_TRAILING_ICON,\n      useExisting: MatChipTrailingIcon\n    }]), i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatChipTrailingIcon, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-chip-trailing-icon, [matChipTrailingIcon]',\n      host: {\n        'class': 'mat-mdc-chip-trailing-icon mdc-evolution-chip__icon mdc-evolution-chip__icon--trailing',\n        'aria-hidden': 'true'\n      },\n      providers: [{\n        provide: MAT_CHIP_TRAILING_ICON,\n        useExisting: MatChipTrailingIcon\n      }]\n    }]\n  }], null, null);\n})();\n/**\n * Directive to remove the parent chip when the trailing icon is clicked or\n * when the ENTER key is pressed on it.\n *\n * Recommended for use with the Material Design \"cancel\" icon\n * available at https://material.io/icons/#ic_cancel.\n *\n * Example:\n *\n * ```\n * <mat-chip>\n *   <mat-icon matChipRemove>cancel</mat-icon>\n * </mat-chip>\n * ```\n */\nclass MatChipRemove extends MatChipAction {\n  _isPrimary = false;\n  _handleClick(event) {\n    if (!this.disabled) {\n      event.stopPropagation();\n      event.preventDefault();\n      this._parentChip.remove();\n    }\n  }\n  _handleKeydown(event) {\n    if ((event.keyCode === ENTER || event.keyCode === SPACE) && !this.disabled) {\n      event.stopPropagation();\n      event.preventDefault();\n      this._parentChip.remove();\n    }\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMatChipRemove_BaseFactory;\n    return function MatChipRemove_Factory(__ngFactoryType__) {\n      return (ɵMatChipRemove_BaseFactory || (ɵMatChipRemove_BaseFactory = i0.ɵɵgetInheritedFactory(MatChipRemove)))(__ngFactoryType__ || MatChipRemove);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatChipRemove,\n    selectors: [[\"\", \"matChipRemove\", \"\"]],\n    hostAttrs: [\"role\", \"button\", 1, \"mat-mdc-chip-remove\", \"mat-mdc-chip-trailing-icon\", \"mat-focus-indicator\", \"mdc-evolution-chip__icon\", \"mdc-evolution-chip__icon--trailing\"],\n    hostVars: 1,\n    hostBindings: function MatChipRemove_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"aria-hidden\", null);\n      }\n    },\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MAT_CHIP_REMOVE,\n      useExisting: MatChipRemove\n    }]), i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatChipRemove, [{\n    type: Directive,\n    args: [{\n      selector: '[matChipRemove]',\n      host: {\n        'class': 'mat-mdc-chip-remove mat-mdc-chip-trailing-icon mat-focus-indicator ' + 'mdc-evolution-chip__icon mdc-evolution-chip__icon--trailing',\n        'role': 'button',\n        '[attr.aria-hidden]': 'null'\n      },\n      providers: [{\n        provide: MAT_CHIP_REMOVE,\n        useExisting: MatChipRemove\n      }]\n    }]\n  }], null, null);\n})();\n\n/**\n * Material design styled Chip base component. Used inside the MatChipSet component.\n *\n * Extended by MatChipOption and MatChipRow for different interaction patterns.\n */\nclass MatChip {\n  _changeDetectorRef = inject(ChangeDetectorRef);\n  _elementRef = inject(ElementRef);\n  _ngZone = inject(NgZone);\n  _focusMonitor = inject(FocusMonitor);\n  _globalRippleOptions = inject(MAT_RIPPLE_GLOBAL_OPTIONS, {\n    optional: true\n  });\n  _document = inject(DOCUMENT);\n  /** Emits when the chip is focused. */\n  _onFocus = new Subject();\n  /** Emits when the chip is blurred. */\n  _onBlur = new Subject();\n  /** Whether this chip is a basic (unstyled) chip. */\n  _isBasicChip;\n  /** Role for the root of the chip. */\n  role = null;\n  /** Whether the chip has focus. */\n  _hasFocusInternal = false;\n  /** Whether moving focus into the chip is pending. */\n  _pendingFocus;\n  /** Subscription to changes in the chip's actions. */\n  _actionChanges;\n  /** Whether animations for the chip are enabled. */\n  _animationsDisabled;\n  /** All avatars present in the chip. */\n  _allLeadingIcons;\n  /** All trailing icons present in the chip. */\n  _allTrailingIcons;\n  /** All remove icons present in the chip. */\n  _allRemoveIcons;\n  _hasFocus() {\n    return this._hasFocusInternal;\n  }\n  /** A unique id for the chip. If none is supplied, it will be auto-generated. */\n  id = inject(_IdGenerator).getId('mat-mdc-chip-');\n  // TODO(#26104): Consider deprecating and using `_computeAriaAccessibleName` instead.\n  // `ariaLabel` may be unnecessary, and `_computeAriaAccessibleName` only supports\n  // datepicker's use case.\n  /** ARIA label for the content of the chip. */\n  ariaLabel = null;\n  // TODO(#26104): Consider deprecating and using `_computeAriaAccessibleName` instead.\n  // `ariaDescription` may be unnecessary, and `_computeAriaAccessibleName` only supports\n  // datepicker's use case.\n  /** ARIA description for the content of the chip. */\n  ariaDescription = null;\n  /** Id of a span that contains this chip's aria description. */\n  _ariaDescriptionId = `${this.id}-aria-description`;\n  /** Whether the chip list is disabled. */\n  _chipListDisabled = false;\n  _textElement;\n  /**\n   * The value of the chip. Defaults to the content inside\n   * the `mat-mdc-chip-action-label` element.\n   */\n  get value() {\n    return this._value !== undefined ? this._value : this._textElement.textContent.trim();\n  }\n  set value(value) {\n    this._value = value;\n  }\n  _value;\n  // TODO: should be typed as `ThemePalette` but internal apps pass in arbitrary strings.\n  /**\n   * Theme color of the chip. This API is supported in M2 themes only, it has no\n   * effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/chips/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   */\n  color;\n  /**\n   * Determines whether or not the chip displays the remove styling and emits (removed) events.\n   */\n  removable = true;\n  /**\n   * Colors the chip for emphasis as if it were selected.\n   */\n  highlighted = false;\n  /** Whether the ripple effect is disabled or not. */\n  disableRipple = false;\n  /** Whether the chip is disabled. */\n  get disabled() {\n    return this._disabled || this._chipListDisabled;\n  }\n  set disabled(value) {\n    this._disabled = value;\n  }\n  _disabled = false;\n  /** Emitted when a chip is to be removed. */\n  removed = new EventEmitter();\n  /** Emitted when the chip is destroyed. */\n  destroyed = new EventEmitter();\n  /** The unstyled chip selector for this component. */\n  basicChipAttrName = 'mat-basic-chip';\n  /** The chip's leading icon. */\n  leadingIcon;\n  /** The chip's trailing icon. */\n  trailingIcon;\n  /** The chip's trailing remove icon. */\n  removeIcon;\n  /** Action receiving the primary set of user interactions. */\n  primaryAction;\n  /**\n   * Handles the lazy creation of the MatChip ripple.\n   * Used to improve initial load time of large applications.\n   */\n  _rippleLoader = inject(MatRippleLoader);\n  _injector = inject(Injector);\n  constructor() {\n    const styleLoader = inject(_CdkPrivateStyleLoader);\n    styleLoader.load(_StructuralStylesLoader);\n    styleLoader.load(_VisuallyHiddenLoader);\n    const animationMode = inject(ANIMATION_MODULE_TYPE, {\n      optional: true\n    });\n    this._animationsDisabled = animationMode === 'NoopAnimations';\n    this._monitorFocus();\n    this._rippleLoader?.configureRipple(this._elementRef.nativeElement, {\n      className: 'mat-mdc-chip-ripple',\n      disabled: this._isRippleDisabled()\n    });\n  }\n  ngOnInit() {\n    // This check needs to happen in `ngOnInit` so the overridden value of\n    // `basicChipAttrName` coming from base classes can be picked up.\n    const element = this._elementRef.nativeElement;\n    this._isBasicChip = element.hasAttribute(this.basicChipAttrName) || element.tagName.toLowerCase() === this.basicChipAttrName;\n  }\n  ngAfterViewInit() {\n    this._textElement = this._elementRef.nativeElement.querySelector('.mat-mdc-chip-action-label');\n    if (this._pendingFocus) {\n      this._pendingFocus = false;\n      this.focus();\n    }\n  }\n  ngAfterContentInit() {\n    // Since the styling depends on the presence of some\n    // actions, we have to mark for check on changes.\n    this._actionChanges = merge(this._allLeadingIcons.changes, this._allTrailingIcons.changes, this._allRemoveIcons.changes).subscribe(() => this._changeDetectorRef.markForCheck());\n  }\n  ngDoCheck() {\n    this._rippleLoader.setDisabled(this._elementRef.nativeElement, this._isRippleDisabled());\n  }\n  ngOnDestroy() {\n    this._focusMonitor.stopMonitoring(this._elementRef);\n    this._rippleLoader?.destroyRipple(this._elementRef.nativeElement);\n    this._actionChanges?.unsubscribe();\n    this.destroyed.emit({\n      chip: this\n    });\n    this.destroyed.complete();\n  }\n  /**\n   * Allows for programmatic removal of the chip.\n   *\n   * Informs any listeners of the removal request. Does not remove the chip from the DOM.\n   */\n  remove() {\n    if (this.removable) {\n      this.removed.emit({\n        chip: this\n      });\n    }\n  }\n  /** Whether or not the ripple should be disabled. */\n  _isRippleDisabled() {\n    return this.disabled || this.disableRipple || this._animationsDisabled || this._isBasicChip || !!this._globalRippleOptions?.disabled;\n  }\n  /** Returns whether the chip has a trailing icon. */\n  _hasTrailingIcon() {\n    return !!(this.trailingIcon || this.removeIcon);\n  }\n  /** Handles keyboard events on the chip. */\n  _handleKeydown(event) {\n    // Ignore backspace events where the user is holding down the key\n    // so that we don't accidentally remove too many chips.\n    if (event.keyCode === BACKSPACE && !event.repeat || event.keyCode === DELETE) {\n      event.preventDefault();\n      this.remove();\n    }\n  }\n  /** Allows for programmatic focusing of the chip. */\n  focus() {\n    if (!this.disabled) {\n      // If `focus` is called before `ngAfterViewInit`, we won't have access to the primary action.\n      // This can happen if the consumer tries to focus a chip immediately after it is added.\n      // Queue the method to be called again on init.\n      if (this.primaryAction) {\n        this.primaryAction.focus();\n      } else {\n        this._pendingFocus = true;\n      }\n    }\n  }\n  /** Gets the action that contains a specific target node. */\n  _getSourceAction(target) {\n    return this._getActions().find(action => {\n      const element = action._elementRef.nativeElement;\n      return element === target || element.contains(target);\n    });\n  }\n  /** Gets all of the actions within the chip. */\n  _getActions() {\n    const result = [];\n    if (this.primaryAction) {\n      result.push(this.primaryAction);\n    }\n    if (this.removeIcon) {\n      result.push(this.removeIcon);\n    }\n    if (this.trailingIcon) {\n      result.push(this.trailingIcon);\n    }\n    return result;\n  }\n  /** Handles interactions with the primary action of the chip. */\n  _handlePrimaryActionInteraction() {\n    // Empty here, but is overwritten in child classes.\n  }\n  /** Starts the focus monitoring process on the chip. */\n  _monitorFocus() {\n    this._focusMonitor.monitor(this._elementRef, true).subscribe(origin => {\n      const hasFocus = origin !== null;\n      if (hasFocus !== this._hasFocusInternal) {\n        this._hasFocusInternal = hasFocus;\n        if (hasFocus) {\n          this._onFocus.next({\n            chip: this\n          });\n        } else {\n          // When animations are enabled, Angular may end up removing the chip from the DOM a little\n          // earlier than usual, causing it to be blurred and throwing off the logic in the chip list\n          // that moves focus to the next item. To work around the issue, we defer marking the chip\n          // as not focused until after the next render.\n          this._changeDetectorRef.markForCheck();\n          setTimeout(() => this._ngZone.run(() => this._onBlur.next({\n            chip: this\n          })));\n        }\n      }\n    });\n  }\n  static ɵfac = function MatChip_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatChip)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatChip,\n    selectors: [[\"mat-basic-chip\"], [\"\", \"mat-basic-chip\", \"\"], [\"mat-chip\"], [\"\", \"mat-chip\", \"\"]],\n    contentQueries: function MatChip_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, MAT_CHIP_AVATAR, 5);\n        i0.ɵɵcontentQuery(dirIndex, MAT_CHIP_TRAILING_ICON, 5);\n        i0.ɵɵcontentQuery(dirIndex, MAT_CHIP_REMOVE, 5);\n        i0.ɵɵcontentQuery(dirIndex, MAT_CHIP_AVATAR, 5);\n        i0.ɵɵcontentQuery(dirIndex, MAT_CHIP_TRAILING_ICON, 5);\n        i0.ɵɵcontentQuery(dirIndex, MAT_CHIP_REMOVE, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.leadingIcon = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.trailingIcon = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.removeIcon = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._allLeadingIcons = _t);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._allTrailingIcons = _t);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._allRemoveIcons = _t);\n      }\n    },\n    viewQuery: function MatChip_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(MatChipAction, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.primaryAction = _t.first);\n      }\n    },\n    hostAttrs: [1, \"mat-mdc-chip\"],\n    hostVars: 31,\n    hostBindings: function MatChip_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"keydown\", function MatChip_keydown_HostBindingHandler($event) {\n          return ctx._handleKeydown($event);\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵhostProperty(\"id\", ctx.id);\n        i0.ɵɵattribute(\"role\", ctx.role)(\"aria-label\", ctx.ariaLabel);\n        i0.ɵɵclassMap(\"mat-\" + (ctx.color || \"primary\"));\n        i0.ɵɵclassProp(\"mdc-evolution-chip\", !ctx._isBasicChip)(\"mdc-evolution-chip--disabled\", ctx.disabled)(\"mdc-evolution-chip--with-trailing-action\", ctx._hasTrailingIcon())(\"mdc-evolution-chip--with-primary-graphic\", ctx.leadingIcon)(\"mdc-evolution-chip--with-primary-icon\", ctx.leadingIcon)(\"mdc-evolution-chip--with-avatar\", ctx.leadingIcon)(\"mat-mdc-chip-with-avatar\", ctx.leadingIcon)(\"mat-mdc-chip-highlighted\", ctx.highlighted)(\"mat-mdc-chip-disabled\", ctx.disabled)(\"mat-mdc-basic-chip\", ctx._isBasicChip)(\"mat-mdc-standard-chip\", !ctx._isBasicChip)(\"mat-mdc-chip-with-trailing-icon\", ctx._hasTrailingIcon())(\"_mat-animation-noopable\", ctx._animationsDisabled);\n      }\n    },\n    inputs: {\n      role: \"role\",\n      id: \"id\",\n      ariaLabel: [0, \"aria-label\", \"ariaLabel\"],\n      ariaDescription: [0, \"aria-description\", \"ariaDescription\"],\n      value: \"value\",\n      color: \"color\",\n      removable: [2, \"removable\", \"removable\", booleanAttribute],\n      highlighted: [2, \"highlighted\", \"highlighted\", booleanAttribute],\n      disableRipple: [2, \"disableRipple\", \"disableRipple\", booleanAttribute],\n      disabled: [2, \"disabled\", \"disabled\", booleanAttribute]\n    },\n    outputs: {\n      removed: \"removed\",\n      destroyed: \"destroyed\"\n    },\n    exportAs: [\"matChip\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MAT_CHIP,\n      useExisting: MatChip\n    }])],\n    ngContentSelectors: _c1,\n    decls: 8,\n    vars: 3,\n    consts: [[1, \"mat-mdc-chip-focus-overlay\"], [1, \"mdc-evolution-chip__cell\", \"mdc-evolution-chip__cell--primary\"], [\"matChipAction\", \"\", 3, \"isInteractive\"], [1, \"mdc-evolution-chip__graphic\", \"mat-mdc-chip-graphic\"], [1, \"mdc-evolution-chip__text-label\", \"mat-mdc-chip-action-label\"], [1, \"mat-mdc-chip-primary-focus-indicator\", \"mat-focus-indicator\"], [1, \"mdc-evolution-chip__cell\", \"mdc-evolution-chip__cell--trailing\"]],\n    template: function MatChip_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef(_c0);\n        i0.ɵɵelement(0, \"span\", 0);\n        i0.ɵɵelementStart(1, \"span\", 1)(2, \"span\", 2);\n        i0.ɵɵtemplate(3, MatChip_Conditional_3_Template, 2, 0, \"span\", 3);\n        i0.ɵɵelementStart(4, \"span\", 4);\n        i0.ɵɵprojection(5);\n        i0.ɵɵelement(6, \"span\", 5);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵtemplate(7, MatChip_Conditional_7_Template, 2, 0, \"span\", 6);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"isInteractive\", false);\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(ctx.leadingIcon ? 3 : -1);\n        i0.ɵɵadvance(4);\n        i0.ɵɵconditional(ctx._hasTrailingIcon() ? 7 : -1);\n      }\n    },\n    dependencies: [MatChipAction],\n    styles: [\".mdc-evolution-chip,.mdc-evolution-chip__cell,.mdc-evolution-chip__action{display:inline-flex;align-items:center}.mdc-evolution-chip{position:relative;max-width:100%}.mdc-evolution-chip__cell,.mdc-evolution-chip__action{height:100%}.mdc-evolution-chip__cell--primary{flex-basis:100%;overflow-x:hidden}.mdc-evolution-chip__cell--trailing{flex:1 0 auto}.mdc-evolution-chip__action{align-items:center;background:none;border:none;box-sizing:content-box;cursor:pointer;display:inline-flex;justify-content:center;outline:none;padding:0;text-decoration:none;color:inherit}.mdc-evolution-chip__action--presentational{cursor:auto}.mdc-evolution-chip--disabled,.mdc-evolution-chip__action:disabled{pointer-events:none}@media(forced-colors: active){.mdc-evolution-chip--disabled,.mdc-evolution-chip__action:disabled{forced-color-adjust:none}}.mdc-evolution-chip__action--primary{font:inherit;letter-spacing:inherit;white-space:inherit;overflow-x:hidden}.mat-mdc-standard-chip .mdc-evolution-chip__action--primary::before{border-width:var(--mdc-chip-outline-width, 1px);border-radius:var(--mdc-chip-container-shape-radius, 8px);box-sizing:border-box;content:\\\"\\\";height:100%;left:0;position:absolute;pointer-events:none;top:0;width:100%;z-index:1;border-style:solid}.mat-mdc-standard-chip .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:12px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__action--primary::before{border-color:var(--mdc-chip-outline-color, var(--mat-sys-outline))}.mdc-evolution-chip__action--primary:not(.mdc-evolution-chip__action--presentational):not(.mdc-ripple-upgraded):focus::before{border-color:var(--mdc-chip-focus-outline-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__action--primary::before{border-color:var(--mdc-chip-disabled-outline-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-standard-chip.mdc-evolution-chip--selected .mdc-evolution-chip__action--primary::before{border-width:var(--mdc-chip-flat-selected-outline-width, 0)}.mat-mdc-basic-chip .mdc-evolution-chip__action--primary{font:inherit}.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}.mdc-evolution-chip__action--trailing{position:relative;overflow:visible}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__action--trailing{color:var(--mdc-chip-with-trailing-icon-trailing-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__action--trailing{color:var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-color, var(--mat-sys-on-surface))}.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mdc-evolution-chip__text-label{-webkit-user-select:none;user-select:none;white-space:nowrap;text-overflow:ellipsis;overflow:hidden}.mat-mdc-standard-chip .mdc-evolution-chip__text-label{font-family:var(--mdc-chip-label-text-font, var(--mat-sys-label-large-font));line-height:var(--mdc-chip-label-text-line-height, var(--mat-sys-label-large-line-height));font-size:var(--mdc-chip-label-text-size, var(--mat-sys-label-large-size));font-weight:var(--mdc-chip-label-text-weight, var(--mat-sys-label-large-weight));letter-spacing:var(--mdc-chip-label-text-tracking, var(--mat-sys-label-large-tracking))}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__text-label{color:var(--mdc-chip-label-text-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--selected:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__text-label{color:var(--mdc-chip-selected-label-text-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__text-label,.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled .mdc-evolution-chip__text-label{color:var(--mdc-chip-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-evolution-chip__graphic{align-items:center;display:inline-flex;justify-content:center;overflow:hidden;pointer-events:none;position:relative;flex:1 0 auto}.mat-mdc-standard-chip .mdc-evolution-chip__graphic{width:var(--mdc-chip-with-avatar-avatar-size, 24px);height:var(--mdc-chip-with-avatar-avatar-size, 24px);font-size:var(--mdc-chip-with-avatar-avatar-size, 24px)}.mdc-evolution-chip--selecting .mdc-evolution-chip__graphic{transition:width 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--selectable:not(.mdc-evolution-chip--selected):not(.mdc-evolution-chip--with-primary-icon) .mdc-evolution-chip__graphic{width:0}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:6px;padding-right:6px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:4px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:8px;padding-right:4px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:6px;padding-right:6px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:4px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:8px;padding-right:4px}.mdc-evolution-chip__checkmark{position:absolute;opacity:0;top:50%;left:50%;height:20px;width:20px}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__checkmark{color:var(--mdc-chip-with-icon-selected-icon-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__checkmark{color:var(--mdc-chip-with-icon-disabled-icon-color, var(--mat-sys-on-surface))}.mdc-evolution-chip--selecting .mdc-evolution-chip__checkmark{transition:transform 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1);transform:translate(-75%, -50%)}.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark{transform:translate(-50%, -50%);opacity:1}.mdc-evolution-chip__checkmark-svg{display:block}.mdc-evolution-chip__checkmark-path{stroke-width:2px;stroke-dasharray:29.7833385;stroke-dashoffset:29.7833385;stroke:currentColor}.mdc-evolution-chip--selecting .mdc-evolution-chip__checkmark-path{transition:stroke-dashoffset 150ms 45ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark-path{stroke-dashoffset:0}@media(forced-colors: active){.mdc-evolution-chip__checkmark-path{stroke:CanvasText !important}}.mat-mdc-standard-chip .mdc-evolution-chip__icon--trailing{height:18px;width:18px;font-size:18px}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing.mat-mdc-chip-remove{opacity:calc(var(--mat-chip-trailing-action-opacity, 1)*var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38))}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing.mat-mdc-chip-remove:focus{opacity:calc(var(--mat-chip-trailing-action-focus-opacity, 1)*var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38))}.mat-mdc-standard-chip{border-radius:var(--mdc-chip-container-shape-radius, 8px);height:var(--mdc-chip-container-height, 32px)}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled){background-color:var(--mdc-chip-elevated-container-color, transparent)}.mat-mdc-standard-chip.mdc-evolution-chip--disabled{background-color:var(--mdc-chip-elevated-disabled-container-color)}.mat-mdc-standard-chip.mdc-evolution-chip--selected:not(.mdc-evolution-chip--disabled){background-color:var(--mdc-chip-elevated-selected-container-color, var(--mat-sys-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled{background-color:var(--mdc-chip-flat-disabled-selected-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}@media(forced-colors: active){.mat-mdc-standard-chip{outline:solid 1px}}.mat-mdc-standard-chip .mdc-evolution-chip__icon--primary{border-radius:var(--mdc-chip-with-avatar-avatar-shape-radius, 24px);width:var(--mdc-chip-with-icon-icon-size, 18px);height:var(--mdc-chip-with-icon-icon-size, 18px);font-size:var(--mdc-chip-with-icon-icon-size, 18px)}.mdc-evolution-chip--selected .mdc-evolution-chip__icon--primary{opacity:0}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__icon--primary{color:var(--mdc-chip-with-icon-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--primary{color:var(--mdc-chip-with-icon-disabled-icon-color, var(--mat-sys-on-surface))}.mat-mdc-chip-highlighted{--mdc-chip-with-icon-icon-color:var(--mdc-chip-with-icon-selected-icon-color, var(--mat-sys-on-secondary-container));--mdc-chip-elevated-container-color:var(--mdc-chip-elevated-selected-container-color, var(--mat-sys-secondary-container));--mdc-chip-label-text-color:var(--mdc-chip-selected-label-text-color, var(--mat-sys-on-secondary-container));--mdc-chip-outline-width:var(--mdc-chip-flat-selected-outline-width, 0)}.mat-mdc-chip-focus-overlay{background:var(--mdc-chip-focus-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-chip-selected .mat-mdc-chip-focus-overlay,.mat-mdc-chip-highlighted .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-selected-focus-state-layer-color, var(--mat-sys-on-secondary-container))}.mat-mdc-chip:hover .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-hover-state-layer-color, var(--mat-sys-on-surface-variant));opacity:var(--mdc-chip-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip-focus-overlay .mat-mdc-chip-selected:hover,.mat-mdc-chip-highlighted:hover .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-selected-hover-state-layer-color, var(--mat-sys-on-secondary-container));opacity:var(--mdc-chip-selected-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip.cdk-focused .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-focus-state-layer-color, var(--mat-sys-on-surface-variant));opacity:var(--mdc-chip-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-chip-selected.cdk-focused .mat-mdc-chip-focus-overlay,.mat-mdc-chip-highlighted.cdk-focused .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-selected-focus-state-layer-color, var(--mat-sys-on-secondary-container));opacity:var(--mdc-chip-selected-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mdc-evolution-chip--disabled:not(.mdc-evolution-chip--selected) .mat-mdc-chip-avatar{opacity:var(--mdc-chip-with-avatar-disabled-avatar-opacity, 0.38)}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing{opacity:var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38)}.mdc-evolution-chip--disabled.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark{opacity:var(--mdc-chip-with-icon-disabled-icon-opacity, 0.38)}.mat-mdc-standard-chip.mdc-evolution-chip--disabled{opacity:var(--mat-chip-disabled-container-opacity, 1)}.mat-mdc-standard-chip.mdc-evolution-chip--selected .mdc-evolution-chip__icon--trailing,.mat-mdc-standard-chip.mat-mdc-chip-highlighted .mdc-evolution-chip__icon--trailing{color:var(--mat-chip-selected-trailing-icon-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing,.mat-mdc-standard-chip.mat-mdc-chip-highlighted.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing{color:var(--mat-chip-selected-disabled-trailing-icon-color, var(--mat-sys-on-surface))}.mat-mdc-chip-remove{opacity:var(--mat-chip-trailing-action-opacity, 1)}.mat-mdc-chip-remove:focus{opacity:var(--mat-chip-trailing-action-focus-opacity, 1)}.mat-mdc-chip-remove::after{background-color:var(--mat-chip-trailing-action-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-chip-remove:hover::after{opacity:var(--mat-chip-trailing-action-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip-remove:focus::after{opacity:var(--mat-chip-trailing-action-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-chip-selected .mat-mdc-chip-remove::after,.mat-mdc-chip-highlighted .mat-mdc-chip-remove::after{background-color:var(--mat-chip-selected-trailing-action-state-layer-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-standard-chip .mdc-evolution-chip__cell--primary,.mat-mdc-standard-chip .mdc-evolution-chip__action--primary,.mat-mdc-standard-chip .mat-mdc-chip-action-label{overflow:visible}.mat-mdc-standard-chip .mat-mdc-chip-graphic,.mat-mdc-standard-chip .mat-mdc-chip-trailing-icon{box-sizing:content-box}.mat-mdc-standard-chip._mat-animation-noopable,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__graphic,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__checkmark,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__checkmark-path{transition-duration:1ms;animation-duration:1ms}.mat-mdc-chip-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;opacity:0;border-radius:inherit;transition:opacity 150ms linear}._mat-animation-noopable .mat-mdc-chip-focus-overlay{transition:none}.mat-mdc-basic-chip .mat-mdc-chip-focus-overlay{display:none}.mat-mdc-chip .mat-ripple.mat-mdc-chip-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-chip-avatar{text-align:center;line-height:1;color:var(--mdc-chip-with-icon-icon-color, currentColor)}.mat-mdc-chip{position:relative;z-index:0}.mat-mdc-chip-action-label{text-align:left;z-index:1}[dir=rtl] .mat-mdc-chip-action-label{text-align:right}.mat-mdc-chip.mdc-evolution-chip--with-trailing-action .mat-mdc-chip-action-label{position:relative}.mat-mdc-chip-action-label .mat-mdc-chip-primary-focus-indicator{position:absolute;top:0;right:0;bottom:0;left:0;pointer-events:none}.mat-mdc-chip-action-label .mat-focus-indicator::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-mdc-chip-remove::before{margin:calc(var(--mat-focus-indicator-border-width, 3px)*-1);left:8px;right:8px}.mat-mdc-chip-remove::after{content:\\\"\\\";display:block;opacity:0;position:absolute;top:-3px;bottom:-3px;left:5px;right:5px;border-radius:50%;box-sizing:border-box;padding:12px;margin:-12px;background-clip:content-box}.mat-mdc-chip-remove .mat-icon{width:18px;height:18px;font-size:18px;box-sizing:content-box}.mat-chip-edit-input{cursor:text;display:inline-block;color:inherit;outline:0}@media(forced-colors: active){.mat-mdc-chip-selected:not(.mat-mdc-chip-multiple){outline-width:3px}}.mat-mdc-chip-action:focus .mat-focus-indicator::before{content:\\\"\\\"}.mdc-evolution-chip__icon,.mat-mdc-chip-remove .mat-icon{min-height:fit-content}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatChip, [{\n    type: Component,\n    args: [{\n      selector: 'mat-basic-chip, [mat-basic-chip], mat-chip, [mat-chip]',\n      exportAs: 'matChip',\n      host: {\n        'class': 'mat-mdc-chip',\n        '[class]': '\"mat-\" + (color || \"primary\")',\n        '[class.mdc-evolution-chip]': '!_isBasicChip',\n        '[class.mdc-evolution-chip--disabled]': 'disabled',\n        '[class.mdc-evolution-chip--with-trailing-action]': '_hasTrailingIcon()',\n        '[class.mdc-evolution-chip--with-primary-graphic]': 'leadingIcon',\n        '[class.mdc-evolution-chip--with-primary-icon]': 'leadingIcon',\n        '[class.mdc-evolution-chip--with-avatar]': 'leadingIcon',\n        '[class.mat-mdc-chip-with-avatar]': 'leadingIcon',\n        '[class.mat-mdc-chip-highlighted]': 'highlighted',\n        '[class.mat-mdc-chip-disabled]': 'disabled',\n        '[class.mat-mdc-basic-chip]': '_isBasicChip',\n        '[class.mat-mdc-standard-chip]': '!_isBasicChip',\n        '[class.mat-mdc-chip-with-trailing-icon]': '_hasTrailingIcon()',\n        '[class._mat-animation-noopable]': '_animationsDisabled',\n        '[id]': 'id',\n        '[attr.role]': 'role',\n        '[attr.aria-label]': 'ariaLabel',\n        '(keydown)': '_handleKeydown($event)'\n      },\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [{\n        provide: MAT_CHIP,\n        useExisting: MatChip\n      }],\n      imports: [MatChipAction],\n      template: \"<span class=\\\"mat-mdc-chip-focus-overlay\\\"></span>\\n\\n<span class=\\\"mdc-evolution-chip__cell mdc-evolution-chip__cell--primary\\\">\\n  <span matChipAction [isInteractive]=\\\"false\\\">\\n    @if (leadingIcon) {\\n      <span class=\\\"mdc-evolution-chip__graphic mat-mdc-chip-graphic\\\">\\n        <ng-content select=\\\"mat-chip-avatar, [matChipAvatar]\\\"></ng-content>\\n      </span>\\n    }\\n    <span class=\\\"mdc-evolution-chip__text-label mat-mdc-chip-action-label\\\">\\n      <ng-content></ng-content>\\n      <span class=\\\"mat-mdc-chip-primary-focus-indicator mat-focus-indicator\\\"></span>\\n    </span>\\n  </span>\\n</span>\\n\\n@if (_hasTrailingIcon()) {\\n  <span class=\\\"mdc-evolution-chip__cell mdc-evolution-chip__cell--trailing\\\">\\n    <ng-content select=\\\"mat-chip-trailing-icon,[matChipRemove],[matChipTrailingIcon]\\\"></ng-content>\\n  </span>\\n}\\n\",\n      styles: [\".mdc-evolution-chip,.mdc-evolution-chip__cell,.mdc-evolution-chip__action{display:inline-flex;align-items:center}.mdc-evolution-chip{position:relative;max-width:100%}.mdc-evolution-chip__cell,.mdc-evolution-chip__action{height:100%}.mdc-evolution-chip__cell--primary{flex-basis:100%;overflow-x:hidden}.mdc-evolution-chip__cell--trailing{flex:1 0 auto}.mdc-evolution-chip__action{align-items:center;background:none;border:none;box-sizing:content-box;cursor:pointer;display:inline-flex;justify-content:center;outline:none;padding:0;text-decoration:none;color:inherit}.mdc-evolution-chip__action--presentational{cursor:auto}.mdc-evolution-chip--disabled,.mdc-evolution-chip__action:disabled{pointer-events:none}@media(forced-colors: active){.mdc-evolution-chip--disabled,.mdc-evolution-chip__action:disabled{forced-color-adjust:none}}.mdc-evolution-chip__action--primary{font:inherit;letter-spacing:inherit;white-space:inherit;overflow-x:hidden}.mat-mdc-standard-chip .mdc-evolution-chip__action--primary::before{border-width:var(--mdc-chip-outline-width, 1px);border-radius:var(--mdc-chip-container-shape-radius, 8px);box-sizing:border-box;content:\\\"\\\";height:100%;left:0;position:absolute;pointer-events:none;top:0;width:100%;z-index:1;border-style:solid}.mat-mdc-standard-chip .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:12px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__action--primary::before{border-color:var(--mdc-chip-outline-color, var(--mat-sys-outline))}.mdc-evolution-chip__action--primary:not(.mdc-evolution-chip__action--presentational):not(.mdc-ripple-upgraded):focus::before{border-color:var(--mdc-chip-focus-outline-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__action--primary::before{border-color:var(--mdc-chip-disabled-outline-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-standard-chip.mdc-evolution-chip--selected .mdc-evolution-chip__action--primary::before{border-width:var(--mdc-chip-flat-selected-outline-width, 0)}.mat-mdc-basic-chip .mdc-evolution-chip__action--primary{font:inherit}.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}.mdc-evolution-chip__action--trailing{position:relative;overflow:visible}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__action--trailing{color:var(--mdc-chip-with-trailing-icon-trailing-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__action--trailing{color:var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-color, var(--mat-sys-on-surface))}.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mdc-evolution-chip__text-label{-webkit-user-select:none;user-select:none;white-space:nowrap;text-overflow:ellipsis;overflow:hidden}.mat-mdc-standard-chip .mdc-evolution-chip__text-label{font-family:var(--mdc-chip-label-text-font, var(--mat-sys-label-large-font));line-height:var(--mdc-chip-label-text-line-height, var(--mat-sys-label-large-line-height));font-size:var(--mdc-chip-label-text-size, var(--mat-sys-label-large-size));font-weight:var(--mdc-chip-label-text-weight, var(--mat-sys-label-large-weight));letter-spacing:var(--mdc-chip-label-text-tracking, var(--mat-sys-label-large-tracking))}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__text-label{color:var(--mdc-chip-label-text-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--selected:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__text-label{color:var(--mdc-chip-selected-label-text-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__text-label,.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled .mdc-evolution-chip__text-label{color:var(--mdc-chip-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-evolution-chip__graphic{align-items:center;display:inline-flex;justify-content:center;overflow:hidden;pointer-events:none;position:relative;flex:1 0 auto}.mat-mdc-standard-chip .mdc-evolution-chip__graphic{width:var(--mdc-chip-with-avatar-avatar-size, 24px);height:var(--mdc-chip-with-avatar-avatar-size, 24px);font-size:var(--mdc-chip-with-avatar-avatar-size, 24px)}.mdc-evolution-chip--selecting .mdc-evolution-chip__graphic{transition:width 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--selectable:not(.mdc-evolution-chip--selected):not(.mdc-evolution-chip--with-primary-icon) .mdc-evolution-chip__graphic{width:0}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:6px;padding-right:6px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:4px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:8px;padding-right:4px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:6px;padding-right:6px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:4px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:8px;padding-right:4px}.mdc-evolution-chip__checkmark{position:absolute;opacity:0;top:50%;left:50%;height:20px;width:20px}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__checkmark{color:var(--mdc-chip-with-icon-selected-icon-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__checkmark{color:var(--mdc-chip-with-icon-disabled-icon-color, var(--mat-sys-on-surface))}.mdc-evolution-chip--selecting .mdc-evolution-chip__checkmark{transition:transform 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1);transform:translate(-75%, -50%)}.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark{transform:translate(-50%, -50%);opacity:1}.mdc-evolution-chip__checkmark-svg{display:block}.mdc-evolution-chip__checkmark-path{stroke-width:2px;stroke-dasharray:29.7833385;stroke-dashoffset:29.7833385;stroke:currentColor}.mdc-evolution-chip--selecting .mdc-evolution-chip__checkmark-path{transition:stroke-dashoffset 150ms 45ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark-path{stroke-dashoffset:0}@media(forced-colors: active){.mdc-evolution-chip__checkmark-path{stroke:CanvasText !important}}.mat-mdc-standard-chip .mdc-evolution-chip__icon--trailing{height:18px;width:18px;font-size:18px}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing.mat-mdc-chip-remove{opacity:calc(var(--mat-chip-trailing-action-opacity, 1)*var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38))}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing.mat-mdc-chip-remove:focus{opacity:calc(var(--mat-chip-trailing-action-focus-opacity, 1)*var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38))}.mat-mdc-standard-chip{border-radius:var(--mdc-chip-container-shape-radius, 8px);height:var(--mdc-chip-container-height, 32px)}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled){background-color:var(--mdc-chip-elevated-container-color, transparent)}.mat-mdc-standard-chip.mdc-evolution-chip--disabled{background-color:var(--mdc-chip-elevated-disabled-container-color)}.mat-mdc-standard-chip.mdc-evolution-chip--selected:not(.mdc-evolution-chip--disabled){background-color:var(--mdc-chip-elevated-selected-container-color, var(--mat-sys-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled{background-color:var(--mdc-chip-flat-disabled-selected-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}@media(forced-colors: active){.mat-mdc-standard-chip{outline:solid 1px}}.mat-mdc-standard-chip .mdc-evolution-chip__icon--primary{border-radius:var(--mdc-chip-with-avatar-avatar-shape-radius, 24px);width:var(--mdc-chip-with-icon-icon-size, 18px);height:var(--mdc-chip-with-icon-icon-size, 18px);font-size:var(--mdc-chip-with-icon-icon-size, 18px)}.mdc-evolution-chip--selected .mdc-evolution-chip__icon--primary{opacity:0}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__icon--primary{color:var(--mdc-chip-with-icon-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--primary{color:var(--mdc-chip-with-icon-disabled-icon-color, var(--mat-sys-on-surface))}.mat-mdc-chip-highlighted{--mdc-chip-with-icon-icon-color:var(--mdc-chip-with-icon-selected-icon-color, var(--mat-sys-on-secondary-container));--mdc-chip-elevated-container-color:var(--mdc-chip-elevated-selected-container-color, var(--mat-sys-secondary-container));--mdc-chip-label-text-color:var(--mdc-chip-selected-label-text-color, var(--mat-sys-on-secondary-container));--mdc-chip-outline-width:var(--mdc-chip-flat-selected-outline-width, 0)}.mat-mdc-chip-focus-overlay{background:var(--mdc-chip-focus-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-chip-selected .mat-mdc-chip-focus-overlay,.mat-mdc-chip-highlighted .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-selected-focus-state-layer-color, var(--mat-sys-on-secondary-container))}.mat-mdc-chip:hover .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-hover-state-layer-color, var(--mat-sys-on-surface-variant));opacity:var(--mdc-chip-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip-focus-overlay .mat-mdc-chip-selected:hover,.mat-mdc-chip-highlighted:hover .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-selected-hover-state-layer-color, var(--mat-sys-on-secondary-container));opacity:var(--mdc-chip-selected-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip.cdk-focused .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-focus-state-layer-color, var(--mat-sys-on-surface-variant));opacity:var(--mdc-chip-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-chip-selected.cdk-focused .mat-mdc-chip-focus-overlay,.mat-mdc-chip-highlighted.cdk-focused .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-selected-focus-state-layer-color, var(--mat-sys-on-secondary-container));opacity:var(--mdc-chip-selected-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mdc-evolution-chip--disabled:not(.mdc-evolution-chip--selected) .mat-mdc-chip-avatar{opacity:var(--mdc-chip-with-avatar-disabled-avatar-opacity, 0.38)}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing{opacity:var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38)}.mdc-evolution-chip--disabled.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark{opacity:var(--mdc-chip-with-icon-disabled-icon-opacity, 0.38)}.mat-mdc-standard-chip.mdc-evolution-chip--disabled{opacity:var(--mat-chip-disabled-container-opacity, 1)}.mat-mdc-standard-chip.mdc-evolution-chip--selected .mdc-evolution-chip__icon--trailing,.mat-mdc-standard-chip.mat-mdc-chip-highlighted .mdc-evolution-chip__icon--trailing{color:var(--mat-chip-selected-trailing-icon-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing,.mat-mdc-standard-chip.mat-mdc-chip-highlighted.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing{color:var(--mat-chip-selected-disabled-trailing-icon-color, var(--mat-sys-on-surface))}.mat-mdc-chip-remove{opacity:var(--mat-chip-trailing-action-opacity, 1)}.mat-mdc-chip-remove:focus{opacity:var(--mat-chip-trailing-action-focus-opacity, 1)}.mat-mdc-chip-remove::after{background-color:var(--mat-chip-trailing-action-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-chip-remove:hover::after{opacity:var(--mat-chip-trailing-action-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip-remove:focus::after{opacity:var(--mat-chip-trailing-action-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-chip-selected .mat-mdc-chip-remove::after,.mat-mdc-chip-highlighted .mat-mdc-chip-remove::after{background-color:var(--mat-chip-selected-trailing-action-state-layer-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-standard-chip .mdc-evolution-chip__cell--primary,.mat-mdc-standard-chip .mdc-evolution-chip__action--primary,.mat-mdc-standard-chip .mat-mdc-chip-action-label{overflow:visible}.mat-mdc-standard-chip .mat-mdc-chip-graphic,.mat-mdc-standard-chip .mat-mdc-chip-trailing-icon{box-sizing:content-box}.mat-mdc-standard-chip._mat-animation-noopable,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__graphic,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__checkmark,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__checkmark-path{transition-duration:1ms;animation-duration:1ms}.mat-mdc-chip-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;opacity:0;border-radius:inherit;transition:opacity 150ms linear}._mat-animation-noopable .mat-mdc-chip-focus-overlay{transition:none}.mat-mdc-basic-chip .mat-mdc-chip-focus-overlay{display:none}.mat-mdc-chip .mat-ripple.mat-mdc-chip-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-chip-avatar{text-align:center;line-height:1;color:var(--mdc-chip-with-icon-icon-color, currentColor)}.mat-mdc-chip{position:relative;z-index:0}.mat-mdc-chip-action-label{text-align:left;z-index:1}[dir=rtl] .mat-mdc-chip-action-label{text-align:right}.mat-mdc-chip.mdc-evolution-chip--with-trailing-action .mat-mdc-chip-action-label{position:relative}.mat-mdc-chip-action-label .mat-mdc-chip-primary-focus-indicator{position:absolute;top:0;right:0;bottom:0;left:0;pointer-events:none}.mat-mdc-chip-action-label .mat-focus-indicator::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-mdc-chip-remove::before{margin:calc(var(--mat-focus-indicator-border-width, 3px)*-1);left:8px;right:8px}.mat-mdc-chip-remove::after{content:\\\"\\\";display:block;opacity:0;position:absolute;top:-3px;bottom:-3px;left:5px;right:5px;border-radius:50%;box-sizing:border-box;padding:12px;margin:-12px;background-clip:content-box}.mat-mdc-chip-remove .mat-icon{width:18px;height:18px;font-size:18px;box-sizing:content-box}.mat-chip-edit-input{cursor:text;display:inline-block;color:inherit;outline:0}@media(forced-colors: active){.mat-mdc-chip-selected:not(.mat-mdc-chip-multiple){outline-width:3px}}.mat-mdc-chip-action:focus .mat-focus-indicator::before{content:\\\"\\\"}.mdc-evolution-chip__icon,.mat-mdc-chip-remove .mat-icon{min-height:fit-content}\\n\"]\n    }]\n  }], () => [], {\n    role: [{\n      type: Input\n    }],\n    _allLeadingIcons: [{\n      type: ContentChildren,\n      args: [MAT_CHIP_AVATAR, {\n        descendants: true\n      }]\n    }],\n    _allTrailingIcons: [{\n      type: ContentChildren,\n      args: [MAT_CHIP_TRAILING_ICON, {\n        descendants: true\n      }]\n    }],\n    _allRemoveIcons: [{\n      type: ContentChildren,\n      args: [MAT_CHIP_REMOVE, {\n        descendants: true\n      }]\n    }],\n    id: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input,\n      args: ['aria-label']\n    }],\n    ariaDescription: [{\n      type: Input,\n      args: ['aria-description']\n    }],\n    value: [{\n      type: Input\n    }],\n    color: [{\n      type: Input\n    }],\n    removable: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    highlighted: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    disableRipple: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    removed: [{\n      type: Output\n    }],\n    destroyed: [{\n      type: Output\n    }],\n    leadingIcon: [{\n      type: ContentChild,\n      args: [MAT_CHIP_AVATAR]\n    }],\n    trailingIcon: [{\n      type: ContentChild,\n      args: [MAT_CHIP_TRAILING_ICON]\n    }],\n    removeIcon: [{\n      type: ContentChild,\n      args: [MAT_CHIP_REMOVE]\n    }],\n    primaryAction: [{\n      type: ViewChild,\n      args: [MatChipAction]\n    }]\n  });\n})();\n\n/** Event object emitted by MatChipOption when selected or deselected. */\nclass MatChipSelectionChange {\n  source;\n  selected;\n  isUserInput;\n  constructor(/** Reference to the chip that emitted the event. */\n  source, /** Whether the chip that emitted the event is selected. */\n  selected, /** Whether the selection change was a result of a user interaction. */\n  isUserInput = false) {\n    this.source = source;\n    this.selected = selected;\n    this.isUserInput = isUserInput;\n  }\n}\n/**\n * An extension of the MatChip component that supports chip selection. Used with MatChipListbox.\n *\n * Unlike other chips, the user can focus on disabled chip options inside a MatChipListbox. The\n * user cannot click disabled chips.\n */\nclass MatChipOption extends MatChip {\n  /** Default chip options. */\n  _defaultOptions = inject(MAT_CHIPS_DEFAULT_OPTIONS, {\n    optional: true\n  });\n  /** Whether the chip list is selectable. */\n  chipListSelectable = true;\n  /** Whether the chip list is in multi-selection mode. */\n  _chipListMultiple = false;\n  /** Whether the chip list hides single-selection indicator. */\n  _chipListHideSingleSelectionIndicator = this._defaultOptions?.hideSingleSelectionIndicator ?? false;\n  /**\n   * Whether or not the chip is selectable.\n   *\n   * When a chip is not selectable, changes to its selected state are always\n   * ignored. By default an option chip is selectable, and it becomes\n   * non-selectable if its parent chip list is not selectable.\n   */\n  get selectable() {\n    return this._selectable && this.chipListSelectable;\n  }\n  set selectable(value) {\n    this._selectable = value;\n    this._changeDetectorRef.markForCheck();\n  }\n  _selectable = true;\n  /** Whether the chip is selected. */\n  get selected() {\n    return this._selected;\n  }\n  set selected(value) {\n    this._setSelectedState(value, false, true);\n  }\n  _selected = false;\n  /**\n   * The ARIA selected applied to the chip. Conforms to WAI ARIA best practices for listbox\n   * interaction patterns.\n   *\n   * From [WAI ARIA Listbox authoring practices guide](\n   * https://www.w3.org/WAI/ARIA/apg/patterns/listbox/):\n   *  \"If any options are selected, each selected option has either aria-selected or aria-checked\n   *  set to true. All options that are selectable but not selected have either aria-selected or\n   *  aria-checked set to false.\"\n   *\n   * Set `aria-selected=\"false\"` on not-selected listbox options that are selectable to fix\n   * VoiceOver reading every option as \"selected\" (#25736).\n   */\n  get ariaSelected() {\n    return this.selectable ? this.selected.toString() : null;\n  }\n  /** The unstyled chip selector for this component. */\n  basicChipAttrName = 'mat-basic-chip-option';\n  /** Emitted when the chip is selected or deselected. */\n  selectionChange = new EventEmitter();\n  ngOnInit() {\n    super.ngOnInit();\n    this.role = 'presentation';\n  }\n  /** Selects the chip. */\n  select() {\n    this._setSelectedState(true, false, true);\n  }\n  /** Deselects the chip. */\n  deselect() {\n    this._setSelectedState(false, false, true);\n  }\n  /** Selects this chip and emits userInputSelection event */\n  selectViaInteraction() {\n    this._setSelectedState(true, true, true);\n  }\n  /** Toggles the current selected state of this chip. */\n  toggleSelected(isUserInput = false) {\n    this._setSelectedState(!this.selected, isUserInput, true);\n    return this.selected;\n  }\n  _handlePrimaryActionInteraction() {\n    if (!this.disabled) {\n      // Interacting with the primary action implies that the chip already has focus, however\n      // there's a bug in Safari where focus ends up lingering on the previous chip (see #27544).\n      // We work around it by explicitly focusing the primary action of the current chip.\n      this.focus();\n      if (this.selectable) {\n        this.toggleSelected(true);\n      }\n    }\n  }\n  _hasLeadingGraphic() {\n    if (this.leadingIcon) {\n      return true;\n    }\n    // The checkmark graphic communicates selected state for both single-select and multi-select.\n    // Include checkmark in single-select to fix a11y issue where selected state is communicated\n    // visually only using color (#25886).\n    return !this._chipListHideSingleSelectionIndicator || this._chipListMultiple;\n  }\n  _setSelectedState(isSelected, isUserInput, emitEvent) {\n    if (isSelected !== this.selected) {\n      this._selected = isSelected;\n      if (emitEvent) {\n        this.selectionChange.emit({\n          source: this,\n          isUserInput,\n          selected: this.selected\n        });\n      }\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMatChipOption_BaseFactory;\n    return function MatChipOption_Factory(__ngFactoryType__) {\n      return (ɵMatChipOption_BaseFactory || (ɵMatChipOption_BaseFactory = i0.ɵɵgetInheritedFactory(MatChipOption)))(__ngFactoryType__ || MatChipOption);\n    };\n  })();\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatChipOption,\n    selectors: [[\"mat-basic-chip-option\"], [\"\", \"mat-basic-chip-option\", \"\"], [\"mat-chip-option\"], [\"\", \"mat-chip-option\", \"\"]],\n    hostAttrs: [1, \"mat-mdc-chip\", \"mat-mdc-chip-option\"],\n    hostVars: 37,\n    hostBindings: function MatChipOption_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵhostProperty(\"id\", ctx.id);\n        i0.ɵɵattribute(\"tabindex\", null)(\"aria-label\", null)(\"aria-description\", null)(\"role\", ctx.role);\n        i0.ɵɵclassProp(\"mdc-evolution-chip\", !ctx._isBasicChip)(\"mdc-evolution-chip--filter\", !ctx._isBasicChip)(\"mdc-evolution-chip--selectable\", !ctx._isBasicChip)(\"mat-mdc-chip-selected\", ctx.selected)(\"mat-mdc-chip-multiple\", ctx._chipListMultiple)(\"mat-mdc-chip-disabled\", ctx.disabled)(\"mat-mdc-chip-with-avatar\", ctx.leadingIcon)(\"mdc-evolution-chip--disabled\", ctx.disabled)(\"mdc-evolution-chip--selected\", ctx.selected)(\"mdc-evolution-chip--selecting\", !ctx._animationsDisabled)(\"mdc-evolution-chip--with-trailing-action\", ctx._hasTrailingIcon())(\"mdc-evolution-chip--with-primary-icon\", ctx.leadingIcon)(\"mdc-evolution-chip--with-primary-graphic\", ctx._hasLeadingGraphic())(\"mdc-evolution-chip--with-avatar\", ctx.leadingIcon)(\"mat-mdc-chip-highlighted\", ctx.highlighted)(\"mat-mdc-chip-with-trailing-icon\", ctx._hasTrailingIcon());\n      }\n    },\n    inputs: {\n      selectable: [2, \"selectable\", \"selectable\", booleanAttribute],\n      selected: [2, \"selected\", \"selected\", booleanAttribute]\n    },\n    outputs: {\n      selectionChange: \"selectionChange\"\n    },\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MatChip,\n      useExisting: MatChipOption\n    }, {\n      provide: MAT_CHIP,\n      useExisting: MatChipOption\n    }]), i0.ɵɵInheritDefinitionFeature],\n    ngContentSelectors: _c1,\n    decls: 10,\n    vars: 8,\n    consts: [[1, \"mat-mdc-chip-focus-overlay\"], [1, \"mdc-evolution-chip__cell\", \"mdc-evolution-chip__cell--primary\"], [\"matChipAction\", \"\", \"role\", \"option\", 3, \"_allowFocusWhenDisabled\"], [1, \"mdc-evolution-chip__graphic\", \"mat-mdc-chip-graphic\"], [1, \"mdc-evolution-chip__text-label\", \"mat-mdc-chip-action-label\"], [1, \"mat-mdc-chip-primary-focus-indicator\", \"mat-focus-indicator\"], [1, \"mdc-evolution-chip__cell\", \"mdc-evolution-chip__cell--trailing\"], [1, \"cdk-visually-hidden\", 3, \"id\"], [1, \"mdc-evolution-chip__checkmark\"], [\"viewBox\", \"-2 -3 30 30\", \"focusable\", \"false\", \"aria-hidden\", \"true\", 1, \"mdc-evolution-chip__checkmark-svg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"d\", \"M1.73,12.91 8.1,19.28 22.79,4.59\", 1, \"mdc-evolution-chip__checkmark-path\"]],\n    template: function MatChipOption_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef(_c0);\n        i0.ɵɵelement(0, \"span\", 0);\n        i0.ɵɵelementStart(1, \"span\", 1)(2, \"button\", 2);\n        i0.ɵɵtemplate(3, MatChipOption_Conditional_3_Template, 5, 0, \"span\", 3);\n        i0.ɵɵelementStart(4, \"span\", 4);\n        i0.ɵɵprojection(5);\n        i0.ɵɵelement(6, \"span\", 5);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵtemplate(7, MatChipOption_Conditional_7_Template, 2, 0, \"span\", 6);\n        i0.ɵɵelementStart(8, \"span\", 7);\n        i0.ɵɵtext(9);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"_allowFocusWhenDisabled\", true);\n        i0.ɵɵattribute(\"aria-selected\", ctx.ariaSelected)(\"aria-label\", ctx.ariaLabel)(\"aria-describedby\", ctx._ariaDescriptionId);\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(ctx._hasLeadingGraphic() ? 3 : -1);\n        i0.ɵɵadvance(4);\n        i0.ɵɵconditional(ctx._hasTrailingIcon() ? 7 : -1);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"id\", ctx._ariaDescriptionId);\n        i0.ɵɵadvance();\n        i0.ɵɵtextInterpolate(ctx.ariaDescription);\n      }\n    },\n    dependencies: [MatChipAction],\n    styles: [_c2],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatChipOption, [{\n    type: Component,\n    args: [{\n      selector: 'mat-basic-chip-option, [mat-basic-chip-option], mat-chip-option, [mat-chip-option]',\n      host: {\n        'class': 'mat-mdc-chip mat-mdc-chip-option',\n        '[class.mdc-evolution-chip]': '!_isBasicChip',\n        '[class.mdc-evolution-chip--filter]': '!_isBasicChip',\n        '[class.mdc-evolution-chip--selectable]': '!_isBasicChip',\n        '[class.mat-mdc-chip-selected]': 'selected',\n        '[class.mat-mdc-chip-multiple]': '_chipListMultiple',\n        '[class.mat-mdc-chip-disabled]': 'disabled',\n        '[class.mat-mdc-chip-with-avatar]': 'leadingIcon',\n        '[class.mdc-evolution-chip--disabled]': 'disabled',\n        '[class.mdc-evolution-chip--selected]': 'selected',\n        // This class enables the transition on the checkmark. Usually MDC adds it when selection\n        // starts and removes it once the animation is finished. We don't need to go through all\n        // the trouble, because we only care about the selection animation. MDC needs to do it,\n        // because they also have an exit animation that we don't care about.\n        '[class.mdc-evolution-chip--selecting]': '!_animationsDisabled',\n        '[class.mdc-evolution-chip--with-trailing-action]': '_hasTrailingIcon()',\n        '[class.mdc-evolution-chip--with-primary-icon]': 'leadingIcon',\n        '[class.mdc-evolution-chip--with-primary-graphic]': '_hasLeadingGraphic()',\n        '[class.mdc-evolution-chip--with-avatar]': 'leadingIcon',\n        '[class.mat-mdc-chip-highlighted]': 'highlighted',\n        '[class.mat-mdc-chip-with-trailing-icon]': '_hasTrailingIcon()',\n        '[attr.tabindex]': 'null',\n        '[attr.aria-label]': 'null',\n        '[attr.aria-description]': 'null',\n        '[attr.role]': 'role',\n        '[id]': 'id'\n      },\n      providers: [{\n        provide: MatChip,\n        useExisting: MatChipOption\n      }, {\n        provide: MAT_CHIP,\n        useExisting: MatChipOption\n      }],\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      imports: [MatChipAction],\n      template: \"<span class=\\\"mat-mdc-chip-focus-overlay\\\"></span>\\n\\n<span class=\\\"mdc-evolution-chip__cell mdc-evolution-chip__cell--primary\\\">\\n  <button\\n    matChipAction\\n    [_allowFocusWhenDisabled]=\\\"true\\\"\\n    [attr.aria-selected]=\\\"ariaSelected\\\"\\n    [attr.aria-label]=\\\"ariaLabel\\\"\\n    [attr.aria-describedby]=\\\"_ariaDescriptionId\\\"\\n    role=\\\"option\\\">\\n    @if (_hasLeadingGraphic()) {\\n      <span class=\\\"mdc-evolution-chip__graphic mat-mdc-chip-graphic\\\">\\n        <ng-content select=\\\"mat-chip-avatar, [matChipAvatar]\\\"></ng-content>\\n        <span class=\\\"mdc-evolution-chip__checkmark\\\">\\n          <svg\\n            class=\\\"mdc-evolution-chip__checkmark-svg\\\"\\n            viewBox=\\\"-2 -3 30 30\\\"\\n            focusable=\\\"false\\\"\\n            aria-hidden=\\\"true\\\">\\n            <path class=\\\"mdc-evolution-chip__checkmark-path\\\"\\n                  fill=\\\"none\\\" stroke=\\\"currentColor\\\" d=\\\"M1.73,12.91 8.1,19.28 22.79,4.59\\\" />\\n          </svg>\\n        </span>\\n      </span>\\n    }\\n    <span class=\\\"mdc-evolution-chip__text-label mat-mdc-chip-action-label\\\">\\n      <ng-content></ng-content>\\n      <span class=\\\"mat-mdc-chip-primary-focus-indicator mat-focus-indicator\\\"></span>\\n    </span>\\n  </button>\\n</span>\\n\\n@if (_hasTrailingIcon()) {\\n  <span class=\\\"mdc-evolution-chip__cell mdc-evolution-chip__cell--trailing\\\">\\n    <ng-content select=\\\"mat-chip-trailing-icon,[matChipRemove],[matChipTrailingIcon]\\\"></ng-content>\\n  </span>\\n}\\n\\n<span class=\\\"cdk-visually-hidden\\\" [id]=\\\"_ariaDescriptionId\\\">{{ariaDescription}}</span>\\n\",\n      styles: [\".mdc-evolution-chip,.mdc-evolution-chip__cell,.mdc-evolution-chip__action{display:inline-flex;align-items:center}.mdc-evolution-chip{position:relative;max-width:100%}.mdc-evolution-chip__cell,.mdc-evolution-chip__action{height:100%}.mdc-evolution-chip__cell--primary{flex-basis:100%;overflow-x:hidden}.mdc-evolution-chip__cell--trailing{flex:1 0 auto}.mdc-evolution-chip__action{align-items:center;background:none;border:none;box-sizing:content-box;cursor:pointer;display:inline-flex;justify-content:center;outline:none;padding:0;text-decoration:none;color:inherit}.mdc-evolution-chip__action--presentational{cursor:auto}.mdc-evolution-chip--disabled,.mdc-evolution-chip__action:disabled{pointer-events:none}@media(forced-colors: active){.mdc-evolution-chip--disabled,.mdc-evolution-chip__action:disabled{forced-color-adjust:none}}.mdc-evolution-chip__action--primary{font:inherit;letter-spacing:inherit;white-space:inherit;overflow-x:hidden}.mat-mdc-standard-chip .mdc-evolution-chip__action--primary::before{border-width:var(--mdc-chip-outline-width, 1px);border-radius:var(--mdc-chip-container-shape-radius, 8px);box-sizing:border-box;content:\\\"\\\";height:100%;left:0;position:absolute;pointer-events:none;top:0;width:100%;z-index:1;border-style:solid}.mat-mdc-standard-chip .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:12px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__action--primary::before{border-color:var(--mdc-chip-outline-color, var(--mat-sys-outline))}.mdc-evolution-chip__action--primary:not(.mdc-evolution-chip__action--presentational):not(.mdc-ripple-upgraded):focus::before{border-color:var(--mdc-chip-focus-outline-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__action--primary::before{border-color:var(--mdc-chip-disabled-outline-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-standard-chip.mdc-evolution-chip--selected .mdc-evolution-chip__action--primary::before{border-width:var(--mdc-chip-flat-selected-outline-width, 0)}.mat-mdc-basic-chip .mdc-evolution-chip__action--primary{font:inherit}.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}.mdc-evolution-chip__action--trailing{position:relative;overflow:visible}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__action--trailing{color:var(--mdc-chip-with-trailing-icon-trailing-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__action--trailing{color:var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-color, var(--mat-sys-on-surface))}.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mdc-evolution-chip__text-label{-webkit-user-select:none;user-select:none;white-space:nowrap;text-overflow:ellipsis;overflow:hidden}.mat-mdc-standard-chip .mdc-evolution-chip__text-label{font-family:var(--mdc-chip-label-text-font, var(--mat-sys-label-large-font));line-height:var(--mdc-chip-label-text-line-height, var(--mat-sys-label-large-line-height));font-size:var(--mdc-chip-label-text-size, var(--mat-sys-label-large-size));font-weight:var(--mdc-chip-label-text-weight, var(--mat-sys-label-large-weight));letter-spacing:var(--mdc-chip-label-text-tracking, var(--mat-sys-label-large-tracking))}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__text-label{color:var(--mdc-chip-label-text-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--selected:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__text-label{color:var(--mdc-chip-selected-label-text-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__text-label,.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled .mdc-evolution-chip__text-label{color:var(--mdc-chip-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-evolution-chip__graphic{align-items:center;display:inline-flex;justify-content:center;overflow:hidden;pointer-events:none;position:relative;flex:1 0 auto}.mat-mdc-standard-chip .mdc-evolution-chip__graphic{width:var(--mdc-chip-with-avatar-avatar-size, 24px);height:var(--mdc-chip-with-avatar-avatar-size, 24px);font-size:var(--mdc-chip-with-avatar-avatar-size, 24px)}.mdc-evolution-chip--selecting .mdc-evolution-chip__graphic{transition:width 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--selectable:not(.mdc-evolution-chip--selected):not(.mdc-evolution-chip--with-primary-icon) .mdc-evolution-chip__graphic{width:0}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:6px;padding-right:6px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:4px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:8px;padding-right:4px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:6px;padding-right:6px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:4px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:8px;padding-right:4px}.mdc-evolution-chip__checkmark{position:absolute;opacity:0;top:50%;left:50%;height:20px;width:20px}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__checkmark{color:var(--mdc-chip-with-icon-selected-icon-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__checkmark{color:var(--mdc-chip-with-icon-disabled-icon-color, var(--mat-sys-on-surface))}.mdc-evolution-chip--selecting .mdc-evolution-chip__checkmark{transition:transform 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1);transform:translate(-75%, -50%)}.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark{transform:translate(-50%, -50%);opacity:1}.mdc-evolution-chip__checkmark-svg{display:block}.mdc-evolution-chip__checkmark-path{stroke-width:2px;stroke-dasharray:29.7833385;stroke-dashoffset:29.7833385;stroke:currentColor}.mdc-evolution-chip--selecting .mdc-evolution-chip__checkmark-path{transition:stroke-dashoffset 150ms 45ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark-path{stroke-dashoffset:0}@media(forced-colors: active){.mdc-evolution-chip__checkmark-path{stroke:CanvasText !important}}.mat-mdc-standard-chip .mdc-evolution-chip__icon--trailing{height:18px;width:18px;font-size:18px}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing.mat-mdc-chip-remove{opacity:calc(var(--mat-chip-trailing-action-opacity, 1)*var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38))}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing.mat-mdc-chip-remove:focus{opacity:calc(var(--mat-chip-trailing-action-focus-opacity, 1)*var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38))}.mat-mdc-standard-chip{border-radius:var(--mdc-chip-container-shape-radius, 8px);height:var(--mdc-chip-container-height, 32px)}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled){background-color:var(--mdc-chip-elevated-container-color, transparent)}.mat-mdc-standard-chip.mdc-evolution-chip--disabled{background-color:var(--mdc-chip-elevated-disabled-container-color)}.mat-mdc-standard-chip.mdc-evolution-chip--selected:not(.mdc-evolution-chip--disabled){background-color:var(--mdc-chip-elevated-selected-container-color, var(--mat-sys-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled{background-color:var(--mdc-chip-flat-disabled-selected-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}@media(forced-colors: active){.mat-mdc-standard-chip{outline:solid 1px}}.mat-mdc-standard-chip .mdc-evolution-chip__icon--primary{border-radius:var(--mdc-chip-with-avatar-avatar-shape-radius, 24px);width:var(--mdc-chip-with-icon-icon-size, 18px);height:var(--mdc-chip-with-icon-icon-size, 18px);font-size:var(--mdc-chip-with-icon-icon-size, 18px)}.mdc-evolution-chip--selected .mdc-evolution-chip__icon--primary{opacity:0}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__icon--primary{color:var(--mdc-chip-with-icon-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--primary{color:var(--mdc-chip-with-icon-disabled-icon-color, var(--mat-sys-on-surface))}.mat-mdc-chip-highlighted{--mdc-chip-with-icon-icon-color:var(--mdc-chip-with-icon-selected-icon-color, var(--mat-sys-on-secondary-container));--mdc-chip-elevated-container-color:var(--mdc-chip-elevated-selected-container-color, var(--mat-sys-secondary-container));--mdc-chip-label-text-color:var(--mdc-chip-selected-label-text-color, var(--mat-sys-on-secondary-container));--mdc-chip-outline-width:var(--mdc-chip-flat-selected-outline-width, 0)}.mat-mdc-chip-focus-overlay{background:var(--mdc-chip-focus-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-chip-selected .mat-mdc-chip-focus-overlay,.mat-mdc-chip-highlighted .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-selected-focus-state-layer-color, var(--mat-sys-on-secondary-container))}.mat-mdc-chip:hover .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-hover-state-layer-color, var(--mat-sys-on-surface-variant));opacity:var(--mdc-chip-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip-focus-overlay .mat-mdc-chip-selected:hover,.mat-mdc-chip-highlighted:hover .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-selected-hover-state-layer-color, var(--mat-sys-on-secondary-container));opacity:var(--mdc-chip-selected-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip.cdk-focused .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-focus-state-layer-color, var(--mat-sys-on-surface-variant));opacity:var(--mdc-chip-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-chip-selected.cdk-focused .mat-mdc-chip-focus-overlay,.mat-mdc-chip-highlighted.cdk-focused .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-selected-focus-state-layer-color, var(--mat-sys-on-secondary-container));opacity:var(--mdc-chip-selected-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mdc-evolution-chip--disabled:not(.mdc-evolution-chip--selected) .mat-mdc-chip-avatar{opacity:var(--mdc-chip-with-avatar-disabled-avatar-opacity, 0.38)}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing{opacity:var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38)}.mdc-evolution-chip--disabled.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark{opacity:var(--mdc-chip-with-icon-disabled-icon-opacity, 0.38)}.mat-mdc-standard-chip.mdc-evolution-chip--disabled{opacity:var(--mat-chip-disabled-container-opacity, 1)}.mat-mdc-standard-chip.mdc-evolution-chip--selected .mdc-evolution-chip__icon--trailing,.mat-mdc-standard-chip.mat-mdc-chip-highlighted .mdc-evolution-chip__icon--trailing{color:var(--mat-chip-selected-trailing-icon-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing,.mat-mdc-standard-chip.mat-mdc-chip-highlighted.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing{color:var(--mat-chip-selected-disabled-trailing-icon-color, var(--mat-sys-on-surface))}.mat-mdc-chip-remove{opacity:var(--mat-chip-trailing-action-opacity, 1)}.mat-mdc-chip-remove:focus{opacity:var(--mat-chip-trailing-action-focus-opacity, 1)}.mat-mdc-chip-remove::after{background-color:var(--mat-chip-trailing-action-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-chip-remove:hover::after{opacity:var(--mat-chip-trailing-action-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip-remove:focus::after{opacity:var(--mat-chip-trailing-action-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-chip-selected .mat-mdc-chip-remove::after,.mat-mdc-chip-highlighted .mat-mdc-chip-remove::after{background-color:var(--mat-chip-selected-trailing-action-state-layer-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-standard-chip .mdc-evolution-chip__cell--primary,.mat-mdc-standard-chip .mdc-evolution-chip__action--primary,.mat-mdc-standard-chip .mat-mdc-chip-action-label{overflow:visible}.mat-mdc-standard-chip .mat-mdc-chip-graphic,.mat-mdc-standard-chip .mat-mdc-chip-trailing-icon{box-sizing:content-box}.mat-mdc-standard-chip._mat-animation-noopable,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__graphic,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__checkmark,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__checkmark-path{transition-duration:1ms;animation-duration:1ms}.mat-mdc-chip-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;opacity:0;border-radius:inherit;transition:opacity 150ms linear}._mat-animation-noopable .mat-mdc-chip-focus-overlay{transition:none}.mat-mdc-basic-chip .mat-mdc-chip-focus-overlay{display:none}.mat-mdc-chip .mat-ripple.mat-mdc-chip-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-chip-avatar{text-align:center;line-height:1;color:var(--mdc-chip-with-icon-icon-color, currentColor)}.mat-mdc-chip{position:relative;z-index:0}.mat-mdc-chip-action-label{text-align:left;z-index:1}[dir=rtl] .mat-mdc-chip-action-label{text-align:right}.mat-mdc-chip.mdc-evolution-chip--with-trailing-action .mat-mdc-chip-action-label{position:relative}.mat-mdc-chip-action-label .mat-mdc-chip-primary-focus-indicator{position:absolute;top:0;right:0;bottom:0;left:0;pointer-events:none}.mat-mdc-chip-action-label .mat-focus-indicator::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-mdc-chip-remove::before{margin:calc(var(--mat-focus-indicator-border-width, 3px)*-1);left:8px;right:8px}.mat-mdc-chip-remove::after{content:\\\"\\\";display:block;opacity:0;position:absolute;top:-3px;bottom:-3px;left:5px;right:5px;border-radius:50%;box-sizing:border-box;padding:12px;margin:-12px;background-clip:content-box}.mat-mdc-chip-remove .mat-icon{width:18px;height:18px;font-size:18px;box-sizing:content-box}.mat-chip-edit-input{cursor:text;display:inline-block;color:inherit;outline:0}@media(forced-colors: active){.mat-mdc-chip-selected:not(.mat-mdc-chip-multiple){outline-width:3px}}.mat-mdc-chip-action:focus .mat-focus-indicator::before{content:\\\"\\\"}.mdc-evolution-chip__icon,.mat-mdc-chip-remove .mat-icon{min-height:fit-content}\\n\"]\n    }]\n  }], null, {\n    selectable: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    selected: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    selectionChange: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * A directive that makes a span editable and exposes functions to modify and retrieve the\n * element's contents.\n */\nclass MatChipEditInput {\n  _elementRef = inject(ElementRef);\n  _document = inject(DOCUMENT);\n  constructor() {}\n  initialize(initialValue) {\n    this.getNativeElement().focus();\n    this.setValue(initialValue);\n  }\n  getNativeElement() {\n    return this._elementRef.nativeElement;\n  }\n  setValue(value) {\n    this.getNativeElement().textContent = value;\n    this._moveCursorToEndOfInput();\n  }\n  getValue() {\n    return this.getNativeElement().textContent || '';\n  }\n  _moveCursorToEndOfInput() {\n    const range = this._document.createRange();\n    range.selectNodeContents(this.getNativeElement());\n    range.collapse(false);\n    const sel = window.getSelection();\n    sel.removeAllRanges();\n    sel.addRange(range);\n  }\n  static ɵfac = function MatChipEditInput_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatChipEditInput)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatChipEditInput,\n    selectors: [[\"span\", \"matChipEditInput\", \"\"]],\n    hostAttrs: [\"role\", \"textbox\", \"tabindex\", \"-1\", \"contenteditable\", \"true\", 1, \"mat-chip-edit-input\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatChipEditInput, [{\n    type: Directive,\n    args: [{\n      selector: 'span[matChipEditInput]',\n      host: {\n        'class': 'mat-chip-edit-input',\n        'role': 'textbox',\n        'tabindex': '-1',\n        'contenteditable': 'true'\n      }\n    }]\n  }], () => [], null);\n})();\n\n/**\n * An extension of the MatChip component used with MatChipGrid and\n * the matChipInputFor directive.\n */\nclass MatChipRow extends MatChip {\n  basicChipAttrName = 'mat-basic-chip-row';\n  /**\n   * The editing action has to be triggered in a timeout. While we're waiting on it, a blur\n   * event might occur which will interrupt the editing. This flag is used to avoid interruptions\n   * while the editing action is being initialized.\n   */\n  _editStartPending = false;\n  editable = false;\n  /** Emitted when the chip is edited. */\n  edited = new EventEmitter();\n  /** The default chip edit input that is used if none is projected into this chip row. */\n  defaultEditInput;\n  /** The projected chip edit input. */\n  contentEditInput;\n  _isEditing = false;\n  constructor() {\n    super();\n    this.role = 'row';\n    this._onBlur.pipe(takeUntil(this.destroyed)).subscribe(() => {\n      if (this._isEditing && !this._editStartPending) {\n        this._onEditFinish();\n      }\n    });\n  }\n  _hasTrailingIcon() {\n    // The trailing icon is hidden while editing.\n    return !this._isEditing && super._hasTrailingIcon();\n  }\n  /** Sends focus to the first gridcell when the user clicks anywhere inside the chip. */\n  _handleFocus() {\n    if (!this._isEditing && !this.disabled) {\n      this.focus();\n    }\n  }\n  _handleKeydown(event) {\n    if (event.keyCode === ENTER && !this.disabled) {\n      if (this._isEditing) {\n        event.preventDefault();\n        this._onEditFinish();\n      } else if (this.editable) {\n        this._startEditing(event);\n      }\n    } else if (this._isEditing) {\n      // Stop the event from reaching the chip set in order to avoid navigating.\n      event.stopPropagation();\n    } else {\n      super._handleKeydown(event);\n    }\n  }\n  _handleDoubleclick(event) {\n    if (!this.disabled && this.editable) {\n      this._startEditing(event);\n    }\n  }\n  _startEditing(event) {\n    if (!this.primaryAction || this.removeIcon && this._getSourceAction(event.target) === this.removeIcon) {\n      return;\n    }\n    // The value depends on the DOM so we need to extract it before we flip the flag.\n    const value = this.value;\n    this._isEditing = this._editStartPending = true;\n    // Defer initializing the input until after it has been added to the DOM.\n    afterNextRender(() => {\n      this._getEditInput().initialize(value);\n      this._editStartPending = false;\n    }, {\n      injector: this._injector\n    });\n  }\n  _onEditFinish() {\n    this._isEditing = this._editStartPending = false;\n    this.edited.emit({\n      chip: this,\n      value: this._getEditInput().getValue()\n    });\n    // If the edit input is still focused or focus was returned to the body after it was destroyed,\n    // return focus to the chip contents.\n    if (this._document.activeElement === this._getEditInput().getNativeElement() || this._document.activeElement === this._document.body) {\n      this.primaryAction.focus();\n    }\n  }\n  _isRippleDisabled() {\n    return super._isRippleDisabled() || this._isEditing;\n  }\n  /**\n   * Gets the projected chip edit input, or the default input if none is projected in. One of these\n   * two values is guaranteed to be defined.\n   */\n  _getEditInput() {\n    return this.contentEditInput || this.defaultEditInput;\n  }\n  static ɵfac = function MatChipRow_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatChipRow)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatChipRow,\n    selectors: [[\"mat-chip-row\"], [\"\", \"mat-chip-row\", \"\"], [\"mat-basic-chip-row\"], [\"\", \"mat-basic-chip-row\", \"\"]],\n    contentQueries: function MatChipRow_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, MatChipEditInput, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.contentEditInput = _t.first);\n      }\n    },\n    viewQuery: function MatChipRow_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(MatChipEditInput, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.defaultEditInput = _t.first);\n      }\n    },\n    hostAttrs: [1, \"mat-mdc-chip\", \"mat-mdc-chip-row\", \"mdc-evolution-chip\"],\n    hostVars: 27,\n    hostBindings: function MatChipRow_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"focus\", function MatChipRow_focus_HostBindingHandler() {\n          return ctx._handleFocus();\n        })(\"dblclick\", function MatChipRow_dblclick_HostBindingHandler($event) {\n          return ctx._handleDoubleclick($event);\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵhostProperty(\"id\", ctx.id);\n        i0.ɵɵattribute(\"tabindex\", ctx.disabled ? null : -1)(\"aria-label\", null)(\"aria-description\", null)(\"role\", ctx.role);\n        i0.ɵɵclassProp(\"mat-mdc-chip-with-avatar\", ctx.leadingIcon)(\"mat-mdc-chip-disabled\", ctx.disabled)(\"mat-mdc-chip-editing\", ctx._isEditing)(\"mat-mdc-chip-editable\", ctx.editable)(\"mdc-evolution-chip--disabled\", ctx.disabled)(\"mdc-evolution-chip--with-trailing-action\", ctx._hasTrailingIcon())(\"mdc-evolution-chip--with-primary-graphic\", ctx.leadingIcon)(\"mdc-evolution-chip--with-primary-icon\", ctx.leadingIcon)(\"mdc-evolution-chip--with-avatar\", ctx.leadingIcon)(\"mat-mdc-chip-highlighted\", ctx.highlighted)(\"mat-mdc-chip-with-trailing-icon\", ctx._hasTrailingIcon());\n      }\n    },\n    inputs: {\n      editable: \"editable\"\n    },\n    outputs: {\n      edited: \"edited\"\n    },\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MatChip,\n      useExisting: MatChipRow\n    }, {\n      provide: MAT_CHIP,\n      useExisting: MatChipRow\n    }]), i0.ɵɵInheritDefinitionFeature],\n    ngContentSelectors: _c4,\n    decls: 10,\n    vars: 9,\n    consts: [[1, \"mat-mdc-chip-focus-overlay\"], [\"role\", \"gridcell\", \"matChipAction\", \"\", 1, \"mdc-evolution-chip__cell\", \"mdc-evolution-chip__cell--primary\", 3, \"disabled\"], [1, \"mdc-evolution-chip__graphic\", \"mat-mdc-chip-graphic\"], [1, \"mdc-evolution-chip__text-label\", \"mat-mdc-chip-action-label\"], [\"aria-hidden\", \"true\", 1, \"mat-mdc-chip-primary-focus-indicator\", \"mat-focus-indicator\"], [\"role\", \"gridcell\", 1, \"mdc-evolution-chip__cell\", \"mdc-evolution-chip__cell--trailing\"], [1, \"cdk-visually-hidden\", 3, \"id\"], [\"matChipEditInput\", \"\"]],\n    template: function MatChipRow_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef(_c3);\n        i0.ɵɵtemplate(0, MatChipRow_Conditional_0_Template, 1, 0, \"span\", 0);\n        i0.ɵɵelementStart(1, \"span\", 1);\n        i0.ɵɵtemplate(2, MatChipRow_Conditional_2_Template, 2, 0, \"span\", 2);\n        i0.ɵɵelementStart(3, \"span\", 3);\n        i0.ɵɵtemplate(4, MatChipRow_Conditional_4_Template, 2, 1)(5, MatChipRow_Conditional_5_Template, 1, 0);\n        i0.ɵɵelement(6, \"span\", 4);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(7, MatChipRow_Conditional_7_Template, 2, 0, \"span\", 5);\n        i0.ɵɵelementStart(8, \"span\", 6);\n        i0.ɵɵtext(9);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵconditional(!ctx._isEditing ? 0 : -1);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"disabled\", ctx.disabled);\n        i0.ɵɵattribute(\"aria-label\", ctx.ariaLabel)(\"aria-describedby\", ctx._ariaDescriptionId);\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(ctx.leadingIcon ? 2 : -1);\n        i0.ɵɵadvance(2);\n        i0.ɵɵconditional(ctx._isEditing ? 4 : 5);\n        i0.ɵɵadvance(3);\n        i0.ɵɵconditional(ctx._hasTrailingIcon() ? 7 : -1);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"id\", ctx._ariaDescriptionId);\n        i0.ɵɵadvance();\n        i0.ɵɵtextInterpolate(ctx.ariaDescription);\n      }\n    },\n    dependencies: [MatChipAction, MatChipEditInput],\n    styles: [_c2],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatChipRow, [{\n    type: Component,\n    args: [{\n      selector: 'mat-chip-row, [mat-chip-row], mat-basic-chip-row, [mat-basic-chip-row]',\n      host: {\n        'class': 'mat-mdc-chip mat-mdc-chip-row mdc-evolution-chip',\n        '[class.mat-mdc-chip-with-avatar]': 'leadingIcon',\n        '[class.mat-mdc-chip-disabled]': 'disabled',\n        '[class.mat-mdc-chip-editing]': '_isEditing',\n        '[class.mat-mdc-chip-editable]': 'editable',\n        '[class.mdc-evolution-chip--disabled]': 'disabled',\n        '[class.mdc-evolution-chip--with-trailing-action]': '_hasTrailingIcon()',\n        '[class.mdc-evolution-chip--with-primary-graphic]': 'leadingIcon',\n        '[class.mdc-evolution-chip--with-primary-icon]': 'leadingIcon',\n        '[class.mdc-evolution-chip--with-avatar]': 'leadingIcon',\n        '[class.mat-mdc-chip-highlighted]': 'highlighted',\n        '[class.mat-mdc-chip-with-trailing-icon]': '_hasTrailingIcon()',\n        '[id]': 'id',\n        // Has to have a negative tabindex in order to capture\n        // focus and redirect it to the primary action.\n        '[attr.tabindex]': 'disabled ? null : -1',\n        '[attr.aria-label]': 'null',\n        '[attr.aria-description]': 'null',\n        '[attr.role]': 'role',\n        '(focus)': '_handleFocus()',\n        '(dblclick)': '_handleDoubleclick($event)'\n      },\n      providers: [{\n        provide: MatChip,\n        useExisting: MatChipRow\n      }, {\n        provide: MAT_CHIP,\n        useExisting: MatChipRow\n      }],\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      imports: [MatChipAction, MatChipEditInput],\n      template: \"@if (!_isEditing) {\\n  <span class=\\\"mat-mdc-chip-focus-overlay\\\"></span>\\n}\\n\\n<span class=\\\"mdc-evolution-chip__cell mdc-evolution-chip__cell--primary\\\" role=\\\"gridcell\\\"\\n    matChipAction\\n    [disabled]=\\\"disabled\\\"\\n    [attr.aria-label]=\\\"ariaLabel\\\"\\n    [attr.aria-describedby]=\\\"_ariaDescriptionId\\\">\\n  @if (leadingIcon) {\\n    <span class=\\\"mdc-evolution-chip__graphic mat-mdc-chip-graphic\\\">\\n      <ng-content select=\\\"mat-chip-avatar, [matChipAvatar]\\\"></ng-content>\\n    </span>\\n  }\\n\\n  <span class=\\\"mdc-evolution-chip__text-label mat-mdc-chip-action-label\\\">\\n    @if (_isEditing) {\\n      @if (contentEditInput) {\\n        <ng-content select=\\\"[matChipEditInput]\\\"></ng-content>\\n      } @else {\\n        <span matChipEditInput></span>\\n      }\\n    } @else {\\n      <ng-content></ng-content>\\n    }\\n\\n    <span class=\\\"mat-mdc-chip-primary-focus-indicator mat-focus-indicator\\\" aria-hidden=\\\"true\\\"></span>\\n  </span>\\n</span>\\n\\n@if (_hasTrailingIcon()) {\\n  <span\\n    class=\\\"mdc-evolution-chip__cell mdc-evolution-chip__cell--trailing\\\"\\n    role=\\\"gridcell\\\">\\n    <ng-content select=\\\"mat-chip-trailing-icon,[matChipRemove],[matChipTrailingIcon]\\\"></ng-content>\\n  </span>\\n}\\n\\n<span class=\\\"cdk-visually-hidden\\\" [id]=\\\"_ariaDescriptionId\\\">{{ariaDescription}}</span>\\n\",\n      styles: [\".mdc-evolution-chip,.mdc-evolution-chip__cell,.mdc-evolution-chip__action{display:inline-flex;align-items:center}.mdc-evolution-chip{position:relative;max-width:100%}.mdc-evolution-chip__cell,.mdc-evolution-chip__action{height:100%}.mdc-evolution-chip__cell--primary{flex-basis:100%;overflow-x:hidden}.mdc-evolution-chip__cell--trailing{flex:1 0 auto}.mdc-evolution-chip__action{align-items:center;background:none;border:none;box-sizing:content-box;cursor:pointer;display:inline-flex;justify-content:center;outline:none;padding:0;text-decoration:none;color:inherit}.mdc-evolution-chip__action--presentational{cursor:auto}.mdc-evolution-chip--disabled,.mdc-evolution-chip__action:disabled{pointer-events:none}@media(forced-colors: active){.mdc-evolution-chip--disabled,.mdc-evolution-chip__action:disabled{forced-color-adjust:none}}.mdc-evolution-chip__action--primary{font:inherit;letter-spacing:inherit;white-space:inherit;overflow-x:hidden}.mat-mdc-standard-chip .mdc-evolution-chip__action--primary::before{border-width:var(--mdc-chip-outline-width, 1px);border-radius:var(--mdc-chip-container-shape-radius, 8px);box-sizing:border-box;content:\\\"\\\";height:100%;left:0;position:absolute;pointer-events:none;top:0;width:100%;z-index:1;border-style:solid}.mat-mdc-standard-chip .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:12px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__action--primary::before{border-color:var(--mdc-chip-outline-color, var(--mat-sys-outline))}.mdc-evolution-chip__action--primary:not(.mdc-evolution-chip__action--presentational):not(.mdc-ripple-upgraded):focus::before{border-color:var(--mdc-chip-focus-outline-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__action--primary::before{border-color:var(--mdc-chip-disabled-outline-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-standard-chip.mdc-evolution-chip--selected .mdc-evolution-chip__action--primary::before{border-width:var(--mdc-chip-flat-selected-outline-width, 0)}.mat-mdc-basic-chip .mdc-evolution-chip__action--primary{font:inherit}.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}.mdc-evolution-chip__action--trailing{position:relative;overflow:visible}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__action--trailing{color:var(--mdc-chip-with-trailing-icon-trailing-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__action--trailing{color:var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-color, var(--mat-sys-on-surface))}.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mdc-evolution-chip__text-label{-webkit-user-select:none;user-select:none;white-space:nowrap;text-overflow:ellipsis;overflow:hidden}.mat-mdc-standard-chip .mdc-evolution-chip__text-label{font-family:var(--mdc-chip-label-text-font, var(--mat-sys-label-large-font));line-height:var(--mdc-chip-label-text-line-height, var(--mat-sys-label-large-line-height));font-size:var(--mdc-chip-label-text-size, var(--mat-sys-label-large-size));font-weight:var(--mdc-chip-label-text-weight, var(--mat-sys-label-large-weight));letter-spacing:var(--mdc-chip-label-text-tracking, var(--mat-sys-label-large-tracking))}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__text-label{color:var(--mdc-chip-label-text-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--selected:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__text-label{color:var(--mdc-chip-selected-label-text-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__text-label,.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled .mdc-evolution-chip__text-label{color:var(--mdc-chip-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-evolution-chip__graphic{align-items:center;display:inline-flex;justify-content:center;overflow:hidden;pointer-events:none;position:relative;flex:1 0 auto}.mat-mdc-standard-chip .mdc-evolution-chip__graphic{width:var(--mdc-chip-with-avatar-avatar-size, 24px);height:var(--mdc-chip-with-avatar-avatar-size, 24px);font-size:var(--mdc-chip-with-avatar-avatar-size, 24px)}.mdc-evolution-chip--selecting .mdc-evolution-chip__graphic{transition:width 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--selectable:not(.mdc-evolution-chip--selected):not(.mdc-evolution-chip--with-primary-icon) .mdc-evolution-chip__graphic{width:0}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:6px;padding-right:6px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:4px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:8px;padding-right:4px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:6px;padding-right:6px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:4px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:8px;padding-right:4px}.mdc-evolution-chip__checkmark{position:absolute;opacity:0;top:50%;left:50%;height:20px;width:20px}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__checkmark{color:var(--mdc-chip-with-icon-selected-icon-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__checkmark{color:var(--mdc-chip-with-icon-disabled-icon-color, var(--mat-sys-on-surface))}.mdc-evolution-chip--selecting .mdc-evolution-chip__checkmark{transition:transform 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1);transform:translate(-75%, -50%)}.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark{transform:translate(-50%, -50%);opacity:1}.mdc-evolution-chip__checkmark-svg{display:block}.mdc-evolution-chip__checkmark-path{stroke-width:2px;stroke-dasharray:29.7833385;stroke-dashoffset:29.7833385;stroke:currentColor}.mdc-evolution-chip--selecting .mdc-evolution-chip__checkmark-path{transition:stroke-dashoffset 150ms 45ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark-path{stroke-dashoffset:0}@media(forced-colors: active){.mdc-evolution-chip__checkmark-path{stroke:CanvasText !important}}.mat-mdc-standard-chip .mdc-evolution-chip__icon--trailing{height:18px;width:18px;font-size:18px}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing.mat-mdc-chip-remove{opacity:calc(var(--mat-chip-trailing-action-opacity, 1)*var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38))}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing.mat-mdc-chip-remove:focus{opacity:calc(var(--mat-chip-trailing-action-focus-opacity, 1)*var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38))}.mat-mdc-standard-chip{border-radius:var(--mdc-chip-container-shape-radius, 8px);height:var(--mdc-chip-container-height, 32px)}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled){background-color:var(--mdc-chip-elevated-container-color, transparent)}.mat-mdc-standard-chip.mdc-evolution-chip--disabled{background-color:var(--mdc-chip-elevated-disabled-container-color)}.mat-mdc-standard-chip.mdc-evolution-chip--selected:not(.mdc-evolution-chip--disabled){background-color:var(--mdc-chip-elevated-selected-container-color, var(--mat-sys-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled{background-color:var(--mdc-chip-flat-disabled-selected-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}@media(forced-colors: active){.mat-mdc-standard-chip{outline:solid 1px}}.mat-mdc-standard-chip .mdc-evolution-chip__icon--primary{border-radius:var(--mdc-chip-with-avatar-avatar-shape-radius, 24px);width:var(--mdc-chip-with-icon-icon-size, 18px);height:var(--mdc-chip-with-icon-icon-size, 18px);font-size:var(--mdc-chip-with-icon-icon-size, 18px)}.mdc-evolution-chip--selected .mdc-evolution-chip__icon--primary{opacity:0}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__icon--primary{color:var(--mdc-chip-with-icon-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--primary{color:var(--mdc-chip-with-icon-disabled-icon-color, var(--mat-sys-on-surface))}.mat-mdc-chip-highlighted{--mdc-chip-with-icon-icon-color:var(--mdc-chip-with-icon-selected-icon-color, var(--mat-sys-on-secondary-container));--mdc-chip-elevated-container-color:var(--mdc-chip-elevated-selected-container-color, var(--mat-sys-secondary-container));--mdc-chip-label-text-color:var(--mdc-chip-selected-label-text-color, var(--mat-sys-on-secondary-container));--mdc-chip-outline-width:var(--mdc-chip-flat-selected-outline-width, 0)}.mat-mdc-chip-focus-overlay{background:var(--mdc-chip-focus-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-chip-selected .mat-mdc-chip-focus-overlay,.mat-mdc-chip-highlighted .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-selected-focus-state-layer-color, var(--mat-sys-on-secondary-container))}.mat-mdc-chip:hover .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-hover-state-layer-color, var(--mat-sys-on-surface-variant));opacity:var(--mdc-chip-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip-focus-overlay .mat-mdc-chip-selected:hover,.mat-mdc-chip-highlighted:hover .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-selected-hover-state-layer-color, var(--mat-sys-on-secondary-container));opacity:var(--mdc-chip-selected-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip.cdk-focused .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-focus-state-layer-color, var(--mat-sys-on-surface-variant));opacity:var(--mdc-chip-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-chip-selected.cdk-focused .mat-mdc-chip-focus-overlay,.mat-mdc-chip-highlighted.cdk-focused .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-selected-focus-state-layer-color, var(--mat-sys-on-secondary-container));opacity:var(--mdc-chip-selected-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mdc-evolution-chip--disabled:not(.mdc-evolution-chip--selected) .mat-mdc-chip-avatar{opacity:var(--mdc-chip-with-avatar-disabled-avatar-opacity, 0.38)}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing{opacity:var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38)}.mdc-evolution-chip--disabled.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark{opacity:var(--mdc-chip-with-icon-disabled-icon-opacity, 0.38)}.mat-mdc-standard-chip.mdc-evolution-chip--disabled{opacity:var(--mat-chip-disabled-container-opacity, 1)}.mat-mdc-standard-chip.mdc-evolution-chip--selected .mdc-evolution-chip__icon--trailing,.mat-mdc-standard-chip.mat-mdc-chip-highlighted .mdc-evolution-chip__icon--trailing{color:var(--mat-chip-selected-trailing-icon-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing,.mat-mdc-standard-chip.mat-mdc-chip-highlighted.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing{color:var(--mat-chip-selected-disabled-trailing-icon-color, var(--mat-sys-on-surface))}.mat-mdc-chip-remove{opacity:var(--mat-chip-trailing-action-opacity, 1)}.mat-mdc-chip-remove:focus{opacity:var(--mat-chip-trailing-action-focus-opacity, 1)}.mat-mdc-chip-remove::after{background-color:var(--mat-chip-trailing-action-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-chip-remove:hover::after{opacity:var(--mat-chip-trailing-action-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip-remove:focus::after{opacity:var(--mat-chip-trailing-action-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-chip-selected .mat-mdc-chip-remove::after,.mat-mdc-chip-highlighted .mat-mdc-chip-remove::after{background-color:var(--mat-chip-selected-trailing-action-state-layer-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-standard-chip .mdc-evolution-chip__cell--primary,.mat-mdc-standard-chip .mdc-evolution-chip__action--primary,.mat-mdc-standard-chip .mat-mdc-chip-action-label{overflow:visible}.mat-mdc-standard-chip .mat-mdc-chip-graphic,.mat-mdc-standard-chip .mat-mdc-chip-trailing-icon{box-sizing:content-box}.mat-mdc-standard-chip._mat-animation-noopable,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__graphic,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__checkmark,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__checkmark-path{transition-duration:1ms;animation-duration:1ms}.mat-mdc-chip-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;opacity:0;border-radius:inherit;transition:opacity 150ms linear}._mat-animation-noopable .mat-mdc-chip-focus-overlay{transition:none}.mat-mdc-basic-chip .mat-mdc-chip-focus-overlay{display:none}.mat-mdc-chip .mat-ripple.mat-mdc-chip-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-chip-avatar{text-align:center;line-height:1;color:var(--mdc-chip-with-icon-icon-color, currentColor)}.mat-mdc-chip{position:relative;z-index:0}.mat-mdc-chip-action-label{text-align:left;z-index:1}[dir=rtl] .mat-mdc-chip-action-label{text-align:right}.mat-mdc-chip.mdc-evolution-chip--with-trailing-action .mat-mdc-chip-action-label{position:relative}.mat-mdc-chip-action-label .mat-mdc-chip-primary-focus-indicator{position:absolute;top:0;right:0;bottom:0;left:0;pointer-events:none}.mat-mdc-chip-action-label .mat-focus-indicator::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-mdc-chip-remove::before{margin:calc(var(--mat-focus-indicator-border-width, 3px)*-1);left:8px;right:8px}.mat-mdc-chip-remove::after{content:\\\"\\\";display:block;opacity:0;position:absolute;top:-3px;bottom:-3px;left:5px;right:5px;border-radius:50%;box-sizing:border-box;padding:12px;margin:-12px;background-clip:content-box}.mat-mdc-chip-remove .mat-icon{width:18px;height:18px;font-size:18px;box-sizing:content-box}.mat-chip-edit-input{cursor:text;display:inline-block;color:inherit;outline:0}@media(forced-colors: active){.mat-mdc-chip-selected:not(.mat-mdc-chip-multiple){outline-width:3px}}.mat-mdc-chip-action:focus .mat-focus-indicator::before{content:\\\"\\\"}.mdc-evolution-chip__icon,.mat-mdc-chip-remove .mat-icon{min-height:fit-content}\\n\"]\n    }]\n  }], () => [], {\n    editable: [{\n      type: Input\n    }],\n    edited: [{\n      type: Output\n    }],\n    defaultEditInput: [{\n      type: ViewChild,\n      args: [MatChipEditInput]\n    }],\n    contentEditInput: [{\n      type: ContentChild,\n      args: [MatChipEditInput]\n    }]\n  });\n})();\n\n/**\n * Basic container component for the MatChip component.\n *\n * Extended by MatChipListbox and MatChipGrid for different interaction patterns.\n */\nclass MatChipSet {\n  _elementRef = inject(ElementRef);\n  _changeDetectorRef = inject(ChangeDetectorRef);\n  _dir = inject(Directionality, {\n    optional: true\n  });\n  /** Index of the last destroyed chip that had focus. */\n  _lastDestroyedFocusedChipIndex = null;\n  /** Used to manage focus within the chip list. */\n  _keyManager;\n  /** Subject that emits when the component has been destroyed. */\n  _destroyed = new Subject();\n  /** Role to use if it hasn't been overwritten by the user. */\n  _defaultRole = 'presentation';\n  /** Combined stream of all of the child chips' focus events. */\n  get chipFocusChanges() {\n    return this._getChipStream(chip => chip._onFocus);\n  }\n  /** Combined stream of all of the child chips' destroy events. */\n  get chipDestroyedChanges() {\n    return this._getChipStream(chip => chip.destroyed);\n  }\n  /** Combined stream of all of the child chips' remove events. */\n  get chipRemovedChanges() {\n    return this._getChipStream(chip => chip.removed);\n  }\n  /** Whether the chip set is disabled. */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(value) {\n    this._disabled = value;\n    this._syncChipsState();\n  }\n  _disabled = false;\n  /** Whether the chip list contains chips or not. */\n  get empty() {\n    return !this._chips || this._chips.length === 0;\n  }\n  /** The ARIA role applied to the chip set. */\n  get role() {\n    if (this._explicitRole) {\n      return this._explicitRole;\n    }\n    return this.empty ? null : this._defaultRole;\n  }\n  /** Tabindex of the chip set. */\n  tabIndex = 0;\n  set role(value) {\n    this._explicitRole = value;\n  }\n  _explicitRole = null;\n  /** Whether any of the chips inside of this chip-set has focus. */\n  get focused() {\n    return this._hasFocusedChip();\n  }\n  /** The chips that are part of this chip set. */\n  _chips;\n  /** Flat list of all the actions contained within the chips. */\n  _chipActions = new QueryList();\n  constructor() {}\n  ngAfterViewInit() {\n    this._setUpFocusManagement();\n    this._trackChipSetChanges();\n    this._trackDestroyedFocusedChip();\n  }\n  ngOnDestroy() {\n    this._keyManager?.destroy();\n    this._chipActions.destroy();\n    this._destroyed.next();\n    this._destroyed.complete();\n  }\n  /** Checks whether any of the chips is focused. */\n  _hasFocusedChip() {\n    return this._chips && this._chips.some(chip => chip._hasFocus());\n  }\n  /** Syncs the chip-set's state with the individual chips. */\n  _syncChipsState() {\n    this._chips?.forEach(chip => {\n      chip._chipListDisabled = this._disabled;\n      chip._changeDetectorRef.markForCheck();\n    });\n  }\n  /** Dummy method for subclasses to override. Base chip set cannot be focused. */\n  focus() {}\n  /** Handles keyboard events on the chip set. */\n  _handleKeydown(event) {\n    if (this._originatesFromChip(event)) {\n      this._keyManager.onKeydown(event);\n    }\n  }\n  /**\n   * Utility to ensure all indexes are valid.\n   *\n   * @param index The index to be checked.\n   * @returns True if the index is valid for our list of chips.\n   */\n  _isValidIndex(index) {\n    return index >= 0 && index < this._chips.length;\n  }\n  /**\n   * Removes the `tabindex` from the chip set and resets it back afterwards, allowing the\n   * user to tab out of it. This prevents the set from capturing focus and redirecting\n   * it back to the first chip, creating a focus trap, if it user tries to tab away.\n   */\n  _allowFocusEscape() {\n    const previous = this._elementRef.nativeElement.tabIndex;\n    if (previous !== -1) {\n      // Set the tabindex directly on the element, instead of going through\n      // the data binding, because we aren't guaranteed that change detection\n      // will run quickly enough to allow focus to escape.\n      this._elementRef.nativeElement.tabIndex = -1;\n      // Note that this needs to be a `setTimeout`, because a `Promise.resolve`\n      // doesn't allow enough time for the focus to escape.\n      setTimeout(() => this._elementRef.nativeElement.tabIndex = previous);\n    }\n  }\n  /**\n   * Gets a stream of events from all the chips within the set.\n   * The stream will automatically incorporate any newly-added chips.\n   */\n  _getChipStream(mappingFunction) {\n    return this._chips.changes.pipe(startWith(null), switchMap(() => merge(...this._chips.map(mappingFunction))));\n  }\n  /** Checks whether an event comes from inside a chip element. */\n  _originatesFromChip(event) {\n    let currentElement = event.target;\n    while (currentElement && currentElement !== this._elementRef.nativeElement) {\n      if (currentElement.classList.contains('mat-mdc-chip')) {\n        return true;\n      }\n      currentElement = currentElement.parentElement;\n    }\n    return false;\n  }\n  /** Sets up the chip set's focus management logic. */\n  _setUpFocusManagement() {\n    // Create a flat `QueryList` containing the actions of all of the chips.\n    // This allows us to navigate both within the chip and move to the next/previous\n    // one using the existing `ListKeyManager`.\n    this._chips.changes.pipe(startWith(this._chips)).subscribe(chips => {\n      const actions = [];\n      chips.forEach(chip => chip._getActions().forEach(action => actions.push(action)));\n      this._chipActions.reset(actions);\n      this._chipActions.notifyOnChanges();\n    });\n    this._keyManager = new FocusKeyManager(this._chipActions).withVerticalOrientation().withHorizontalOrientation(this._dir ? this._dir.value : 'ltr').withHomeAndEnd().skipPredicate(action => this._skipPredicate(action));\n    // Keep the manager active index in sync so that navigation picks\n    // up from the current chip if the user clicks into the list directly.\n    this.chipFocusChanges.pipe(takeUntil(this._destroyed)).subscribe(({\n      chip\n    }) => {\n      const action = chip._getSourceAction(document.activeElement);\n      if (action) {\n        this._keyManager.updateActiveItem(action);\n      }\n    });\n    this._dir?.change.pipe(takeUntil(this._destroyed)).subscribe(direction => this._keyManager.withHorizontalOrientation(direction));\n  }\n  /**\n   * Determines if key manager should avoid putting a given chip action in the tab index. Skip\n   * non-interactive and disabled actions since the user can't do anything with them.\n   */\n  _skipPredicate(action) {\n    // Skip chips that the user cannot interact with. `mat-chip-set` does not permit focusing disabled\n    // chips.\n    return !action.isInteractive || action.disabled;\n  }\n  /** Listens to changes in the chip set and syncs up the state of the individual chips. */\n  _trackChipSetChanges() {\n    this._chips.changes.pipe(startWith(null), takeUntil(this._destroyed)).subscribe(() => {\n      if (this.disabled) {\n        // Since this happens after the content has been\n        // checked, we need to defer it to the next tick.\n        Promise.resolve().then(() => this._syncChipsState());\n      }\n      this._redirectDestroyedChipFocus();\n    });\n  }\n  /** Starts tracking the destroyed chips in order to capture the focused one. */\n  _trackDestroyedFocusedChip() {\n    this.chipDestroyedChanges.pipe(takeUntil(this._destroyed)).subscribe(event => {\n      const chipArray = this._chips.toArray();\n      const chipIndex = chipArray.indexOf(event.chip);\n      // If the focused chip is destroyed, save its index so that we can move focus to the next\n      // chip. We only save the index here, rather than move the focus immediately, because we want\n      // to wait until the chip is removed from the chip list before focusing the next one. This\n      // allows us to keep focus on the same index if the chip gets swapped out.\n      if (this._isValidIndex(chipIndex) && event.chip._hasFocus()) {\n        this._lastDestroyedFocusedChipIndex = chipIndex;\n      }\n    });\n  }\n  /**\n   * Finds the next appropriate chip to move focus to,\n   * if the currently-focused chip is destroyed.\n   */\n  _redirectDestroyedChipFocus() {\n    if (this._lastDestroyedFocusedChipIndex == null) {\n      return;\n    }\n    if (this._chips.length) {\n      const newIndex = Math.min(this._lastDestroyedFocusedChipIndex, this._chips.length - 1);\n      const chipToFocus = this._chips.toArray()[newIndex];\n      if (chipToFocus.disabled) {\n        // If we're down to one disabled chip, move focus back to the set.\n        if (this._chips.length === 1) {\n          this.focus();\n        } else {\n          this._keyManager.setPreviousItemActive();\n        }\n      } else {\n        chipToFocus.focus();\n      }\n    } else {\n      this.focus();\n    }\n    this._lastDestroyedFocusedChipIndex = null;\n  }\n  static ɵfac = function MatChipSet_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatChipSet)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatChipSet,\n    selectors: [[\"mat-chip-set\"]],\n    contentQueries: function MatChipSet_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, MatChip, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._chips = _t);\n      }\n    },\n    hostAttrs: [1, \"mat-mdc-chip-set\", \"mdc-evolution-chip-set\"],\n    hostVars: 1,\n    hostBindings: function MatChipSet_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"keydown\", function MatChipSet_keydown_HostBindingHandler($event) {\n          return ctx._handleKeydown($event);\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵattribute(\"role\", ctx.role);\n      }\n    },\n    inputs: {\n      disabled: [2, \"disabled\", \"disabled\", booleanAttribute],\n      role: \"role\",\n      tabIndex: [2, \"tabIndex\", \"tabIndex\", value => value == null ? 0 : numberAttribute(value)]\n    },\n    ngContentSelectors: _c5,\n    decls: 2,\n    vars: 0,\n    consts: [[\"role\", \"presentation\", 1, \"mdc-evolution-chip-set__chips\"]],\n    template: function MatChipSet_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵprojection(1);\n        i0.ɵɵelementEnd();\n      }\n    },\n    styles: [\".mat-mdc-chip-set{display:flex}.mat-mdc-chip-set:focus{outline:none}.mat-mdc-chip-set .mdc-evolution-chip-set__chips{min-width:100%;margin-left:-8px;margin-right:0}.mat-mdc-chip-set .mdc-evolution-chip{margin:4px 0 4px 8px}[dir=rtl] .mat-mdc-chip-set .mdc-evolution-chip-set__chips{margin-left:0;margin-right:-8px}[dir=rtl] .mat-mdc-chip-set .mdc-evolution-chip{margin-left:0;margin-right:8px}.mdc-evolution-chip-set__chips{display:flex;flex-flow:wrap;min-width:0}.mat-mdc-chip-set-stacked{flex-direction:column;align-items:flex-start}.mat-mdc-chip-set-stacked .mat-mdc-chip{width:100%}.mat-mdc-chip-set-stacked .mdc-evolution-chip__graphic{flex-grow:0}.mat-mdc-chip-set-stacked .mdc-evolution-chip__action--primary{flex-basis:100%;justify-content:start}input.mat-mdc-chip-input{flex:1 0 150px;margin-left:8px}[dir=rtl] input.mat-mdc-chip-input{margin-left:0;margin-right:8px}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatChipSet, [{\n    type: Component,\n    args: [{\n      selector: 'mat-chip-set',\n      template: `\n    <div class=\"mdc-evolution-chip-set__chips\" role=\"presentation\">\n      <ng-content></ng-content>\n    </div>\n  `,\n      host: {\n        'class': 'mat-mdc-chip-set mdc-evolution-chip-set',\n        '(keydown)': '_handleKeydown($event)',\n        '[attr.role]': 'role'\n      },\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      styles: [\".mat-mdc-chip-set{display:flex}.mat-mdc-chip-set:focus{outline:none}.mat-mdc-chip-set .mdc-evolution-chip-set__chips{min-width:100%;margin-left:-8px;margin-right:0}.mat-mdc-chip-set .mdc-evolution-chip{margin:4px 0 4px 8px}[dir=rtl] .mat-mdc-chip-set .mdc-evolution-chip-set__chips{margin-left:0;margin-right:-8px}[dir=rtl] .mat-mdc-chip-set .mdc-evolution-chip{margin-left:0;margin-right:8px}.mdc-evolution-chip-set__chips{display:flex;flex-flow:wrap;min-width:0}.mat-mdc-chip-set-stacked{flex-direction:column;align-items:flex-start}.mat-mdc-chip-set-stacked .mat-mdc-chip{width:100%}.mat-mdc-chip-set-stacked .mdc-evolution-chip__graphic{flex-grow:0}.mat-mdc-chip-set-stacked .mdc-evolution-chip__action--primary{flex-basis:100%;justify-content:start}input.mat-mdc-chip-input{flex:1 0 150px;margin-left:8px}[dir=rtl] input.mat-mdc-chip-input{margin-left:0;margin-right:8px}\\n\"]\n    }]\n  }], () => [], {\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    role: [{\n      type: Input\n    }],\n    tabIndex: [{\n      type: Input,\n      args: [{\n        transform: value => value == null ? 0 : numberAttribute(value)\n      }]\n    }],\n    _chips: [{\n      type: ContentChildren,\n      args: [MatChip, {\n        // We need to use `descendants: true`, because Ivy will no longer match\n        // indirect descendants if it's left as false.\n        descendants: true\n      }]\n    }]\n  });\n})();\n\n/** Change event object that is emitted when the chip listbox value has changed. */\nclass MatChipListboxChange {\n  source;\n  value;\n  constructor(/** Chip listbox that emitted the event. */\n  source, /** Value of the chip listbox when the event was emitted. */\n  value) {\n    this.source = source;\n    this.value = value;\n  }\n}\n/**\n * Provider Expression that allows mat-chip-listbox to register as a ControlValueAccessor.\n * This allows it to support [(ngModel)].\n * @docs-private\n */\nconst MAT_CHIP_LISTBOX_CONTROL_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => MatChipListbox),\n  multi: true\n};\n/**\n * An extension of the MatChipSet component that supports chip selection.\n * Used with MatChipOption chips.\n */\nclass MatChipListbox extends MatChipSet {\n  /**\n   * Function when touched. Set as part of ControlValueAccessor implementation.\n   * @docs-private\n   */\n  _onTouched = () => {};\n  /**\n   * Function when changed. Set as part of ControlValueAccessor implementation.\n   * @docs-private\n   */\n  _onChange = () => {};\n  // TODO: MDC uses `grid` here\n  _defaultRole = 'listbox';\n  /** Default chip options. */\n  _defaultOptions = inject(MAT_CHIPS_DEFAULT_OPTIONS, {\n    optional: true\n  });\n  /** Whether the user should be allowed to select multiple chips. */\n  get multiple() {\n    return this._multiple;\n  }\n  set multiple(value) {\n    this._multiple = value;\n    this._syncListboxProperties();\n  }\n  _multiple = false;\n  /** The array of selected chips inside the chip listbox. */\n  get selected() {\n    const selectedChips = this._chips.toArray().filter(chip => chip.selected);\n    return this.multiple ? selectedChips : selectedChips[0];\n  }\n  /** Orientation of the chip list. */\n  ariaOrientation = 'horizontal';\n  /**\n   * Whether or not this chip listbox is selectable.\n   *\n   * When a chip listbox is not selectable, the selected states for all\n   * the chips inside the chip listbox are always ignored.\n   */\n  get selectable() {\n    return this._selectable;\n  }\n  set selectable(value) {\n    this._selectable = value;\n    this._syncListboxProperties();\n  }\n  _selectable = true;\n  /**\n   * A function to compare the option values with the selected values. The first argument\n   * is a value from an option. The second is a value from the selection. A boolean\n   * should be returned.\n   */\n  compareWith = (o1, o2) => o1 === o2;\n  /** Whether this chip listbox is required. */\n  required = false;\n  /** Whether checkmark indicator for single-selection options is hidden. */\n  get hideSingleSelectionIndicator() {\n    return this._hideSingleSelectionIndicator;\n  }\n  set hideSingleSelectionIndicator(value) {\n    this._hideSingleSelectionIndicator = value;\n    this._syncListboxProperties();\n  }\n  _hideSingleSelectionIndicator = this._defaultOptions?.hideSingleSelectionIndicator ?? false;\n  /** Combined stream of all of the child chips' selection change events. */\n  get chipSelectionChanges() {\n    return this._getChipStream(chip => chip.selectionChange);\n  }\n  /** Combined stream of all of the child chips' blur events. */\n  get chipBlurChanges() {\n    return this._getChipStream(chip => chip._onBlur);\n  }\n  /** The value of the listbox, which is the combined value of the selected chips. */\n  get value() {\n    return this._value;\n  }\n  set value(value) {\n    if (this._chips && this._chips.length) {\n      this._setSelectionByValue(value, false);\n    }\n    this._value = value;\n  }\n  _value;\n  /** Event emitted when the selected chip listbox value has been changed by the user. */\n  change = new EventEmitter();\n  _chips = undefined;\n  ngAfterContentInit() {\n    this._chips.changes.pipe(startWith(null), takeUntil(this._destroyed)).subscribe(() => {\n      if (this.value !== undefined) {\n        Promise.resolve().then(() => {\n          this._setSelectionByValue(this.value, false);\n        });\n      }\n      // Update listbox selectable/multiple properties on chips\n      this._syncListboxProperties();\n    });\n    this.chipBlurChanges.pipe(takeUntil(this._destroyed)).subscribe(() => this._blur());\n    this.chipSelectionChanges.pipe(takeUntil(this._destroyed)).subscribe(event => {\n      if (!this.multiple) {\n        this._chips.forEach(chip => {\n          if (chip !== event.source) {\n            chip._setSelectedState(false, false, false);\n          }\n        });\n      }\n      if (event.isUserInput) {\n        this._propagateChanges();\n      }\n    });\n  }\n  /**\n   * Focuses the first selected chip in this chip listbox, or the first non-disabled chip when there\n   * are no selected chips.\n   */\n  focus() {\n    if (this.disabled) {\n      return;\n    }\n    const firstSelectedChip = this._getFirstSelectedChip();\n    if (firstSelectedChip && !firstSelectedChip.disabled) {\n      firstSelectedChip.focus();\n    } else if (this._chips.length > 0) {\n      this._keyManager.setFirstItemActive();\n    } else {\n      this._elementRef.nativeElement.focus();\n    }\n  }\n  /**\n   * Implemented as part of ControlValueAccessor.\n   * @docs-private\n   */\n  writeValue(value) {\n    if (value != null) {\n      this.value = value;\n    } else {\n      this.value = undefined;\n    }\n  }\n  /**\n   * Implemented as part of ControlValueAccessor.\n   * @docs-private\n   */\n  registerOnChange(fn) {\n    this._onChange = fn;\n  }\n  /**\n   * Implemented as part of ControlValueAccessor.\n   * @docs-private\n   */\n  registerOnTouched(fn) {\n    this._onTouched = fn;\n  }\n  /**\n   * Implemented as part of ControlValueAccessor.\n   * @docs-private\n   */\n  setDisabledState(isDisabled) {\n    this.disabled = isDisabled;\n  }\n  /** Selects all chips with value. */\n  _setSelectionByValue(value, isUserInput = true) {\n    this._clearSelection();\n    if (Array.isArray(value)) {\n      value.forEach(currentValue => this._selectValue(currentValue, isUserInput));\n    } else {\n      this._selectValue(value, isUserInput);\n    }\n  }\n  /** When blurred, marks the field as touched when focus moved outside the chip listbox. */\n  _blur() {\n    if (!this.disabled) {\n      // Wait to see if focus moves to an individual chip.\n      setTimeout(() => {\n        if (!this.focused) {\n          this._markAsTouched();\n        }\n      });\n    }\n  }\n  _keydown(event) {\n    if (event.keyCode === TAB) {\n      super._allowFocusEscape();\n    }\n  }\n  /** Marks the field as touched */\n  _markAsTouched() {\n    this._onTouched();\n    this._changeDetectorRef.markForCheck();\n  }\n  /** Emits change event to set the model value. */\n  _propagateChanges() {\n    let valueToEmit = null;\n    if (Array.isArray(this.selected)) {\n      valueToEmit = this.selected.map(chip => chip.value);\n    } else {\n      valueToEmit = this.selected ? this.selected.value : undefined;\n    }\n    this._value = valueToEmit;\n    this.change.emit(new MatChipListboxChange(this, valueToEmit));\n    this._onChange(valueToEmit);\n    this._changeDetectorRef.markForCheck();\n  }\n  /**\n   * Deselects every chip in the listbox.\n   * @param skip Chip that should not be deselected.\n   */\n  _clearSelection(skip) {\n    this._chips.forEach(chip => {\n      if (chip !== skip) {\n        chip.deselect();\n      }\n    });\n  }\n  /**\n   * Finds and selects the chip based on its value.\n   * @returns Chip that has the corresponding value.\n   */\n  _selectValue(value, isUserInput) {\n    const correspondingChip = this._chips.find(chip => {\n      return chip.value != null && this.compareWith(chip.value, value);\n    });\n    if (correspondingChip) {\n      isUserInput ? correspondingChip.selectViaInteraction() : correspondingChip.select();\n    }\n    return correspondingChip;\n  }\n  /** Syncs the chip-listbox selection state with the individual chips. */\n  _syncListboxProperties() {\n    if (this._chips) {\n      // Defer setting the value in order to avoid the \"Expression\n      // has changed after it was checked\" errors from Angular.\n      Promise.resolve().then(() => {\n        this._chips.forEach(chip => {\n          chip._chipListMultiple = this.multiple;\n          chip.chipListSelectable = this._selectable;\n          chip._chipListHideSingleSelectionIndicator = this.hideSingleSelectionIndicator;\n          chip._changeDetectorRef.markForCheck();\n        });\n      });\n    }\n  }\n  /** Returns the first selected chip in this listbox, or undefined if no chips are selected. */\n  _getFirstSelectedChip() {\n    if (Array.isArray(this.selected)) {\n      return this.selected.length ? this.selected[0] : undefined;\n    } else {\n      return this.selected;\n    }\n  }\n  /**\n   * Determines if key manager should avoid putting a given chip action in the tab index. Skip\n   * non-interactive actions since the user can't do anything with them.\n   */\n  _skipPredicate(action) {\n    // Override the skip predicate in the base class to avoid skipping disabled chips. Allow\n    // disabled chip options to receive focus to align with WAI ARIA recommendation. Normally WAI\n    // ARIA's instructions are to exclude disabled items from the tab order, but it makes a few\n    // exceptions for compound widgets.\n    //\n    // From [Developing a Keyboard Interface](\n    // https://www.w3.org/WAI/ARIA/apg/practices/keyboard-interface/):\n    //   \"For the following composite widget elements, keep them focusable when disabled: Options in a\n    //   Listbox...\"\n    return !action.isInteractive;\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMatChipListbox_BaseFactory;\n    return function MatChipListbox_Factory(__ngFactoryType__) {\n      return (ɵMatChipListbox_BaseFactory || (ɵMatChipListbox_BaseFactory = i0.ɵɵgetInheritedFactory(MatChipListbox)))(__ngFactoryType__ || MatChipListbox);\n    };\n  })();\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatChipListbox,\n    selectors: [[\"mat-chip-listbox\"]],\n    contentQueries: function MatChipListbox_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, MatChipOption, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._chips = _t);\n      }\n    },\n    hostAttrs: [1, \"mdc-evolution-chip-set\", \"mat-mdc-chip-listbox\"],\n    hostVars: 10,\n    hostBindings: function MatChipListbox_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"focus\", function MatChipListbox_focus_HostBindingHandler() {\n          return ctx.focus();\n        })(\"blur\", function MatChipListbox_blur_HostBindingHandler() {\n          return ctx._blur();\n        })(\"keydown\", function MatChipListbox_keydown_HostBindingHandler($event) {\n          return ctx._keydown($event);\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵhostProperty(\"tabIndex\", ctx.disabled || ctx.empty ? -1 : ctx.tabIndex);\n        i0.ɵɵattribute(\"role\", ctx.role)(\"aria-required\", ctx.role ? ctx.required : null)(\"aria-disabled\", ctx.disabled.toString())(\"aria-multiselectable\", ctx.multiple)(\"aria-orientation\", ctx.ariaOrientation);\n        i0.ɵɵclassProp(\"mat-mdc-chip-list-disabled\", ctx.disabled)(\"mat-mdc-chip-list-required\", ctx.required);\n      }\n    },\n    inputs: {\n      multiple: [2, \"multiple\", \"multiple\", booleanAttribute],\n      ariaOrientation: [0, \"aria-orientation\", \"ariaOrientation\"],\n      selectable: [2, \"selectable\", \"selectable\", booleanAttribute],\n      compareWith: \"compareWith\",\n      required: [2, \"required\", \"required\", booleanAttribute],\n      hideSingleSelectionIndicator: [2, \"hideSingleSelectionIndicator\", \"hideSingleSelectionIndicator\", booleanAttribute],\n      value: \"value\"\n    },\n    outputs: {\n      change: \"change\"\n    },\n    features: [i0.ɵɵProvidersFeature([MAT_CHIP_LISTBOX_CONTROL_VALUE_ACCESSOR]), i0.ɵɵInheritDefinitionFeature],\n    ngContentSelectors: _c5,\n    decls: 2,\n    vars: 0,\n    consts: [[\"role\", \"presentation\", 1, \"mdc-evolution-chip-set__chips\"]],\n    template: function MatChipListbox_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵprojection(1);\n        i0.ɵɵelementEnd();\n      }\n    },\n    styles: [_c6],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatChipListbox, [{\n    type: Component,\n    args: [{\n      selector: 'mat-chip-listbox',\n      template: `\n    <div class=\"mdc-evolution-chip-set__chips\" role=\"presentation\">\n      <ng-content></ng-content>\n    </div>\n  `,\n      host: {\n        'class': 'mdc-evolution-chip-set mat-mdc-chip-listbox',\n        '[attr.role]': 'role',\n        '[tabIndex]': '(disabled || empty) ? -1 : tabIndex',\n        '[attr.aria-required]': 'role ? required : null',\n        '[attr.aria-disabled]': 'disabled.toString()',\n        '[attr.aria-multiselectable]': 'multiple',\n        '[attr.aria-orientation]': 'ariaOrientation',\n        '[class.mat-mdc-chip-list-disabled]': 'disabled',\n        '[class.mat-mdc-chip-list-required]': 'required',\n        '(focus)': 'focus()',\n        '(blur)': '_blur()',\n        '(keydown)': '_keydown($event)'\n      },\n      providers: [MAT_CHIP_LISTBOX_CONTROL_VALUE_ACCESSOR],\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      styles: [\".mat-mdc-chip-set{display:flex}.mat-mdc-chip-set:focus{outline:none}.mat-mdc-chip-set .mdc-evolution-chip-set__chips{min-width:100%;margin-left:-8px;margin-right:0}.mat-mdc-chip-set .mdc-evolution-chip{margin:4px 0 4px 8px}[dir=rtl] .mat-mdc-chip-set .mdc-evolution-chip-set__chips{margin-left:0;margin-right:-8px}[dir=rtl] .mat-mdc-chip-set .mdc-evolution-chip{margin-left:0;margin-right:8px}.mdc-evolution-chip-set__chips{display:flex;flex-flow:wrap;min-width:0}.mat-mdc-chip-set-stacked{flex-direction:column;align-items:flex-start}.mat-mdc-chip-set-stacked .mat-mdc-chip{width:100%}.mat-mdc-chip-set-stacked .mdc-evolution-chip__graphic{flex-grow:0}.mat-mdc-chip-set-stacked .mdc-evolution-chip__action--primary{flex-basis:100%;justify-content:start}input.mat-mdc-chip-input{flex:1 0 150px;margin-left:8px}[dir=rtl] input.mat-mdc-chip-input{margin-left:0;margin-right:8px}\\n\"]\n    }]\n  }], null, {\n    multiple: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    ariaOrientation: [{\n      type: Input,\n      args: ['aria-orientation']\n    }],\n    selectable: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    compareWith: [{\n      type: Input\n    }],\n    required: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    hideSingleSelectionIndicator: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    value: [{\n      type: Input\n    }],\n    change: [{\n      type: Output\n    }],\n    _chips: [{\n      type: ContentChildren,\n      args: [MatChipOption, {\n        // We need to use `descendants: true`, because Ivy will no longer match\n        // indirect descendants if it's left as false.\n        descendants: true\n      }]\n    }]\n  });\n})();\n\n/** Change event object that is emitted when the chip grid value has changed. */\nclass MatChipGridChange {\n  source;\n  value;\n  constructor(/** Chip grid that emitted the event. */\n  source, /** Value of the chip grid when the event was emitted. */\n  value) {\n    this.source = source;\n    this.value = value;\n  }\n}\n/**\n * An extension of the MatChipSet component used with MatChipRow chips and\n * the matChipInputFor directive.\n */\nclass MatChipGrid extends MatChipSet {\n  ngControl = inject(NgControl, {\n    optional: true,\n    self: true\n  });\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  controlType = 'mat-chip-grid';\n  /** The chip input to add more chips */\n  _chipInput;\n  _defaultRole = 'grid';\n  _errorStateTracker;\n  /**\n   * List of element ids to propagate to the chipInput's aria-describedby attribute.\n   */\n  _ariaDescribedbyIds = [];\n  /**\n   * Function when touched. Set as part of ControlValueAccessor implementation.\n   * @docs-private\n   */\n  _onTouched = () => {};\n  /**\n   * Function when changed. Set as part of ControlValueAccessor implementation.\n   * @docs-private\n   */\n  _onChange = () => {};\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  get disabled() {\n    return this.ngControl ? !!this.ngControl.disabled : this._disabled;\n  }\n  set disabled(value) {\n    this._disabled = value;\n    this._syncChipsState();\n    this.stateChanges.next();\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  get id() {\n    return this._chipInput.id;\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  get empty() {\n    return (!this._chipInput || this._chipInput.empty) && (!this._chips || this._chips.length === 0);\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  get placeholder() {\n    return this._chipInput ? this._chipInput.placeholder : this._placeholder;\n  }\n  set placeholder(value) {\n    this._placeholder = value;\n    this.stateChanges.next();\n  }\n  _placeholder;\n  /** Whether any chips or the matChipInput inside of this chip-grid has focus. */\n  get focused() {\n    return this._chipInput.focused || this._hasFocusedChip();\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  get required() {\n    return this._required ?? this.ngControl?.control?.hasValidator(Validators.required) ?? false;\n  }\n  set required(value) {\n    this._required = value;\n    this.stateChanges.next();\n  }\n  _required;\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  get shouldLabelFloat() {\n    return !this.empty || this.focused;\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  get value() {\n    return this._value;\n  }\n  set value(value) {\n    this._value = value;\n  }\n  _value = [];\n  /** An object used to control when error messages are shown. */\n  get errorStateMatcher() {\n    return this._errorStateTracker.matcher;\n  }\n  set errorStateMatcher(value) {\n    this._errorStateTracker.matcher = value;\n  }\n  /** Combined stream of all of the child chips' blur events. */\n  get chipBlurChanges() {\n    return this._getChipStream(chip => chip._onBlur);\n  }\n  /** Emits when the chip grid value has been changed by the user. */\n  change = new EventEmitter();\n  /**\n   * Emits whenever the raw value of the chip-grid changes. This is here primarily\n   * to facilitate the two-way binding for the `value` input.\n   * @docs-private\n   */\n  valueChange = new EventEmitter();\n  _chips = undefined;\n  /**\n   * Emits whenever the component state changes and should cause the parent\n   * form-field to update. Implemented as part of `MatFormFieldControl`.\n   * @docs-private\n   */\n  stateChanges = new Subject();\n  /** Whether the chip grid is in an error state. */\n  get errorState() {\n    return this._errorStateTracker.errorState;\n  }\n  set errorState(value) {\n    this._errorStateTracker.errorState = value;\n  }\n  constructor() {\n    super();\n    const parentForm = inject(NgForm, {\n      optional: true\n    });\n    const parentFormGroup = inject(FormGroupDirective, {\n      optional: true\n    });\n    const defaultErrorStateMatcher = inject(ErrorStateMatcher);\n    if (this.ngControl) {\n      this.ngControl.valueAccessor = this;\n    }\n    this._errorStateTracker = new _ErrorStateTracker(defaultErrorStateMatcher, this.ngControl, parentFormGroup, parentForm, this.stateChanges);\n  }\n  ngAfterContentInit() {\n    this.chipBlurChanges.pipe(takeUntil(this._destroyed)).subscribe(() => {\n      this._blur();\n      this.stateChanges.next();\n    });\n    merge(this.chipFocusChanges, this._chips.changes).pipe(takeUntil(this._destroyed)).subscribe(() => this.stateChanges.next());\n  }\n  ngAfterViewInit() {\n    super.ngAfterViewInit();\n    if (!this._chipInput && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('mat-chip-grid must be used in combination with matChipInputFor.');\n    }\n  }\n  ngDoCheck() {\n    if (this.ngControl) {\n      // We need to re-evaluate this on every change detection cycle, because there are some\n      // error triggers that we can't subscribe to (e.g. parent form submissions). This means\n      // that whatever logic is in here has to be super lean or we risk destroying the performance.\n      this.updateErrorState();\n    }\n  }\n  ngOnDestroy() {\n    super.ngOnDestroy();\n    this.stateChanges.complete();\n  }\n  /** Associates an HTML input element with this chip grid. */\n  registerInput(inputElement) {\n    this._chipInput = inputElement;\n    this._chipInput.setDescribedByIds(this._ariaDescribedbyIds);\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  onContainerClick(event) {\n    if (!this.disabled && !this._originatesFromChip(event)) {\n      this.focus();\n    }\n  }\n  /**\n   * Focuses the first chip in this chip grid, or the associated input when there\n   * are no eligible chips.\n   */\n  focus() {\n    if (this.disabled || this._chipInput.focused) {\n      return;\n    }\n    if (!this._chips.length || this._chips.first.disabled) {\n      // Delay until the next tick, because this can cause a \"changed after checked\"\n      // error if the input does something on focus (e.g. opens an autocomplete).\n      Promise.resolve().then(() => this._chipInput.focus());\n    } else {\n      const activeItem = this._keyManager.activeItem;\n      if (activeItem) {\n        activeItem.focus();\n      } else {\n        this._keyManager.setFirstItemActive();\n      }\n    }\n    this.stateChanges.next();\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  setDescribedByIds(ids) {\n    // We must keep this up to date to handle the case where ids are set\n    // before the chip input is registered.\n    this._ariaDescribedbyIds = ids;\n    this._chipInput?.setDescribedByIds(ids);\n  }\n  /**\n   * Implemented as part of ControlValueAccessor.\n   * @docs-private\n   */\n  writeValue(value) {\n    // The user is responsible for creating the child chips, so we just store the value.\n    this._value = value;\n  }\n  /**\n   * Implemented as part of ControlValueAccessor.\n   * @docs-private\n   */\n  registerOnChange(fn) {\n    this._onChange = fn;\n  }\n  /**\n   * Implemented as part of ControlValueAccessor.\n   * @docs-private\n   */\n  registerOnTouched(fn) {\n    this._onTouched = fn;\n  }\n  /**\n   * Implemented as part of ControlValueAccessor.\n   * @docs-private\n   */\n  setDisabledState(isDisabled) {\n    this.disabled = isDisabled;\n    this.stateChanges.next();\n  }\n  /** Refreshes the error state of the chip grid. */\n  updateErrorState() {\n    this._errorStateTracker.updateErrorState();\n  }\n  /** When blurred, mark the field as touched when focus moved outside the chip grid. */\n  _blur() {\n    if (!this.disabled) {\n      // Check whether the focus moved to chip input.\n      // If the focus is not moved to chip input, mark the field as touched. If the focus moved\n      // to chip input, do nothing.\n      // Timeout is needed to wait for the focus() event trigger on chip input.\n      setTimeout(() => {\n        if (!this.focused) {\n          this._propagateChanges();\n          this._markAsTouched();\n        }\n      });\n    }\n  }\n  /**\n   * Removes the `tabindex` from the chip grid and resets it back afterwards, allowing the\n   * user to tab out of it. This prevents the grid from capturing focus and redirecting\n   * it back to the first chip, creating a focus trap, if it user tries to tab away.\n   */\n  _allowFocusEscape() {\n    if (!this._chipInput.focused) {\n      super._allowFocusEscape();\n    }\n  }\n  /** Handles custom keyboard events. */\n  _handleKeydown(event) {\n    const keyCode = event.keyCode;\n    const activeItem = this._keyManager.activeItem;\n    if (keyCode === TAB) {\n      if (this._chipInput.focused && hasModifierKey(event, 'shiftKey') && this._chips.length && !this._chips.last.disabled) {\n        event.preventDefault();\n        if (activeItem) {\n          this._keyManager.setActiveItem(activeItem);\n        } else {\n          this._focusLastChip();\n        }\n      } else {\n        // Use the super method here since it doesn't check for the input\n        // focused state. This allows focus to escape if there's only one\n        // disabled chip left in the list.\n        super._allowFocusEscape();\n      }\n    } else if (!this._chipInput.focused) {\n      // The up and down arrows are supposed to navigate between the individual rows in the grid.\n      // We do this by filtering the actions down to the ones that have the same `_isPrimary`\n      // flag as the active action and moving focus between them ourseles instead of delegating\n      // to the key manager. For more information, see #29359 and:\n      // https://www.w3.org/WAI/ARIA/apg/patterns/grid/examples/layout-grids/#ex2_label\n      if ((keyCode === UP_ARROW || keyCode === DOWN_ARROW) && activeItem) {\n        const eligibleActions = this._chipActions.filter(action => action._isPrimary === activeItem._isPrimary && !this._skipPredicate(action));\n        const currentIndex = eligibleActions.indexOf(activeItem);\n        const delta = event.keyCode === UP_ARROW ? -1 : 1;\n        event.preventDefault();\n        if (currentIndex > -1 && this._isValidIndex(currentIndex + delta)) {\n          this._keyManager.setActiveItem(eligibleActions[currentIndex + delta]);\n        }\n      } else {\n        super._handleKeydown(event);\n      }\n    }\n    this.stateChanges.next();\n  }\n  _focusLastChip() {\n    if (this._chips.length) {\n      this._chips.last.focus();\n    }\n  }\n  /** Emits change event to set the model value. */\n  _propagateChanges() {\n    const valueToEmit = this._chips.length ? this._chips.toArray().map(chip => chip.value) : [];\n    this._value = valueToEmit;\n    this.change.emit(new MatChipGridChange(this, valueToEmit));\n    this.valueChange.emit(valueToEmit);\n    this._onChange(valueToEmit);\n    this._changeDetectorRef.markForCheck();\n  }\n  /** Mark the field as touched */\n  _markAsTouched() {\n    this._onTouched();\n    this._changeDetectorRef.markForCheck();\n    this.stateChanges.next();\n  }\n  static ɵfac = function MatChipGrid_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatChipGrid)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatChipGrid,\n    selectors: [[\"mat-chip-grid\"]],\n    contentQueries: function MatChipGrid_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, MatChipRow, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._chips = _t);\n      }\n    },\n    hostAttrs: [1, \"mat-mdc-chip-set\", \"mat-mdc-chip-grid\", \"mdc-evolution-chip-set\"],\n    hostVars: 10,\n    hostBindings: function MatChipGrid_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"focus\", function MatChipGrid_focus_HostBindingHandler() {\n          return ctx.focus();\n        })(\"blur\", function MatChipGrid_blur_HostBindingHandler() {\n          return ctx._blur();\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵattribute(\"role\", ctx.role)(\"tabindex\", ctx.disabled || ctx._chips && ctx._chips.length === 0 ? -1 : ctx.tabIndex)(\"aria-disabled\", ctx.disabled.toString())(\"aria-invalid\", ctx.errorState);\n        i0.ɵɵclassProp(\"mat-mdc-chip-list-disabled\", ctx.disabled)(\"mat-mdc-chip-list-invalid\", ctx.errorState)(\"mat-mdc-chip-list-required\", ctx.required);\n      }\n    },\n    inputs: {\n      disabled: [2, \"disabled\", \"disabled\", booleanAttribute],\n      placeholder: \"placeholder\",\n      required: [2, \"required\", \"required\", booleanAttribute],\n      value: \"value\",\n      errorStateMatcher: \"errorStateMatcher\"\n    },\n    outputs: {\n      change: \"change\",\n      valueChange: \"valueChange\"\n    },\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MatFormFieldControl,\n      useExisting: MatChipGrid\n    }]), i0.ɵɵInheritDefinitionFeature],\n    ngContentSelectors: _c5,\n    decls: 2,\n    vars: 0,\n    consts: [[\"role\", \"presentation\", 1, \"mdc-evolution-chip-set__chips\"]],\n    template: function MatChipGrid_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵprojection(1);\n        i0.ɵɵelementEnd();\n      }\n    },\n    styles: [_c6],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatChipGrid, [{\n    type: Component,\n    args: [{\n      selector: 'mat-chip-grid',\n      template: `\n    <div class=\"mdc-evolution-chip-set__chips\" role=\"presentation\">\n      <ng-content></ng-content>\n    </div>\n  `,\n      host: {\n        'class': 'mat-mdc-chip-set mat-mdc-chip-grid mdc-evolution-chip-set',\n        '[attr.role]': 'role',\n        '[attr.tabindex]': '(disabled || (_chips && _chips.length === 0)) ? -1 : tabIndex',\n        '[attr.aria-disabled]': 'disabled.toString()',\n        '[attr.aria-invalid]': 'errorState',\n        '[class.mat-mdc-chip-list-disabled]': 'disabled',\n        '[class.mat-mdc-chip-list-invalid]': 'errorState',\n        '[class.mat-mdc-chip-list-required]': 'required',\n        '(focus)': 'focus()',\n        '(blur)': '_blur()'\n      },\n      providers: [{\n        provide: MatFormFieldControl,\n        useExisting: MatChipGrid\n      }],\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      styles: [\".mat-mdc-chip-set{display:flex}.mat-mdc-chip-set:focus{outline:none}.mat-mdc-chip-set .mdc-evolution-chip-set__chips{min-width:100%;margin-left:-8px;margin-right:0}.mat-mdc-chip-set .mdc-evolution-chip{margin:4px 0 4px 8px}[dir=rtl] .mat-mdc-chip-set .mdc-evolution-chip-set__chips{margin-left:0;margin-right:-8px}[dir=rtl] .mat-mdc-chip-set .mdc-evolution-chip{margin-left:0;margin-right:8px}.mdc-evolution-chip-set__chips{display:flex;flex-flow:wrap;min-width:0}.mat-mdc-chip-set-stacked{flex-direction:column;align-items:flex-start}.mat-mdc-chip-set-stacked .mat-mdc-chip{width:100%}.mat-mdc-chip-set-stacked .mdc-evolution-chip__graphic{flex-grow:0}.mat-mdc-chip-set-stacked .mdc-evolution-chip__action--primary{flex-basis:100%;justify-content:start}input.mat-mdc-chip-input{flex:1 0 150px;margin-left:8px}[dir=rtl] input.mat-mdc-chip-input{margin-left:0;margin-right:8px}\\n\"]\n    }]\n  }], () => [], {\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    placeholder: [{\n      type: Input\n    }],\n    required: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    value: [{\n      type: Input\n    }],\n    errorStateMatcher: [{\n      type: Input\n    }],\n    change: [{\n      type: Output\n    }],\n    valueChange: [{\n      type: Output\n    }],\n    _chips: [{\n      type: ContentChildren,\n      args: [MatChipRow, {\n        // We need to use `descendants: true`, because Ivy will no longer match\n        // indirect descendants if it's left as false.\n        descendants: true\n      }]\n    }]\n  });\n})();\n\n/**\n * Directive that adds chip-specific behaviors to an input element inside `<mat-form-field>`.\n * May be placed inside or outside of a `<mat-chip-grid>`.\n */\nclass MatChipInput {\n  _elementRef = inject(ElementRef);\n  /** Whether the control is focused. */\n  focused = false;\n  /** Register input for chip list */\n  get chipGrid() {\n    return this._chipGrid;\n  }\n  set chipGrid(value) {\n    if (value) {\n      this._chipGrid = value;\n      this._chipGrid.registerInput(this);\n    }\n  }\n  _chipGrid;\n  /**\n   * Whether or not the chipEnd event will be emitted when the input is blurred.\n   */\n  addOnBlur = false;\n  /**\n   * The list of key codes that will trigger a chipEnd event.\n   *\n   * Defaults to `[ENTER]`.\n   */\n  separatorKeyCodes;\n  /** Emitted when a chip is to be added. */\n  chipEnd = new EventEmitter();\n  /** The input's placeholder text. */\n  placeholder = '';\n  /** Unique id for the input. */\n  id = inject(_IdGenerator).getId('mat-mdc-chip-list-input-');\n  /** Whether the input is disabled. */\n  get disabled() {\n    return this._disabled || this._chipGrid && this._chipGrid.disabled;\n  }\n  set disabled(value) {\n    this._disabled = value;\n  }\n  _disabled = false;\n  /** Whether the input is empty. */\n  get empty() {\n    return !this.inputElement.value;\n  }\n  /** The native input element to which this directive is attached. */\n  inputElement;\n  constructor() {\n    const defaultOptions = inject(MAT_CHIPS_DEFAULT_OPTIONS);\n    const formField = inject(MAT_FORM_FIELD, {\n      optional: true\n    });\n    this.inputElement = this._elementRef.nativeElement;\n    this.separatorKeyCodes = defaultOptions.separatorKeyCodes;\n    if (formField) {\n      this.inputElement.classList.add('mat-mdc-form-field-input-control');\n    }\n  }\n  ngOnChanges() {\n    this._chipGrid.stateChanges.next();\n  }\n  ngOnDestroy() {\n    this.chipEnd.complete();\n  }\n  /** Utility method to make host definition/tests more clear. */\n  _keydown(event) {\n    if (this.empty && event.keyCode === BACKSPACE) {\n      // Ignore events where the user is holding down backspace\n      // so that we don't accidentally remove too many chips.\n      if (!event.repeat) {\n        this._chipGrid._focusLastChip();\n      }\n      event.preventDefault();\n    } else {\n      this._emitChipEnd(event);\n    }\n  }\n  /** Checks to see if the blur should emit the (chipEnd) event. */\n  _blur() {\n    if (this.addOnBlur) {\n      this._emitChipEnd();\n    }\n    this.focused = false;\n    // Blur the chip list if it is not focused\n    if (!this._chipGrid.focused) {\n      this._chipGrid._blur();\n    }\n    this._chipGrid.stateChanges.next();\n  }\n  _focus() {\n    this.focused = true;\n    this._chipGrid.stateChanges.next();\n  }\n  /** Checks to see if the (chipEnd) event needs to be emitted. */\n  _emitChipEnd(event) {\n    if (!event || this._isSeparatorKey(event) && !event.repeat) {\n      this.chipEnd.emit({\n        input: this.inputElement,\n        value: this.inputElement.value,\n        chipInput: this\n      });\n      event?.preventDefault();\n    }\n  }\n  _onInput() {\n    // Let chip list know whenever the value changes.\n    this._chipGrid.stateChanges.next();\n  }\n  /** Focuses the input. */\n  focus() {\n    this.inputElement.focus();\n  }\n  /** Clears the input */\n  clear() {\n    this.inputElement.value = '';\n  }\n  setDescribedByIds(ids) {\n    const element = this._elementRef.nativeElement;\n    // Set the value directly in the DOM since this binding\n    // is prone to \"changed after checked\" errors.\n    if (ids.length) {\n      element.setAttribute('aria-describedby', ids.join(' '));\n    } else {\n      element.removeAttribute('aria-describedby');\n    }\n  }\n  /** Checks whether a keycode is one of the configured separators. */\n  _isSeparatorKey(event) {\n    return !hasModifierKey(event) && new Set(this.separatorKeyCodes).has(event.keyCode);\n  }\n  static ɵfac = function MatChipInput_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatChipInput)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatChipInput,\n    selectors: [[\"input\", \"matChipInputFor\", \"\"]],\n    hostAttrs: [1, \"mat-mdc-chip-input\", \"mat-mdc-input-element\", \"mdc-text-field__input\", \"mat-input-element\"],\n    hostVars: 6,\n    hostBindings: function MatChipInput_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"keydown\", function MatChipInput_keydown_HostBindingHandler($event) {\n          return ctx._keydown($event);\n        })(\"blur\", function MatChipInput_blur_HostBindingHandler() {\n          return ctx._blur();\n        })(\"focus\", function MatChipInput_focus_HostBindingHandler() {\n          return ctx._focus();\n        })(\"input\", function MatChipInput_input_HostBindingHandler() {\n          return ctx._onInput();\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵhostProperty(\"id\", ctx.id);\n        i0.ɵɵattribute(\"disabled\", ctx.disabled || null)(\"placeholder\", ctx.placeholder || null)(\"aria-invalid\", ctx._chipGrid && ctx._chipGrid.ngControl ? ctx._chipGrid.ngControl.invalid : null)(\"aria-required\", ctx._chipGrid && ctx._chipGrid.required || null)(\"required\", ctx._chipGrid && ctx._chipGrid.required || null);\n      }\n    },\n    inputs: {\n      chipGrid: [0, \"matChipInputFor\", \"chipGrid\"],\n      addOnBlur: [2, \"matChipInputAddOnBlur\", \"addOnBlur\", booleanAttribute],\n      separatorKeyCodes: [0, \"matChipInputSeparatorKeyCodes\", \"separatorKeyCodes\"],\n      placeholder: \"placeholder\",\n      id: \"id\",\n      disabled: [2, \"disabled\", \"disabled\", booleanAttribute]\n    },\n    outputs: {\n      chipEnd: \"matChipInputTokenEnd\"\n    },\n    exportAs: [\"matChipInput\", \"matChipInputFor\"],\n    features: [i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatChipInput, [{\n    type: Directive,\n    args: [{\n      selector: 'input[matChipInputFor]',\n      exportAs: 'matChipInput, matChipInputFor',\n      host: {\n        // TODO: eventually we should remove `mat-input-element` from here since it comes from the\n        // non-MDC version of the input. It's currently being kept for backwards compatibility, because\n        // the MDC chips were landed initially with it.\n        'class': 'mat-mdc-chip-input mat-mdc-input-element mdc-text-field__input mat-input-element',\n        '(keydown)': '_keydown($event)',\n        '(blur)': '_blur()',\n        '(focus)': '_focus()',\n        '(input)': '_onInput()',\n        '[id]': 'id',\n        '[attr.disabled]': 'disabled || null',\n        '[attr.placeholder]': 'placeholder || null',\n        '[attr.aria-invalid]': '_chipGrid && _chipGrid.ngControl ? _chipGrid.ngControl.invalid : null',\n        '[attr.aria-required]': '_chipGrid && _chipGrid.required || null',\n        '[attr.required]': '_chipGrid && _chipGrid.required || null'\n      }\n    }]\n  }], () => [], {\n    chipGrid: [{\n      type: Input,\n      args: ['matChipInputFor']\n    }],\n    addOnBlur: [{\n      type: Input,\n      args: [{\n        alias: 'matChipInputAddOnBlur',\n        transform: booleanAttribute\n      }]\n    }],\n    separatorKeyCodes: [{\n      type: Input,\n      args: ['matChipInputSeparatorKeyCodes']\n    }],\n    chipEnd: [{\n      type: Output,\n      args: ['matChipInputTokenEnd']\n    }],\n    placeholder: [{\n      type: Input\n    }],\n    id: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\nconst CHIP_DECLARATIONS = [MatChip, MatChipAvatar, MatChipEditInput, MatChipGrid, MatChipInput, MatChipListbox, MatChipOption, MatChipRemove, MatChipRow, MatChipSet, MatChipTrailingIcon];\nclass MatChipsModule {\n  static ɵfac = function MatChipsModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatChipsModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatChipsModule,\n    imports: [MatCommonModule, MatRippleModule, MatChipAction, MatChip, MatChipAvatar, MatChipEditInput, MatChipGrid, MatChipInput, MatChipListbox, MatChipOption, MatChipRemove, MatChipRow, MatChipSet, MatChipTrailingIcon],\n    exports: [MatCommonModule, MatChip, MatChipAvatar, MatChipEditInput, MatChipGrid, MatChipInput, MatChipListbox, MatChipOption, MatChipRemove, MatChipRow, MatChipSet, MatChipTrailingIcon]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [ErrorStateMatcher, {\n      provide: MAT_CHIPS_DEFAULT_OPTIONS,\n      useValue: {\n        separatorKeyCodes: [ENTER]\n      }\n    }],\n    imports: [MatCommonModule, MatRippleModule, MatCommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatChipsModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, MatRippleModule, MatChipAction, CHIP_DECLARATIONS],\n      exports: [MatCommonModule, CHIP_DECLARATIONS],\n      providers: [ErrorStateMatcher, {\n        provide: MAT_CHIPS_DEFAULT_OPTIONS,\n        useValue: {\n          separatorKeyCodes: [ENTER]\n        }\n      }]\n    }]\n  }], null, null);\n})();\nexport { MAT_CHIP, MAT_CHIPS_DEFAULT_OPTIONS, MAT_CHIP_AVATAR, MAT_CHIP_LISTBOX_CONTROL_VALUE_ACCESSOR, MAT_CHIP_REMOVE, MAT_CHIP_TRAILING_ICON, MatChip, MatChipAvatar, MatChipEditInput, MatChipGrid, MatChipGridChange, MatChipInput, MatChipListbox, MatChipListboxChange, MatChipOption, MatChipRemove, MatChipRow, MatChipSelectionChange, MatChipSet, MatChipTrailingIcon, MatChipsModule };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuBA,IAAM,MAAM,CAAC,KAAK,CAAC,CAAC,iBAAiB,GAAG,CAAC,IAAI,iBAAiB,EAAE,CAAC,GAAG,CAAC,CAAC,wBAAwB,GAAG,CAAC,IAAI,iBAAiB,EAAE,GAAG,CAAC,IAAI,uBAAuB,EAAE,CAAC,CAAC;AAC5J,IAAM,MAAM,CAAC,KAAK,oCAAoC,8DAA8D;AACpH,SAAS,+BAA+B,IAAI,KAAK;AAC/C,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,aAAa,GAAG,CAAC;AACpB,IAAG,aAAa;AAAA,EAClB;AACF;AACA,SAAS,+BAA+B,IAAI,KAAK;AAC/C,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,aAAa,GAAG,CAAC;AACpB,IAAG,aAAa;AAAA,EAClB;AACF;AACA,SAAS,qCAAqC,IAAI,KAAK;AACrD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,aAAa,GAAG,CAAC;AACpB,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,eAAe;AAClB,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,UAAU,GAAG,QAAQ,EAAE;AAC1B,IAAG,aAAa,EAAE,EAAE;AAAA,EACtB;AACF;AACA,SAAS,qCAAqC,IAAI,KAAK;AACrD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,aAAa,GAAG,CAAC;AACpB,IAAG,aAAa;AAAA,EAClB;AACF;AACA,IAAM,MAAM;AACZ,IAAM,MAAM,CAAC,CAAC,CAAC,iBAAiB,GAAG,CAAC,IAAI,iBAAiB,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,oBAAoB,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,wBAAwB,GAAG,CAAC,IAAI,iBAAiB,EAAE,GAAG,CAAC,IAAI,uBAAuB,EAAE,CAAC,CAAC;AAC5L,IAAM,MAAM,CAAC,oCAAoC,sBAAsB,KAAK,8DAA8D;AAC1I,SAAS,kCAAkC,IAAI,KAAK;AAClD,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,CAAC;AAAA,EAC3B;AACF;AACA,SAAS,kCAAkC,IAAI,KAAK;AAClD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,aAAa,CAAC;AACjB,IAAG,aAAa;AAAA,EAClB;AACF;AACA,SAAS,gDAAgD,IAAI,KAAK;AAChE,MAAI,KAAK,GAAG;AACV,IAAG,aAAa,GAAG,CAAC;AAAA,EACtB;AACF;AACA,SAAS,gDAAgD,IAAI,KAAK;AAChE,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,CAAC;AAAA,EAC3B;AACF;AACA,SAAS,kCAAkC,IAAI,KAAK;AAClD,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,iDAAiD,GAAG,CAAC,EAAE,GAAG,iDAAiD,GAAG,GAAG,QAAQ,CAAC;AAAA,EAC7I;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,cAAc,OAAO,mBAAmB,IAAI,CAAC;AAAA,EAClD;AACF;AACA,SAAS,kCAAkC,IAAI,KAAK;AAClD,MAAI,KAAK,GAAG;AACV,IAAG,aAAa,GAAG,CAAC;AAAA,EACtB;AACF;AACA,SAAS,kCAAkC,IAAI,KAAK;AAClD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,aAAa,GAAG,CAAC;AACpB,IAAG,aAAa;AAAA,EAClB;AACF;AACA,IAAM,MAAM,CAAC,GAAG;AAChB,IAAM,MAAM;AACZ,IAAM,4BAA4B,IAAI,eAAe,6BAA6B;AAAA,EAChF,YAAY;AAAA,EACZ,SAAS,OAAO;AAAA,IACd,mBAAmB,CAAC,KAAK;AAAA,EAC3B;AACF,CAAC;AAMD,IAAM,kBAAkB,IAAI,eAAe,eAAe;AAM1D,IAAM,yBAAyB,IAAI,eAAe,qBAAqB;AAMvE,IAAM,kBAAkB,IAAI,eAAe,eAAe;AAI1D,IAAM,WAAW,IAAI,eAAe,SAAS;AAM7C,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,cAAc,OAAO,UAAU;AAAA,EAC/B,cAAc,OAAO,QAAQ;AAAA;AAAA,EAE7B,gBAAgB;AAAA;AAAA,EAEhB,aAAa;AAAA;AAAA,EAEb,IAAI,WAAW;AACb,WAAO,KAAK,aAAa,KAAK,aAAa,YAAY;AAAA,EACzD;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,YAAY;AAAA;AAAA,EAEZ,WAAW;AAAA;AAAA;AAAA;AAAA,EAIX,0BAA0B;AAAA;AAAA;AAAA;AAAA,EAI1B,wBAAwB;AAGtB,WAAO,KAAK,YAAY,CAAC,KAAK,0BAA0B,KAAK;AAAA,EAC/D;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe;AACb,WAAO,KAAK,YAAY,CAAC,KAAK,2BAA2B,CAAC,KAAK,gBAAgB,OAAO,KAAK,SAAS,SAAS;AAAA,EAC/G;AAAA,EACA,cAAc;AACZ,WAAO,sBAAsB,EAAE,KAAK,uBAAuB;AAC3D,QAAI,KAAK,YAAY,cAAc,aAAa,UAAU;AACxD,WAAK,YAAY,cAAc,aAAa,QAAQ,QAAQ;AAAA,IAC9D;AAAA,EACF;AAAA,EACA,QAAQ;AACN,SAAK,YAAY,cAAc,MAAM;AAAA,EACvC;AAAA,EACA,aAAa,OAAO;AAClB,QAAI,CAAC,KAAK,YAAY,KAAK,iBAAiB,KAAK,YAAY;AAC3D,YAAM,eAAe;AACrB,WAAK,YAAY,gCAAgC;AAAA,IACnD;AAAA,EACF;AAAA,EACA,eAAe,OAAO;AACpB,SAAK,MAAM,YAAY,SAAS,MAAM,YAAY,UAAU,CAAC,KAAK,YAAY,KAAK,iBAAiB,KAAK,cAAc,CAAC,KAAK,YAAY,YAAY;AACnJ,YAAM,eAAe;AACrB,WAAK,YAAY,gCAAgC;AAAA,IACnD;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,sBAAsB,mBAAmB;AAC9D,WAAO,KAAK,qBAAqB,gBAAe;AAAA,EAClD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,iBAAiB,EAAE,CAAC;AAAA,IACrC,WAAW,CAAC,GAAG,8BAA8B,qBAAqB;AAAA,IAClE,UAAU;AAAA,IACV,cAAc,SAAS,2BAA2B,IAAI,KAAK;AACzD,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,SAAS,SAAS,uCAAuC,QAAQ;AAC7E,iBAAO,IAAI,aAAa,MAAM;AAAA,QAChC,CAAC,EAAE,WAAW,SAAS,yCAAyC,QAAQ;AACtE,iBAAO,IAAI,eAAe,MAAM;AAAA,QAClC,CAAC;AAAA,MACH;AACA,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,YAAY,IAAI,aAAa,CAAC,EAAE,YAAY,IAAI,sBAAsB,CAAC,EAAE,iBAAiB,IAAI,QAAQ;AACrH,QAAG,YAAY,uCAAuC,IAAI,UAAU,EAAE,8CAA8C,CAAC,IAAI,aAAa,EAAE,wCAAwC,CAAC,IAAI,UAAU;AAAA,MACjM;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,eAAe;AAAA,MACf,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,MACtD,UAAU,CAAC,GAAG,YAAY,YAAY,WAAS,SAAS,OAAO,KAAK,gBAAgB,KAAK,CAAC;AAAA,MAC1F,yBAAyB;AAAA,IAC3B;AAAA,EACF,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,+CAA+C;AAAA,QAC/C,sDAAsD;AAAA,QACtD,gDAAgD;AAAA,QAChD,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,WAAW;AAAA,QACX,aAAa;AAAA,MACf;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW,WAAS,SAAS,OAAO,KAAK,gBAAgB,KAAK;AAAA,MAChE,CAAC;AAAA,IACH,CAAC;AAAA,IACD,yBAAyB,CAAC;AAAA,MACxB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAGH,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,OAAO,OAAO,SAAS,sBAAsB,mBAAmB;AAC9D,WAAO,KAAK,qBAAqB,gBAAe;AAAA,EAClD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,iBAAiB,GAAG,CAAC,IAAI,iBAAiB,EAAE,CAAC;AAAA,IAC1D,WAAW,CAAC,QAAQ,OAAO,GAAG,uBAAuB,4BAA4B,mCAAmC;AAAA,IACpH,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,CAAC;AAAA,EACL,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,QAAQ;AAAA,MACV;AAAA,MACA,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAEH,IAAM,sBAAN,MAAM,6BAA4B,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,EAK9C,gBAAgB;AAAA,EAChB,aAAa;AAAA,EACb,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,4BAA4B,mBAAmB;AAC7D,cAAQ,qCAAqC,mCAAsC,sBAAsB,oBAAmB,IAAI,qBAAqB,oBAAmB;AAAA,IAC1K;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,wBAAwB,GAAG,CAAC,IAAI,uBAAuB,EAAE,CAAC;AAAA,IACvE,WAAW,CAAC,eAAe,QAAQ,GAAG,8BAA8B,4BAA4B,oCAAoC;AAAA,IACpI,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,GAAM,0BAA0B;AAAA,EACpC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,eAAe;AAAA,MACjB;AAAA,MACA,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAgBH,IAAM,gBAAN,MAAM,uBAAsB,cAAc;AAAA,EACxC,aAAa;AAAA,EACb,aAAa,OAAO;AAClB,QAAI,CAAC,KAAK,UAAU;AAClB,YAAM,gBAAgB;AACtB,YAAM,eAAe;AACrB,WAAK,YAAY,OAAO;AAAA,IAC1B;AAAA,EACF;AAAA,EACA,eAAe,OAAO;AACpB,SAAK,MAAM,YAAY,SAAS,MAAM,YAAY,UAAU,CAAC,KAAK,UAAU;AAC1E,YAAM,gBAAgB;AACtB,YAAM,eAAe;AACrB,WAAK,YAAY,OAAO;AAAA,IAC1B;AAAA,EACF;AAAA,EACA,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,sBAAsB,mBAAmB;AACvD,cAAQ,+BAA+B,6BAAgC,sBAAsB,cAAa,IAAI,qBAAqB,cAAa;AAAA,IAClJ;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,iBAAiB,EAAE,CAAC;AAAA,IACrC,WAAW,CAAC,QAAQ,UAAU,GAAG,uBAAuB,8BAA8B,uBAAuB,4BAA4B,oCAAoC;AAAA,IAC7K,UAAU;AAAA,IACV,cAAc,SAAS,2BAA2B,IAAI,KAAK;AACzD,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,eAAe,IAAI;AAAA,MACpC;AAAA,IACF;AAAA,IACA,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,GAAM,0BAA0B;AAAA,EACpC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,sBAAsB;AAAA,MACxB;AAAA,MACA,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAOH,IAAM,UAAN,MAAM,SAAQ;AAAA,EACZ,qBAAqB,OAAO,iBAAiB;AAAA,EAC7C,cAAc,OAAO,UAAU;AAAA,EAC/B,UAAU,OAAO,MAAM;AAAA,EACvB,gBAAgB,OAAO,YAAY;AAAA,EACnC,uBAAuB,OAAO,2BAA2B;AAAA,IACvD,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,YAAY,OAAO,QAAQ;AAAA;AAAA,EAE3B,WAAW,IAAI,QAAQ;AAAA;AAAA,EAEvB,UAAU,IAAI,QAAQ;AAAA;AAAA,EAEtB;AAAA;AAAA,EAEA,OAAO;AAAA;AAAA,EAEP,oBAAoB;AAAA;AAAA,EAEpB;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA,EACA,YAAY;AACV,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,KAAK,OAAO,YAAY,EAAE,MAAM,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA,EAK/C,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,EAKZ,kBAAkB;AAAA;AAAA,EAElB,qBAAqB,GAAG,KAAK,EAAE;AAAA;AAAA,EAE/B,oBAAoB;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,QAAQ;AACV,WAAO,KAAK,WAAW,SAAY,KAAK,SAAS,KAAK,aAAa,YAAY,KAAK;AAAA,EACtF;AAAA,EACA,IAAI,MAAM,OAAO;AACf,SAAK,SAAS;AAAA,EAChB;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA;AAAA;AAAA;AAAA;AAAA,EAIA,YAAY;AAAA;AAAA;AAAA;AAAA,EAIZ,cAAc;AAAA;AAAA,EAEd,gBAAgB;AAAA;AAAA,EAEhB,IAAI,WAAW;AACb,WAAO,KAAK,aAAa,KAAK;AAAA,EAChC;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,YAAY;AAAA;AAAA,EAEZ,UAAU,IAAI,aAAa;AAAA;AAAA,EAE3B,YAAY,IAAI,aAAa;AAAA;AAAA,EAE7B,oBAAoB;AAAA;AAAA,EAEpB;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB,OAAO,eAAe;AAAA,EACtC,YAAY,OAAO,QAAQ;AAAA,EAC3B,cAAc;AACZ,UAAM,cAAc,OAAO,sBAAsB;AACjD,gBAAY,KAAK,uBAAuB;AACxC,gBAAY,KAAK,qBAAqB;AACtC,UAAM,gBAAgB,OAAO,uBAAuB;AAAA,MAClD,UAAU;AAAA,IACZ,CAAC;AACD,SAAK,sBAAsB,kBAAkB;AAC7C,SAAK,cAAc;AACnB,SAAK,eAAe,gBAAgB,KAAK,YAAY,eAAe;AAAA,MAClE,WAAW;AAAA,MACX,UAAU,KAAK,kBAAkB;AAAA,IACnC,CAAC;AAAA,EACH;AAAA,EACA,WAAW;AAGT,UAAM,UAAU,KAAK,YAAY;AACjC,SAAK,eAAe,QAAQ,aAAa,KAAK,iBAAiB,KAAK,QAAQ,QAAQ,YAAY,MAAM,KAAK;AAAA,EAC7G;AAAA,EACA,kBAAkB;AAChB,SAAK,eAAe,KAAK,YAAY,cAAc,cAAc,4BAA4B;AAC7F,QAAI,KAAK,eAAe;AACtB,WAAK,gBAAgB;AACrB,WAAK,MAAM;AAAA,IACb;AAAA,EACF;AAAA,EACA,qBAAqB;AAGnB,SAAK,iBAAiB,MAAM,KAAK,iBAAiB,SAAS,KAAK,kBAAkB,SAAS,KAAK,gBAAgB,OAAO,EAAE,UAAU,MAAM,KAAK,mBAAmB,aAAa,CAAC;AAAA,EACjL;AAAA,EACA,YAAY;AACV,SAAK,cAAc,YAAY,KAAK,YAAY,eAAe,KAAK,kBAAkB,CAAC;AAAA,EACzF;AAAA,EACA,cAAc;AACZ,SAAK,cAAc,eAAe,KAAK,WAAW;AAClD,SAAK,eAAe,cAAc,KAAK,YAAY,aAAa;AAChE,SAAK,gBAAgB,YAAY;AACjC,SAAK,UAAU,KAAK;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AACD,SAAK,UAAU,SAAS;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS;AACP,QAAI,KAAK,WAAW;AAClB,WAAK,QAAQ,KAAK;AAAA,QAChB,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA,EAEA,oBAAoB;AAClB,WAAO,KAAK,YAAY,KAAK,iBAAiB,KAAK,uBAAuB,KAAK,gBAAgB,CAAC,CAAC,KAAK,sBAAsB;AAAA,EAC9H;AAAA;AAAA,EAEA,mBAAmB;AACjB,WAAO,CAAC,EAAE,KAAK,gBAAgB,KAAK;AAAA,EACtC;AAAA;AAAA,EAEA,eAAe,OAAO;AAGpB,QAAI,MAAM,YAAY,aAAa,CAAC,MAAM,UAAU,MAAM,YAAY,QAAQ;AAC5E,YAAM,eAAe;AACrB,WAAK,OAAO;AAAA,IACd;AAAA,EACF;AAAA;AAAA,EAEA,QAAQ;AACN,QAAI,CAAC,KAAK,UAAU;AAIlB,UAAI,KAAK,eAAe;AACtB,aAAK,cAAc,MAAM;AAAA,MAC3B,OAAO;AACL,aAAK,gBAAgB;AAAA,MACvB;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,iBAAiB,QAAQ;AACvB,WAAO,KAAK,YAAY,EAAE,KAAK,YAAU;AACvC,YAAM,UAAU,OAAO,YAAY;AACnC,aAAO,YAAY,UAAU,QAAQ,SAAS,MAAM;AAAA,IACtD,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,cAAc;AACZ,UAAM,SAAS,CAAC;AAChB,QAAI,KAAK,eAAe;AACtB,aAAO,KAAK,KAAK,aAAa;AAAA,IAChC;AACA,QAAI,KAAK,YAAY;AACnB,aAAO,KAAK,KAAK,UAAU;AAAA,IAC7B;AACA,QAAI,KAAK,cAAc;AACrB,aAAO,KAAK,KAAK,YAAY;AAAA,IAC/B;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,kCAAkC;AAAA,EAElC;AAAA;AAAA,EAEA,gBAAgB;AACd,SAAK,cAAc,QAAQ,KAAK,aAAa,IAAI,EAAE,UAAU,YAAU;AACrE,YAAM,WAAW,WAAW;AAC5B,UAAI,aAAa,KAAK,mBAAmB;AACvC,aAAK,oBAAoB;AACzB,YAAI,UAAU;AACZ,eAAK,SAAS,KAAK;AAAA,YACjB,MAAM;AAAA,UACR,CAAC;AAAA,QACH,OAAO;AAKL,eAAK,mBAAmB,aAAa;AACrC,qBAAW,MAAM,KAAK,QAAQ,IAAI,MAAM,KAAK,QAAQ,KAAK;AAAA,YACxD,MAAM;AAAA,UACR,CAAC,CAAC,CAAC;AAAA,QACL;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,OAAO,OAAO,SAAS,gBAAgB,mBAAmB;AACxD,WAAO,KAAK,qBAAqB,UAAS;AAAA,EAC5C;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,gBAAgB,GAAG,CAAC,IAAI,kBAAkB,EAAE,GAAG,CAAC,UAAU,GAAG,CAAC,IAAI,YAAY,EAAE,CAAC;AAAA,IAC9F,gBAAgB,SAAS,uBAAuB,IAAI,KAAK,UAAU;AACjE,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,iBAAiB,CAAC;AAC9C,QAAG,eAAe,UAAU,wBAAwB,CAAC;AACrD,QAAG,eAAe,UAAU,iBAAiB,CAAC;AAC9C,QAAG,eAAe,UAAU,iBAAiB,CAAC;AAC9C,QAAG,eAAe,UAAU,wBAAwB,CAAC;AACrD,QAAG,eAAe,UAAU,iBAAiB,CAAC;AAAA,MAChD;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,cAAc,GAAG;AAClE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,eAAe,GAAG;AACnE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,aAAa,GAAG;AACjE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,mBAAmB;AACpE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,oBAAoB;AACrE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,kBAAkB;AAAA,MACrE;AAAA,IACF;AAAA,IACA,WAAW,SAAS,cAAc,IAAI,KAAK;AACzC,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,eAAe,CAAC;AAAA,MACjC;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,gBAAgB,GAAG;AAAA,MACtE;AAAA,IACF;AAAA,IACA,WAAW,CAAC,GAAG,cAAc;AAAA,IAC7B,UAAU;AAAA,IACV,cAAc,SAAS,qBAAqB,IAAI,KAAK;AACnD,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,WAAW,SAAS,mCAAmC,QAAQ;AAC3E,iBAAO,IAAI,eAAe,MAAM;AAAA,QAClC,CAAC;AAAA,MACH;AACA,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,MAAM,IAAI,EAAE;AAC9B,QAAG,YAAY,QAAQ,IAAI,IAAI,EAAE,cAAc,IAAI,SAAS;AAC5D,QAAG,WAAW,UAAU,IAAI,SAAS,UAAU;AAC/C,QAAG,YAAY,sBAAsB,CAAC,IAAI,YAAY,EAAE,gCAAgC,IAAI,QAAQ,EAAE,4CAA4C,IAAI,iBAAiB,CAAC,EAAE,4CAA4C,IAAI,WAAW,EAAE,yCAAyC,IAAI,WAAW,EAAE,mCAAmC,IAAI,WAAW,EAAE,4BAA4B,IAAI,WAAW,EAAE,4BAA4B,IAAI,WAAW,EAAE,yBAAyB,IAAI,QAAQ,EAAE,sBAAsB,IAAI,YAAY,EAAE,yBAAyB,CAAC,IAAI,YAAY,EAAE,mCAAmC,IAAI,iBAAiB,CAAC,EAAE,2BAA2B,IAAI,mBAAmB;AAAA,MACzpB;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,MAAM;AAAA,MACN,IAAI;AAAA,MACJ,WAAW,CAAC,GAAG,cAAc,WAAW;AAAA,MACxC,iBAAiB,CAAC,GAAG,oBAAoB,iBAAiB;AAAA,MAC1D,OAAO;AAAA,MACP,OAAO;AAAA,MACP,WAAW,CAAC,GAAG,aAAa,aAAa,gBAAgB;AAAA,MACzD,aAAa,CAAC,GAAG,eAAe,eAAe,gBAAgB;AAAA,MAC/D,eAAe,CAAC,GAAG,iBAAiB,iBAAiB,gBAAgB;AAAA,MACrE,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,IACxD;AAAA,IACA,SAAS;AAAA,MACP,SAAS;AAAA,MACT,WAAW;AAAA,IACb;AAAA,IACA,UAAU,CAAC,SAAS;AAAA,IACpB,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,CAAC;AAAA,IACH,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,GAAG,4BAA4B,GAAG,CAAC,GAAG,4BAA4B,mCAAmC,GAAG,CAAC,iBAAiB,IAAI,GAAG,eAAe,GAAG,CAAC,GAAG,+BAA+B,sBAAsB,GAAG,CAAC,GAAG,kCAAkC,2BAA2B,GAAG,CAAC,GAAG,wCAAwC,qBAAqB,GAAG,CAAC,GAAG,4BAA4B,oCAAoC,CAAC;AAAA,IACta,UAAU,SAAS,iBAAiB,IAAI,KAAK;AAC3C,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB,GAAG;AACtB,QAAG,UAAU,GAAG,QAAQ,CAAC;AACzB,QAAG,eAAe,GAAG,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC;AAC5C,QAAG,WAAW,GAAG,gCAAgC,GAAG,GAAG,QAAQ,CAAC;AAChE,QAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,QAAG,aAAa,CAAC;AACjB,QAAG,UAAU,GAAG,QAAQ,CAAC;AACzB,QAAG,aAAa,EAAE,EAAE;AACpB,QAAG,WAAW,GAAG,gCAAgC,GAAG,GAAG,QAAQ,CAAC;AAAA,MAClE;AACA,UAAI,KAAK,GAAG;AACV,QAAG,UAAU,CAAC;AACd,QAAG,WAAW,iBAAiB,KAAK;AACpC,QAAG,UAAU;AACb,QAAG,cAAc,IAAI,cAAc,IAAI,EAAE;AACzC,QAAG,UAAU,CAAC;AACd,QAAG,cAAc,IAAI,iBAAiB,IAAI,IAAI,EAAE;AAAA,MAClD;AAAA,IACF;AAAA,IACA,cAAc,CAAC,aAAa;AAAA,IAC5B,QAAQ,CAAC,27hBAAi8hB;AAAA,IAC18hB,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,SAAS,CAAC;AAAA,IAChF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,WAAW;AAAA,QACX,8BAA8B;AAAA,QAC9B,wCAAwC;AAAA,QACxC,oDAAoD;AAAA,QACpD,oDAAoD;AAAA,QACpD,iDAAiD;AAAA,QACjD,2CAA2C;AAAA,QAC3C,oCAAoC;AAAA,QACpC,oCAAoC;AAAA,QACpC,iCAAiC;AAAA,QACjC,8BAA8B;AAAA,QAC9B,iCAAiC;AAAA,QACjC,2CAA2C;AAAA,QAC3C,mCAAmC;AAAA,QACnC,QAAQ;AAAA,QACR,eAAe;AAAA,QACf,qBAAqB;AAAA,QACrB,aAAa;AAAA,MACf;AAAA,MACA,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,SAAS,CAAC,aAAa;AAAA,MACvB,UAAU;AAAA,MACV,QAAQ,CAAC,27hBAAi8hB;AAAA,IAC58hB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,QACtB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,MACN,MAAM,CAAC,wBAAwB;AAAA,QAC7B,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,QACtB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,IAAI,CAAC;AAAA,MACH,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,IACrB,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,IAC3B,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,IACxB,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,sBAAsB;AAAA,IAC/B,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,IACxB,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAGH,IAAM,yBAAN,MAA6B;AAAA,EAC3B;AAAA,EACA;AAAA,EACA;AAAA,EACA,YACA,QACA,UACA,cAAc,OAAO;AACnB,SAAK,SAAS;AACd,SAAK,WAAW;AAChB,SAAK,cAAc;AAAA,EACrB;AACF;AAOA,IAAM,gBAAN,MAAM,uBAAsB,QAAQ;AAAA;AAAA,EAElC,kBAAkB,OAAO,2BAA2B;AAAA,IAClD,UAAU;AAAA,EACZ,CAAC;AAAA;AAAA,EAED,qBAAqB;AAAA;AAAA,EAErB,oBAAoB;AAAA;AAAA,EAEpB,wCAAwC,KAAK,iBAAiB,gCAAgC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQ9F,IAAI,aAAa;AACf,WAAO,KAAK,eAAe,KAAK;AAAA,EAClC;AAAA,EACA,IAAI,WAAW,OAAO;AACpB,SAAK,cAAc;AACnB,SAAK,mBAAmB,aAAa;AAAA,EACvC;AAAA,EACA,cAAc;AAAA;AAAA,EAEd,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,kBAAkB,OAAO,OAAO,IAAI;AAAA,EAC3C;AAAA,EACA,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcZ,IAAI,eAAe;AACjB,WAAO,KAAK,aAAa,KAAK,SAAS,SAAS,IAAI;AAAA,EACtD;AAAA;AAAA,EAEA,oBAAoB;AAAA;AAAA,EAEpB,kBAAkB,IAAI,aAAa;AAAA,EACnC,WAAW;AACT,UAAM,SAAS;AACf,SAAK,OAAO;AAAA,EACd;AAAA;AAAA,EAEA,SAAS;AACP,SAAK,kBAAkB,MAAM,OAAO,IAAI;AAAA,EAC1C;AAAA;AAAA,EAEA,WAAW;AACT,SAAK,kBAAkB,OAAO,OAAO,IAAI;AAAA,EAC3C;AAAA;AAAA,EAEA,uBAAuB;AACrB,SAAK,kBAAkB,MAAM,MAAM,IAAI;AAAA,EACzC;AAAA;AAAA,EAEA,eAAe,cAAc,OAAO;AAClC,SAAK,kBAAkB,CAAC,KAAK,UAAU,aAAa,IAAI;AACxD,WAAO,KAAK;AAAA,EACd;AAAA,EACA,kCAAkC;AAChC,QAAI,CAAC,KAAK,UAAU;AAIlB,WAAK,MAAM;AACX,UAAI,KAAK,YAAY;AACnB,aAAK,eAAe,IAAI;AAAA,MAC1B;AAAA,IACF;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,QAAI,KAAK,aAAa;AACpB,aAAO;AAAA,IACT;AAIA,WAAO,CAAC,KAAK,yCAAyC,KAAK;AAAA,EAC7D;AAAA,EACA,kBAAkB,YAAY,aAAa,WAAW;AACpD,QAAI,eAAe,KAAK,UAAU;AAChC,WAAK,YAAY;AACjB,UAAI,WAAW;AACb,aAAK,gBAAgB,KAAK;AAAA,UACxB,QAAQ;AAAA,UACR;AAAA,UACA,UAAU,KAAK;AAAA,QACjB,CAAC;AAAA,MACH;AACA,WAAK,mBAAmB,aAAa;AAAA,IACvC;AAAA,EACF;AAAA,EACA,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,sBAAsB,mBAAmB;AACvD,cAAQ,+BAA+B,6BAAgC,sBAAsB,cAAa,IAAI,qBAAqB,cAAa;AAAA,IAClJ;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,uBAAuB,GAAG,CAAC,IAAI,yBAAyB,EAAE,GAAG,CAAC,iBAAiB,GAAG,CAAC,IAAI,mBAAmB,EAAE,CAAC;AAAA,IAC1H,WAAW,CAAC,GAAG,gBAAgB,qBAAqB;AAAA,IACpD,UAAU;AAAA,IACV,cAAc,SAAS,2BAA2B,IAAI,KAAK;AACzD,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,MAAM,IAAI,EAAE;AAC9B,QAAG,YAAY,YAAY,IAAI,EAAE,cAAc,IAAI,EAAE,oBAAoB,IAAI,EAAE,QAAQ,IAAI,IAAI;AAC/F,QAAG,YAAY,sBAAsB,CAAC,IAAI,YAAY,EAAE,8BAA8B,CAAC,IAAI,YAAY,EAAE,kCAAkC,CAAC,IAAI,YAAY,EAAE,yBAAyB,IAAI,QAAQ,EAAE,yBAAyB,IAAI,iBAAiB,EAAE,yBAAyB,IAAI,QAAQ,EAAE,4BAA4B,IAAI,WAAW,EAAE,gCAAgC,IAAI,QAAQ,EAAE,gCAAgC,IAAI,QAAQ,EAAE,iCAAiC,CAAC,IAAI,mBAAmB,EAAE,4CAA4C,IAAI,iBAAiB,CAAC,EAAE,yCAAyC,IAAI,WAAW,EAAE,4CAA4C,IAAI,mBAAmB,CAAC,EAAE,mCAAmC,IAAI,WAAW,EAAE,4BAA4B,IAAI,WAAW,EAAE,mCAAmC,IAAI,iBAAiB,CAAC;AAAA,MACh0B;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,YAAY,CAAC,GAAG,cAAc,cAAc,gBAAgB;AAAA,MAC5D,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,IACxD;AAAA,IACA,SAAS;AAAA,MACP,iBAAiB;AAAA,IACnB;AAAA,IACA,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,GAAG;AAAA,MACD,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,GAAM,0BAA0B;AAAA,IAClC,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,GAAG,4BAA4B,GAAG,CAAC,GAAG,4BAA4B,mCAAmC,GAAG,CAAC,iBAAiB,IAAI,QAAQ,UAAU,GAAG,yBAAyB,GAAG,CAAC,GAAG,+BAA+B,sBAAsB,GAAG,CAAC,GAAG,kCAAkC,2BAA2B,GAAG,CAAC,GAAG,wCAAwC,qBAAqB,GAAG,CAAC,GAAG,4BAA4B,oCAAoC,GAAG,CAAC,GAAG,uBAAuB,GAAG,IAAI,GAAG,CAAC,GAAG,+BAA+B,GAAG,CAAC,WAAW,eAAe,aAAa,SAAS,eAAe,QAAQ,GAAG,mCAAmC,GAAG,CAAC,QAAQ,QAAQ,UAAU,gBAAgB,KAAK,oCAAoC,GAAG,oCAAoC,CAAC;AAAA,IAC5vB,UAAU,SAAS,uBAAuB,IAAI,KAAK;AACjD,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB,GAAG;AACtB,QAAG,UAAU,GAAG,QAAQ,CAAC;AACzB,QAAG,eAAe,GAAG,QAAQ,CAAC,EAAE,GAAG,UAAU,CAAC;AAC9C,QAAG,WAAW,GAAG,sCAAsC,GAAG,GAAG,QAAQ,CAAC;AACtE,QAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,QAAG,aAAa,CAAC;AACjB,QAAG,UAAU,GAAG,QAAQ,CAAC;AACzB,QAAG,aAAa,EAAE,EAAE;AACpB,QAAG,WAAW,GAAG,sCAAsC,GAAG,GAAG,QAAQ,CAAC;AACtE,QAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,QAAG,OAAO,CAAC;AACX,QAAG,aAAa;AAAA,MAClB;AACA,UAAI,KAAK,GAAG;AACV,QAAG,UAAU,CAAC;AACd,QAAG,WAAW,2BAA2B,IAAI;AAC7C,QAAG,YAAY,iBAAiB,IAAI,YAAY,EAAE,cAAc,IAAI,SAAS,EAAE,oBAAoB,IAAI,kBAAkB;AACzH,QAAG,UAAU;AACb,QAAG,cAAc,IAAI,mBAAmB,IAAI,IAAI,EAAE;AAClD,QAAG,UAAU,CAAC;AACd,QAAG,cAAc,IAAI,iBAAiB,IAAI,IAAI,EAAE;AAChD,QAAG,UAAU;AACb,QAAG,WAAW,MAAM,IAAI,kBAAkB;AAC1C,QAAG,UAAU;AACb,QAAG,kBAAkB,IAAI,eAAe;AAAA,MAC1C;AAAA,IACF;AAAA,IACA,cAAc,CAAC,aAAa;AAAA,IAC5B,QAAQ,CAAC,GAAG;AAAA,IACZ,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,8BAA8B;AAAA,QAC9B,sCAAsC;AAAA,QACtC,0CAA0C;AAAA,QAC1C,iCAAiC;AAAA,QACjC,iCAAiC;AAAA,QACjC,iCAAiC;AAAA,QACjC,oCAAoC;AAAA,QACpC,wCAAwC;AAAA,QACxC,wCAAwC;AAAA;AAAA;AAAA;AAAA;AAAA,QAKxC,yCAAyC;AAAA,QACzC,oDAAoD;AAAA,QACpD,iDAAiD;AAAA,QACjD,oDAAoD;AAAA,QACpD,2CAA2C;AAAA,QAC3C,oCAAoC;AAAA,QACpC,2CAA2C;AAAA,QAC3C,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,QACrB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,QAAQ;AAAA,MACV;AAAA,MACA,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,GAAG;AAAA,QACD,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,SAAS,CAAC,aAAa;AAAA,MACvB,UAAU;AAAA,MACV,QAAQ,CAAC,27hBAAi8hB;AAAA,IAC58hB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,cAAc,OAAO,UAAU;AAAA,EAC/B,YAAY,OAAO,QAAQ;AAAA,EAC3B,cAAc;AAAA,EAAC;AAAA,EACf,WAAW,cAAc;AACvB,SAAK,iBAAiB,EAAE,MAAM;AAC9B,SAAK,SAAS,YAAY;AAAA,EAC5B;AAAA,EACA,mBAAmB;AACjB,WAAO,KAAK,YAAY;AAAA,EAC1B;AAAA,EACA,SAAS,OAAO;AACd,SAAK,iBAAiB,EAAE,cAAc;AACtC,SAAK,wBAAwB;AAAA,EAC/B;AAAA,EACA,WAAW;AACT,WAAO,KAAK,iBAAiB,EAAE,eAAe;AAAA,EAChD;AAAA,EACA,0BAA0B;AACxB,UAAM,QAAQ,KAAK,UAAU,YAAY;AACzC,UAAM,mBAAmB,KAAK,iBAAiB,CAAC;AAChD,UAAM,SAAS,KAAK;AACpB,UAAM,MAAM,OAAO,aAAa;AAChC,QAAI,gBAAgB;AACpB,QAAI,SAAS,KAAK;AAAA,EACpB;AAAA,EACA,OAAO,OAAO,SAAS,yBAAyB,mBAAmB;AACjE,WAAO,KAAK,qBAAqB,mBAAkB;AAAA,EACrD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,QAAQ,oBAAoB,EAAE,CAAC;AAAA,IAC5C,WAAW,CAAC,QAAQ,WAAW,YAAY,MAAM,mBAAmB,QAAQ,GAAG,qBAAqB;AAAA,EACtG,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,mBAAmB;AAAA,MACrB;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAMH,IAAM,aAAN,MAAM,oBAAmB,QAAQ;AAAA,EAC/B,oBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMpB,oBAAoB;AAAA,EACpB,WAAW;AAAA;AAAA,EAEX,SAAS,IAAI,aAAa;AAAA;AAAA,EAE1B;AAAA;AAAA,EAEA;AAAA,EACA,aAAa;AAAA,EACb,cAAc;AACZ,UAAM;AACN,SAAK,OAAO;AACZ,SAAK,QAAQ,KAAK,UAAU,KAAK,SAAS,CAAC,EAAE,UAAU,MAAM;AAC3D,UAAI,KAAK,cAAc,CAAC,KAAK,mBAAmB;AAC9C,aAAK,cAAc;AAAA,MACrB;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,mBAAmB;AAEjB,WAAO,CAAC,KAAK,cAAc,MAAM,iBAAiB;AAAA,EACpD;AAAA;AAAA,EAEA,eAAe;AACb,QAAI,CAAC,KAAK,cAAc,CAAC,KAAK,UAAU;AACtC,WAAK,MAAM;AAAA,IACb;AAAA,EACF;AAAA,EACA,eAAe,OAAO;AACpB,QAAI,MAAM,YAAY,SAAS,CAAC,KAAK,UAAU;AAC7C,UAAI,KAAK,YAAY;AACnB,cAAM,eAAe;AACrB,aAAK,cAAc;AAAA,MACrB,WAAW,KAAK,UAAU;AACxB,aAAK,cAAc,KAAK;AAAA,MAC1B;AAAA,IACF,WAAW,KAAK,YAAY;AAE1B,YAAM,gBAAgB;AAAA,IACxB,OAAO;AACL,YAAM,eAAe,KAAK;AAAA,IAC5B;AAAA,EACF;AAAA,EACA,mBAAmB,OAAO;AACxB,QAAI,CAAC,KAAK,YAAY,KAAK,UAAU;AACnC,WAAK,cAAc,KAAK;AAAA,IAC1B;AAAA,EACF;AAAA,EACA,cAAc,OAAO;AACnB,QAAI,CAAC,KAAK,iBAAiB,KAAK,cAAc,KAAK,iBAAiB,MAAM,MAAM,MAAM,KAAK,YAAY;AACrG;AAAA,IACF;AAEA,UAAM,QAAQ,KAAK;AACnB,SAAK,aAAa,KAAK,oBAAoB;AAE3C,oBAAgB,MAAM;AACpB,WAAK,cAAc,EAAE,WAAW,KAAK;AACrC,WAAK,oBAAoB;AAAA,IAC3B,GAAG;AAAA,MACD,UAAU,KAAK;AAAA,IACjB,CAAC;AAAA,EACH;AAAA,EACA,gBAAgB;AACd,SAAK,aAAa,KAAK,oBAAoB;AAC3C,SAAK,OAAO,KAAK;AAAA,MACf,MAAM;AAAA,MACN,OAAO,KAAK,cAAc,EAAE,SAAS;AAAA,IACvC,CAAC;AAGD,QAAI,KAAK,UAAU,kBAAkB,KAAK,cAAc,EAAE,iBAAiB,KAAK,KAAK,UAAU,kBAAkB,KAAK,UAAU,MAAM;AACpI,WAAK,cAAc,MAAM;AAAA,IAC3B;AAAA,EACF;AAAA,EACA,oBAAoB;AAClB,WAAO,MAAM,kBAAkB,KAAK,KAAK;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB;AACd,WAAO,KAAK,oBAAoB,KAAK;AAAA,EACvC;AAAA,EACA,OAAO,OAAO,SAAS,mBAAmB,mBAAmB;AAC3D,WAAO,KAAK,qBAAqB,aAAY;AAAA,EAC/C;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,cAAc,GAAG,CAAC,IAAI,gBAAgB,EAAE,GAAG,CAAC,oBAAoB,GAAG,CAAC,IAAI,sBAAsB,EAAE,CAAC;AAAA,IAC9G,gBAAgB,SAAS,0BAA0B,IAAI,KAAK,UAAU;AACpE,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,kBAAkB,CAAC;AAAA,MACjD;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,mBAAmB,GAAG;AAAA,MACzE;AAAA,IACF;AAAA,IACA,WAAW,SAAS,iBAAiB,IAAI,KAAK;AAC5C,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,kBAAkB,CAAC;AAAA,MACpC;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,mBAAmB,GAAG;AAAA,MACzE;AAAA,IACF;AAAA,IACA,WAAW,CAAC,GAAG,gBAAgB,oBAAoB,oBAAoB;AAAA,IACvE,UAAU;AAAA,IACV,cAAc,SAAS,wBAAwB,IAAI,KAAK;AACtD,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,SAAS,SAAS,sCAAsC;AACpE,iBAAO,IAAI,aAAa;AAAA,QAC1B,CAAC,EAAE,YAAY,SAAS,uCAAuC,QAAQ;AACrE,iBAAO,IAAI,mBAAmB,MAAM;AAAA,QACtC,CAAC;AAAA,MACH;AACA,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,MAAM,IAAI,EAAE;AAC9B,QAAG,YAAY,YAAY,IAAI,WAAW,OAAO,EAAE,EAAE,cAAc,IAAI,EAAE,oBAAoB,IAAI,EAAE,QAAQ,IAAI,IAAI;AACnH,QAAG,YAAY,4BAA4B,IAAI,WAAW,EAAE,yBAAyB,IAAI,QAAQ,EAAE,wBAAwB,IAAI,UAAU,EAAE,yBAAyB,IAAI,QAAQ,EAAE,gCAAgC,IAAI,QAAQ,EAAE,4CAA4C,IAAI,iBAAiB,CAAC,EAAE,4CAA4C,IAAI,WAAW,EAAE,yCAAyC,IAAI,WAAW,EAAE,mCAAmC,IAAI,WAAW,EAAE,4BAA4B,IAAI,WAAW,EAAE,mCAAmC,IAAI,iBAAiB,CAAC;AAAA,MACvjB;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,UAAU;AAAA,IACZ;AAAA,IACA,SAAS;AAAA,MACP,QAAQ;AAAA,IACV;AAAA,IACA,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,GAAG;AAAA,MACD,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,GAAM,0BAA0B;AAAA,IAClC,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,GAAG,4BAA4B,GAAG,CAAC,QAAQ,YAAY,iBAAiB,IAAI,GAAG,4BAA4B,qCAAqC,GAAG,UAAU,GAAG,CAAC,GAAG,+BAA+B,sBAAsB,GAAG,CAAC,GAAG,kCAAkC,2BAA2B,GAAG,CAAC,eAAe,QAAQ,GAAG,wCAAwC,qBAAqB,GAAG,CAAC,QAAQ,YAAY,GAAG,4BAA4B,oCAAoC,GAAG,CAAC,GAAG,uBAAuB,GAAG,IAAI,GAAG,CAAC,oBAAoB,EAAE,CAAC;AAAA,IAC7hB,UAAU,SAAS,oBAAoB,IAAI,KAAK;AAC9C,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB,GAAG;AACtB,QAAG,WAAW,GAAG,mCAAmC,GAAG,GAAG,QAAQ,CAAC;AACnE,QAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,QAAG,WAAW,GAAG,mCAAmC,GAAG,GAAG,QAAQ,CAAC;AACnE,QAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,QAAG,WAAW,GAAG,mCAAmC,GAAG,CAAC,EAAE,GAAG,mCAAmC,GAAG,CAAC;AACpG,QAAG,UAAU,GAAG,QAAQ,CAAC;AACzB,QAAG,aAAa,EAAE;AAClB,QAAG,WAAW,GAAG,mCAAmC,GAAG,GAAG,QAAQ,CAAC;AACnE,QAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,QAAG,OAAO,CAAC;AACX,QAAG,aAAa;AAAA,MAClB;AACA,UAAI,KAAK,GAAG;AACV,QAAG,cAAc,CAAC,IAAI,aAAa,IAAI,EAAE;AACzC,QAAG,UAAU;AACb,QAAG,WAAW,YAAY,IAAI,QAAQ;AACtC,QAAG,YAAY,cAAc,IAAI,SAAS,EAAE,oBAAoB,IAAI,kBAAkB;AACtF,QAAG,UAAU;AACb,QAAG,cAAc,IAAI,cAAc,IAAI,EAAE;AACzC,QAAG,UAAU,CAAC;AACd,QAAG,cAAc,IAAI,aAAa,IAAI,CAAC;AACvC,QAAG,UAAU,CAAC;AACd,QAAG,cAAc,IAAI,iBAAiB,IAAI,IAAI,EAAE;AAChD,QAAG,UAAU;AACb,QAAG,WAAW,MAAM,IAAI,kBAAkB;AAC1C,QAAG,UAAU;AACb,QAAG,kBAAkB,IAAI,eAAe;AAAA,MAC1C;AAAA,IACF;AAAA,IACA,cAAc,CAAC,eAAe,gBAAgB;AAAA,IAC9C,QAAQ,CAAC,GAAG;AAAA,IACZ,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,YAAY,CAAC;AAAA,IACnF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,oCAAoC;AAAA,QACpC,iCAAiC;AAAA,QACjC,gCAAgC;AAAA,QAChC,iCAAiC;AAAA,QACjC,wCAAwC;AAAA,QACxC,oDAAoD;AAAA,QACpD,oDAAoD;AAAA,QACpD,iDAAiD;AAAA,QACjD,2CAA2C;AAAA,QAC3C,oCAAoC;AAAA,QACpC,2CAA2C;AAAA,QAC3C,QAAQ;AAAA;AAAA;AAAA,QAGR,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,QACrB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,WAAW;AAAA,QACX,cAAc;AAAA,MAChB;AAAA,MACA,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,GAAG;AAAA,QACD,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,SAAS,CAAC,eAAe,gBAAgB;AAAA,MACzC,UAAU;AAAA,MACV,QAAQ,CAAC,27hBAAi8hB;AAAA,IAC58hB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAOH,IAAM,aAAN,MAAM,YAAW;AAAA,EACf,cAAc,OAAO,UAAU;AAAA,EAC/B,qBAAqB,OAAO,iBAAiB;AAAA,EAC7C,OAAO,OAAO,gBAAgB;AAAA,IAC5B,UAAU;AAAA,EACZ,CAAC;AAAA;AAAA,EAED,iCAAiC;AAAA;AAAA,EAEjC;AAAA;AAAA,EAEA,aAAa,IAAI,QAAQ;AAAA;AAAA,EAEzB,eAAe;AAAA;AAAA,EAEf,IAAI,mBAAmB;AACrB,WAAO,KAAK,eAAe,UAAQ,KAAK,QAAQ;AAAA,EAClD;AAAA;AAAA,EAEA,IAAI,uBAAuB;AACzB,WAAO,KAAK,eAAe,UAAQ,KAAK,SAAS;AAAA,EACnD;AAAA;AAAA,EAEA,IAAI,qBAAqB;AACvB,WAAO,KAAK,eAAe,UAAQ,KAAK,OAAO;AAAA,EACjD;AAAA;AAAA,EAEA,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,YAAY;AACjB,SAAK,gBAAgB;AAAA,EACvB;AAAA,EACA,YAAY;AAAA;AAAA,EAEZ,IAAI,QAAQ;AACV,WAAO,CAAC,KAAK,UAAU,KAAK,OAAO,WAAW;AAAA,EAChD;AAAA;AAAA,EAEA,IAAI,OAAO;AACT,QAAI,KAAK,eAAe;AACtB,aAAO,KAAK;AAAA,IACd;AACA,WAAO,KAAK,QAAQ,OAAO,KAAK;AAAA,EAClC;AAAA;AAAA,EAEA,WAAW;AAAA,EACX,IAAI,KAAK,OAAO;AACd,SAAK,gBAAgB;AAAA,EACvB;AAAA,EACA,gBAAgB;AAAA;AAAA,EAEhB,IAAI,UAAU;AACZ,WAAO,KAAK,gBAAgB;AAAA,EAC9B;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,eAAe,IAAI,UAAU;AAAA,EAC7B,cAAc;AAAA,EAAC;AAAA,EACf,kBAAkB;AAChB,SAAK,sBAAsB;AAC3B,SAAK,qBAAqB;AAC1B,SAAK,2BAA2B;AAAA,EAClC;AAAA,EACA,cAAc;AACZ,SAAK,aAAa,QAAQ;AAC1B,SAAK,aAAa,QAAQ;AAC1B,SAAK,WAAW,KAAK;AACrB,SAAK,WAAW,SAAS;AAAA,EAC3B;AAAA;AAAA,EAEA,kBAAkB;AAChB,WAAO,KAAK,UAAU,KAAK,OAAO,KAAK,UAAQ,KAAK,UAAU,CAAC;AAAA,EACjE;AAAA;AAAA,EAEA,kBAAkB;AAChB,SAAK,QAAQ,QAAQ,UAAQ;AAC3B,WAAK,oBAAoB,KAAK;AAC9B,WAAK,mBAAmB,aAAa;AAAA,IACvC,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,QAAQ;AAAA,EAAC;AAAA;AAAA,EAET,eAAe,OAAO;AACpB,QAAI,KAAK,oBAAoB,KAAK,GAAG;AACnC,WAAK,YAAY,UAAU,KAAK;AAAA,IAClC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc,OAAO;AACnB,WAAO,SAAS,KAAK,QAAQ,KAAK,OAAO;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,oBAAoB;AAClB,UAAM,WAAW,KAAK,YAAY,cAAc;AAChD,QAAI,aAAa,IAAI;AAInB,WAAK,YAAY,cAAc,WAAW;AAG1C,iBAAW,MAAM,KAAK,YAAY,cAAc,WAAW,QAAQ;AAAA,IACrE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe,iBAAiB;AAC9B,WAAO,KAAK,OAAO,QAAQ,KAAK,UAAU,IAAI,GAAG,UAAU,MAAM,MAAM,GAAG,KAAK,OAAO,IAAI,eAAe,CAAC,CAAC,CAAC;AAAA,EAC9G;AAAA;AAAA,EAEA,oBAAoB,OAAO;AACzB,QAAI,iBAAiB,MAAM;AAC3B,WAAO,kBAAkB,mBAAmB,KAAK,YAAY,eAAe;AAC1E,UAAI,eAAe,UAAU,SAAS,cAAc,GAAG;AACrD,eAAO;AAAA,MACT;AACA,uBAAiB,eAAe;AAAA,IAClC;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,wBAAwB;AAItB,SAAK,OAAO,QAAQ,KAAK,UAAU,KAAK,MAAM,CAAC,EAAE,UAAU,WAAS;AAClE,YAAM,UAAU,CAAC;AACjB,YAAM,QAAQ,UAAQ,KAAK,YAAY,EAAE,QAAQ,YAAU,QAAQ,KAAK,MAAM,CAAC,CAAC;AAChF,WAAK,aAAa,MAAM,OAAO;AAC/B,WAAK,aAAa,gBAAgB;AAAA,IACpC,CAAC;AACD,SAAK,cAAc,IAAI,gBAAgB,KAAK,YAAY,EAAE,wBAAwB,EAAE,0BAA0B,KAAK,OAAO,KAAK,KAAK,QAAQ,KAAK,EAAE,eAAe,EAAE,cAAc,YAAU,KAAK,eAAe,MAAM,CAAC;AAGvN,SAAK,iBAAiB,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,CAAC;AAAA,MAChE;AAAA,IACF,MAAM;AACJ,YAAM,SAAS,KAAK,iBAAiB,SAAS,aAAa;AAC3D,UAAI,QAAQ;AACV,aAAK,YAAY,iBAAiB,MAAM;AAAA,MAC1C;AAAA,IACF,CAAC;AACD,SAAK,MAAM,OAAO,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,eAAa,KAAK,YAAY,0BAA0B,SAAS,CAAC;AAAA,EACjI;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe,QAAQ;AAGrB,WAAO,CAAC,OAAO,iBAAiB,OAAO;AAAA,EACzC;AAAA;AAAA,EAEA,uBAAuB;AACrB,SAAK,OAAO,QAAQ,KAAK,UAAU,IAAI,GAAG,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,MAAM;AACpF,UAAI,KAAK,UAAU;AAGjB,gBAAQ,QAAQ,EAAE,KAAK,MAAM,KAAK,gBAAgB,CAAC;AAAA,MACrD;AACA,WAAK,4BAA4B;AAAA,IACnC,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,6BAA6B;AAC3B,SAAK,qBAAqB,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,WAAS;AAC5E,YAAM,YAAY,KAAK,OAAO,QAAQ;AACtC,YAAM,YAAY,UAAU,QAAQ,MAAM,IAAI;AAK9C,UAAI,KAAK,cAAc,SAAS,KAAK,MAAM,KAAK,UAAU,GAAG;AAC3D,aAAK,iCAAiC;AAAA,MACxC;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,8BAA8B;AAC5B,QAAI,KAAK,kCAAkC,MAAM;AAC/C;AAAA,IACF;AACA,QAAI,KAAK,OAAO,QAAQ;AACtB,YAAM,WAAW,KAAK,IAAI,KAAK,gCAAgC,KAAK,OAAO,SAAS,CAAC;AACrF,YAAM,cAAc,KAAK,OAAO,QAAQ,EAAE,QAAQ;AAClD,UAAI,YAAY,UAAU;AAExB,YAAI,KAAK,OAAO,WAAW,GAAG;AAC5B,eAAK,MAAM;AAAA,QACb,OAAO;AACL,eAAK,YAAY,sBAAsB;AAAA,QACzC;AAAA,MACF,OAAO;AACL,oBAAY,MAAM;AAAA,MACpB;AAAA,IACF,OAAO;AACL,WAAK,MAAM;AAAA,IACb;AACA,SAAK,iCAAiC;AAAA,EACxC;AAAA,EACA,OAAO,OAAO,SAAS,mBAAmB,mBAAmB;AAC3D,WAAO,KAAK,qBAAqB,aAAY;AAAA,EAC/C;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,cAAc,CAAC;AAAA,IAC5B,gBAAgB,SAAS,0BAA0B,IAAI,KAAK,UAAU;AACpE,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,SAAS,CAAC;AAAA,MACxC;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,SAAS;AAAA,MAC5D;AAAA,IACF;AAAA,IACA,WAAW,CAAC,GAAG,oBAAoB,wBAAwB;AAAA,IAC3D,UAAU;AAAA,IACV,cAAc,SAAS,wBAAwB,IAAI,KAAK;AACtD,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,WAAW,SAAS,sCAAsC,QAAQ;AAC9E,iBAAO,IAAI,eAAe,MAAM;AAAA,QAClC,CAAC;AAAA,MACH;AACA,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,QAAQ,IAAI,IAAI;AAAA,MACjC;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,MACtD,MAAM;AAAA,MACN,UAAU,CAAC,GAAG,YAAY,YAAY,WAAS,SAAS,OAAO,IAAI,gBAAgB,KAAK,CAAC;AAAA,IAC3F;AAAA,IACA,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,QAAQ,gBAAgB,GAAG,+BAA+B,CAAC;AAAA,IACrE,UAAU,SAAS,oBAAoB,IAAI,KAAK;AAC9C,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,QAAG,aAAa,CAAC;AACjB,QAAG,aAAa;AAAA,MAClB;AAAA,IACF;AAAA,IACA,QAAQ,CAAC,g3BAAg3B;AAAA,IACz3B,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,YAAY,CAAC;AAAA,IACnF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,MAKV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,aAAa;AAAA,QACb,eAAe;AAAA,MACjB;AAAA,MACA,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,QAAQ,CAAC,g3BAAg3B;AAAA,IAC33B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW,WAAS,SAAS,OAAO,IAAI,gBAAgB,KAAK;AAAA,MAC/D,CAAC;AAAA,IACH,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,SAAS;AAAA;AAAA;AAAA,QAGd,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAGH,IAAM,uBAAN,MAA2B;AAAA,EACzB;AAAA,EACA;AAAA,EACA,YACA,QACA,OAAO;AACL,SAAK,SAAS;AACd,SAAK,QAAQ;AAAA,EACf;AACF;AAMA,IAAM,0CAA0C;AAAA,EAC9C,SAAS;AAAA,EACT,aAAa,WAAW,MAAM,cAAc;AAAA,EAC5C,OAAO;AACT;AAKA,IAAM,iBAAN,MAAM,wBAAuB,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtC,aAAa,MAAM;AAAA,EAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKpB,YAAY,MAAM;AAAA,EAAC;AAAA;AAAA,EAEnB,eAAe;AAAA;AAAA,EAEf,kBAAkB,OAAO,2BAA2B;AAAA,IAClD,UAAU;AAAA,EACZ,CAAC;AAAA;AAAA,EAED,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,YAAY;AACjB,SAAK,uBAAuB;AAAA,EAC9B;AAAA,EACA,YAAY;AAAA;AAAA,EAEZ,IAAI,WAAW;AACb,UAAM,gBAAgB,KAAK,OAAO,QAAQ,EAAE,OAAO,UAAQ,KAAK,QAAQ;AACxE,WAAO,KAAK,WAAW,gBAAgB,cAAc,CAAC;AAAA,EACxD;AAAA;AAAA,EAEA,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOlB,IAAI,aAAa;AACf,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,WAAW,OAAO;AACpB,SAAK,cAAc;AACnB,SAAK,uBAAuB;AAAA,EAC9B;AAAA,EACA,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMd,cAAc,CAAC,IAAI,OAAO,OAAO;AAAA;AAAA,EAEjC,WAAW;AAAA;AAAA,EAEX,IAAI,+BAA+B;AACjC,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,6BAA6B,OAAO;AACtC,SAAK,gCAAgC;AACrC,SAAK,uBAAuB;AAAA,EAC9B;AAAA,EACA,gCAAgC,KAAK,iBAAiB,gCAAgC;AAAA;AAAA,EAEtF,IAAI,uBAAuB;AACzB,WAAO,KAAK,eAAe,UAAQ,KAAK,eAAe;AAAA,EACzD;AAAA;AAAA,EAEA,IAAI,kBAAkB;AACpB,WAAO,KAAK,eAAe,UAAQ,KAAK,OAAO;AAAA,EACjD;AAAA;AAAA,EAEA,IAAI,QAAQ;AACV,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,MAAM,OAAO;AACf,QAAI,KAAK,UAAU,KAAK,OAAO,QAAQ;AACrC,WAAK,qBAAqB,OAAO,KAAK;AAAA,IACxC;AACA,SAAK,SAAS;AAAA,EAChB;AAAA,EACA;AAAA;AAAA,EAEA,SAAS,IAAI,aAAa;AAAA,EAC1B,SAAS;AAAA,EACT,qBAAqB;AACnB,SAAK,OAAO,QAAQ,KAAK,UAAU,IAAI,GAAG,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,MAAM;AACpF,UAAI,KAAK,UAAU,QAAW;AAC5B,gBAAQ,QAAQ,EAAE,KAAK,MAAM;AAC3B,eAAK,qBAAqB,KAAK,OAAO,KAAK;AAAA,QAC7C,CAAC;AAAA,MACH;AAEA,WAAK,uBAAuB;AAAA,IAC9B,CAAC;AACD,SAAK,gBAAgB,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,MAAM,KAAK,MAAM,CAAC;AAClF,SAAK,qBAAqB,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,WAAS;AAC5E,UAAI,CAAC,KAAK,UAAU;AAClB,aAAK,OAAO,QAAQ,UAAQ;AAC1B,cAAI,SAAS,MAAM,QAAQ;AACzB,iBAAK,kBAAkB,OAAO,OAAO,KAAK;AAAA,UAC5C;AAAA,QACF,CAAC;AAAA,MACH;AACA,UAAI,MAAM,aAAa;AACrB,aAAK,kBAAkB;AAAA,MACzB;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ;AACN,QAAI,KAAK,UAAU;AACjB;AAAA,IACF;AACA,UAAM,oBAAoB,KAAK,sBAAsB;AACrD,QAAI,qBAAqB,CAAC,kBAAkB,UAAU;AACpD,wBAAkB,MAAM;AAAA,IAC1B,WAAW,KAAK,OAAO,SAAS,GAAG;AACjC,WAAK,YAAY,mBAAmB;AAAA,IACtC,OAAO;AACL,WAAK,YAAY,cAAc,MAAM;AAAA,IACvC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,OAAO;AAChB,QAAI,SAAS,MAAM;AACjB,WAAK,QAAQ;AAAA,IACf,OAAO;AACL,WAAK,QAAQ;AAAA,IACf;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB,IAAI;AACnB,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB,IAAI;AACpB,SAAK,aAAa;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB,YAAY;AAC3B,SAAK,WAAW;AAAA,EAClB;AAAA;AAAA,EAEA,qBAAqB,OAAO,cAAc,MAAM;AAC9C,SAAK,gBAAgB;AACrB,QAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,YAAM,QAAQ,kBAAgB,KAAK,aAAa,cAAc,WAAW,CAAC;AAAA,IAC5E,OAAO;AACL,WAAK,aAAa,OAAO,WAAW;AAAA,IACtC;AAAA,EACF;AAAA;AAAA,EAEA,QAAQ;AACN,QAAI,CAAC,KAAK,UAAU;AAElB,iBAAW,MAAM;AACf,YAAI,CAAC,KAAK,SAAS;AACjB,eAAK,eAAe;AAAA,QACtB;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,SAAS,OAAO;AACd,QAAI,MAAM,YAAY,KAAK;AACzB,YAAM,kBAAkB;AAAA,IAC1B;AAAA,EACF;AAAA;AAAA,EAEA,iBAAiB;AACf,SAAK,WAAW;AAChB,SAAK,mBAAmB,aAAa;AAAA,EACvC;AAAA;AAAA,EAEA,oBAAoB;AAClB,QAAI,cAAc;AAClB,QAAI,MAAM,QAAQ,KAAK,QAAQ,GAAG;AAChC,oBAAc,KAAK,SAAS,IAAI,UAAQ,KAAK,KAAK;AAAA,IACpD,OAAO;AACL,oBAAc,KAAK,WAAW,KAAK,SAAS,QAAQ;AAAA,IACtD;AACA,SAAK,SAAS;AACd,SAAK,OAAO,KAAK,IAAI,qBAAqB,MAAM,WAAW,CAAC;AAC5D,SAAK,UAAU,WAAW;AAC1B,SAAK,mBAAmB,aAAa;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB,MAAM;AACpB,SAAK,OAAO,QAAQ,UAAQ;AAC1B,UAAI,SAAS,MAAM;AACjB,aAAK,SAAS;AAAA,MAChB;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa,OAAO,aAAa;AAC/B,UAAM,oBAAoB,KAAK,OAAO,KAAK,UAAQ;AACjD,aAAO,KAAK,SAAS,QAAQ,KAAK,YAAY,KAAK,OAAO,KAAK;AAAA,IACjE,CAAC;AACD,QAAI,mBAAmB;AACrB,oBAAc,kBAAkB,qBAAqB,IAAI,kBAAkB,OAAO;AAAA,IACpF;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,yBAAyB;AACvB,QAAI,KAAK,QAAQ;AAGf,cAAQ,QAAQ,EAAE,KAAK,MAAM;AAC3B,aAAK,OAAO,QAAQ,UAAQ;AAC1B,eAAK,oBAAoB,KAAK;AAC9B,eAAK,qBAAqB,KAAK;AAC/B,eAAK,wCAAwC,KAAK;AAClD,eAAK,mBAAmB,aAAa;AAAA,QACvC,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA,EAEA,wBAAwB;AACtB,QAAI,MAAM,QAAQ,KAAK,QAAQ,GAAG;AAChC,aAAO,KAAK,SAAS,SAAS,KAAK,SAAS,CAAC,IAAI;AAAA,IACnD,OAAO;AACL,aAAO,KAAK;AAAA,IACd;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe,QAAQ;AAUrB,WAAO,CAAC,OAAO;AAAA,EACjB;AAAA,EACA,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,uBAAuB,mBAAmB;AACxD,cAAQ,gCAAgC,8BAAiC,sBAAsB,eAAc,IAAI,qBAAqB,eAAc;AAAA,IACtJ;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,kBAAkB,CAAC;AAAA,IAChC,gBAAgB,SAAS,8BAA8B,IAAI,KAAK,UAAU;AACxE,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,eAAe,CAAC;AAAA,MAC9C;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,SAAS;AAAA,MAC5D;AAAA,IACF;AAAA,IACA,WAAW,CAAC,GAAG,0BAA0B,sBAAsB;AAAA,IAC/D,UAAU;AAAA,IACV,cAAc,SAAS,4BAA4B,IAAI,KAAK;AAC1D,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,SAAS,SAAS,0CAA0C;AACxE,iBAAO,IAAI,MAAM;AAAA,QACnB,CAAC,EAAE,QAAQ,SAAS,yCAAyC;AAC3D,iBAAO,IAAI,MAAM;AAAA,QACnB,CAAC,EAAE,WAAW,SAAS,0CAA0C,QAAQ;AACvE,iBAAO,IAAI,SAAS,MAAM;AAAA,QAC5B,CAAC;AAAA,MACH;AACA,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,YAAY,IAAI,YAAY,IAAI,QAAQ,KAAK,IAAI,QAAQ;AAC3E,QAAG,YAAY,QAAQ,IAAI,IAAI,EAAE,iBAAiB,IAAI,OAAO,IAAI,WAAW,IAAI,EAAE,iBAAiB,IAAI,SAAS,SAAS,CAAC,EAAE,wBAAwB,IAAI,QAAQ,EAAE,oBAAoB,IAAI,eAAe;AACzM,QAAG,YAAY,8BAA8B,IAAI,QAAQ,EAAE,8BAA8B,IAAI,QAAQ;AAAA,MACvG;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,MACtD,iBAAiB,CAAC,GAAG,oBAAoB,iBAAiB;AAAA,MAC1D,YAAY,CAAC,GAAG,cAAc,cAAc,gBAAgB;AAAA,MAC5D,aAAa;AAAA,MACb,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,MACtD,8BAA8B,CAAC,GAAG,gCAAgC,gCAAgC,gBAAgB;AAAA,MAClH,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,QAAQ;AAAA,IACV;AAAA,IACA,UAAU,CAAI,mBAAmB,CAAC,uCAAuC,CAAC,GAAM,0BAA0B;AAAA,IAC1G,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,QAAQ,gBAAgB,GAAG,+BAA+B,CAAC;AAAA,IACrE,UAAU,SAAS,wBAAwB,IAAI,KAAK;AAClD,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,QAAG,aAAa,CAAC;AACjB,QAAG,aAAa;AAAA,MAClB;AAAA,IACF;AAAA,IACA,QAAQ,CAAC,GAAG;AAAA,IACZ,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,MAKV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,eAAe;AAAA,QACf,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,+BAA+B;AAAA,QAC/B,2BAA2B;AAAA,QAC3B,sCAAsC;AAAA,QACtC,sCAAsC;AAAA,QACtC,WAAW;AAAA,QACX,UAAU;AAAA,QACV,aAAa;AAAA,MACf;AAAA,MACA,WAAW,CAAC,uCAAuC;AAAA,MACnD,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,QAAQ,CAAC,g3BAAg3B;AAAA,IAC33B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,IAC3B,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,8BAA8B,CAAC;AAAA,MAC7B,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA;AAAA;AAAA,QAGpB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAGH,IAAM,oBAAN,MAAwB;AAAA,EACtB;AAAA,EACA;AAAA,EACA,YACA,QACA,OAAO;AACL,SAAK,SAAS;AACd,SAAK,QAAQ;AAAA,EACf;AACF;AAKA,IAAM,cAAN,MAAM,qBAAoB,WAAW;AAAA,EACnC,YAAY,OAAO,WAAW;AAAA,IAC5B,UAAU;AAAA,IACV,MAAM;AAAA,EACR,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKD,cAAc;AAAA;AAAA,EAEd;AAAA,EACA,eAAe;AAAA,EACf;AAAA;AAAA;AAAA;AAAA,EAIA,sBAAsB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKvB,aAAa,MAAM;AAAA,EAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKpB,YAAY,MAAM;AAAA,EAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKnB,IAAI,WAAW;AACb,WAAO,KAAK,YAAY,CAAC,CAAC,KAAK,UAAU,WAAW,KAAK;AAAA,EAC3D;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,YAAY;AACjB,SAAK,gBAAgB;AACrB,SAAK,aAAa,KAAK;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,KAAK;AACP,WAAO,KAAK,WAAW;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,QAAQ;AACV,YAAQ,CAAC,KAAK,cAAc,KAAK,WAAW,WAAW,CAAC,KAAK,UAAU,KAAK,OAAO,WAAW;AAAA,EAChG;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,cAAc;AAChB,WAAO,KAAK,aAAa,KAAK,WAAW,cAAc,KAAK;AAAA,EAC9D;AAAA,EACA,IAAI,YAAY,OAAO;AACrB,SAAK,eAAe;AACpB,SAAK,aAAa,KAAK;AAAA,EACzB;AAAA,EACA;AAAA;AAAA,EAEA,IAAI,UAAU;AACZ,WAAO,KAAK,WAAW,WAAW,KAAK,gBAAgB;AAAA,EACzD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,WAAW;AACb,WAAO,KAAK,aAAa,KAAK,WAAW,SAAS,aAAa,WAAW,QAAQ,KAAK;AAAA,EACzF;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,YAAY;AACjB,SAAK,aAAa,KAAK;AAAA,EACzB;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,mBAAmB;AACrB,WAAO,CAAC,KAAK,SAAS,KAAK;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,QAAQ;AACV,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,MAAM,OAAO;AACf,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,SAAS,CAAC;AAAA;AAAA,EAEV,IAAI,oBAAoB;AACtB,WAAO,KAAK,mBAAmB;AAAA,EACjC;AAAA,EACA,IAAI,kBAAkB,OAAO;AAC3B,SAAK,mBAAmB,UAAU;AAAA,EACpC;AAAA;AAAA,EAEA,IAAI,kBAAkB;AACpB,WAAO,KAAK,eAAe,UAAQ,KAAK,OAAO;AAAA,EACjD;AAAA;AAAA,EAEA,SAAS,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM1B,cAAc,IAAI,aAAa;AAAA,EAC/B,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMT,eAAe,IAAI,QAAQ;AAAA;AAAA,EAE3B,IAAI,aAAa;AACf,WAAO,KAAK,mBAAmB;AAAA,EACjC;AAAA,EACA,IAAI,WAAW,OAAO;AACpB,SAAK,mBAAmB,aAAa;AAAA,EACvC;AAAA,EACA,cAAc;AACZ,UAAM;AACN,UAAM,aAAa,OAAO,QAAQ;AAAA,MAChC,UAAU;AAAA,IACZ,CAAC;AACD,UAAM,kBAAkB,OAAO,oBAAoB;AAAA,MACjD,UAAU;AAAA,IACZ,CAAC;AACD,UAAM,2BAA2B,OAAO,iBAAiB;AACzD,QAAI,KAAK,WAAW;AAClB,WAAK,UAAU,gBAAgB;AAAA,IACjC;AACA,SAAK,qBAAqB,IAAI,mBAAmB,0BAA0B,KAAK,WAAW,iBAAiB,YAAY,KAAK,YAAY;AAAA,EAC3I;AAAA,EACA,qBAAqB;AACnB,SAAK,gBAAgB,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,MAAM;AACpE,WAAK,MAAM;AACX,WAAK,aAAa,KAAK;AAAA,IACzB,CAAC;AACD,UAAM,KAAK,kBAAkB,KAAK,OAAO,OAAO,EAAE,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,MAAM,KAAK,aAAa,KAAK,CAAC;AAAA,EAC7H;AAAA,EACA,kBAAkB;AAChB,UAAM,gBAAgB;AACtB,QAAI,CAAC,KAAK,eAAe,OAAO,cAAc,eAAe,YAAY;AACvE,YAAM,MAAM,iEAAiE;AAAA,IAC/E;AAAA,EACF;AAAA,EACA,YAAY;AACV,QAAI,KAAK,WAAW;AAIlB,WAAK,iBAAiB;AAAA,IACxB;AAAA,EACF;AAAA,EACA,cAAc;AACZ,UAAM,YAAY;AAClB,SAAK,aAAa,SAAS;AAAA,EAC7B;AAAA;AAAA,EAEA,cAAc,cAAc;AAC1B,SAAK,aAAa;AAClB,SAAK,WAAW,kBAAkB,KAAK,mBAAmB;AAAA,EAC5D;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB,OAAO;AACtB,QAAI,CAAC,KAAK,YAAY,CAAC,KAAK,oBAAoB,KAAK,GAAG;AACtD,WAAK,MAAM;AAAA,IACb;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ;AACN,QAAI,KAAK,YAAY,KAAK,WAAW,SAAS;AAC5C;AAAA,IACF;AACA,QAAI,CAAC,KAAK,OAAO,UAAU,KAAK,OAAO,MAAM,UAAU;AAGrD,cAAQ,QAAQ,EAAE,KAAK,MAAM,KAAK,WAAW,MAAM,CAAC;AAAA,IACtD,OAAO;AACL,YAAM,aAAa,KAAK,YAAY;AACpC,UAAI,YAAY;AACd,mBAAW,MAAM;AAAA,MACnB,OAAO;AACL,aAAK,YAAY,mBAAmB;AAAA,MACtC;AAAA,IACF;AACA,SAAK,aAAa,KAAK;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB,KAAK;AAGrB,SAAK,sBAAsB;AAC3B,SAAK,YAAY,kBAAkB,GAAG;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,OAAO;AAEhB,SAAK,SAAS;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB,IAAI;AACnB,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB,IAAI;AACpB,SAAK,aAAa;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB,YAAY;AAC3B,SAAK,WAAW;AAChB,SAAK,aAAa,KAAK;AAAA,EACzB;AAAA;AAAA,EAEA,mBAAmB;AACjB,SAAK,mBAAmB,iBAAiB;AAAA,EAC3C;AAAA;AAAA,EAEA,QAAQ;AACN,QAAI,CAAC,KAAK,UAAU;AAKlB,iBAAW,MAAM;AACf,YAAI,CAAC,KAAK,SAAS;AACjB,eAAK,kBAAkB;AACvB,eAAK,eAAe;AAAA,QACtB;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,oBAAoB;AAClB,QAAI,CAAC,KAAK,WAAW,SAAS;AAC5B,YAAM,kBAAkB;AAAA,IAC1B;AAAA,EACF;AAAA;AAAA,EAEA,eAAe,OAAO;AACpB,UAAM,UAAU,MAAM;AACtB,UAAM,aAAa,KAAK,YAAY;AACpC,QAAI,YAAY,KAAK;AACnB,UAAI,KAAK,WAAW,WAAW,eAAe,OAAO,UAAU,KAAK,KAAK,OAAO,UAAU,CAAC,KAAK,OAAO,KAAK,UAAU;AACpH,cAAM,eAAe;AACrB,YAAI,YAAY;AACd,eAAK,YAAY,cAAc,UAAU;AAAA,QAC3C,OAAO;AACL,eAAK,eAAe;AAAA,QACtB;AAAA,MACF,OAAO;AAIL,cAAM,kBAAkB;AAAA,MAC1B;AAAA,IACF,WAAW,CAAC,KAAK,WAAW,SAAS;AAMnC,WAAK,YAAY,YAAY,YAAY,eAAe,YAAY;AAClE,cAAM,kBAAkB,KAAK,aAAa,OAAO,YAAU,OAAO,eAAe,WAAW,cAAc,CAAC,KAAK,eAAe,MAAM,CAAC;AACtI,cAAM,eAAe,gBAAgB,QAAQ,UAAU;AACvD,cAAM,QAAQ,MAAM,YAAY,WAAW,KAAK;AAChD,cAAM,eAAe;AACrB,YAAI,eAAe,MAAM,KAAK,cAAc,eAAe,KAAK,GAAG;AACjE,eAAK,YAAY,cAAc,gBAAgB,eAAe,KAAK,CAAC;AAAA,QACtE;AAAA,MACF,OAAO;AACL,cAAM,eAAe,KAAK;AAAA,MAC5B;AAAA,IACF;AACA,SAAK,aAAa,KAAK;AAAA,EACzB;AAAA,EACA,iBAAiB;AACf,QAAI,KAAK,OAAO,QAAQ;AACtB,WAAK,OAAO,KAAK,MAAM;AAAA,IACzB;AAAA,EACF;AAAA;AAAA,EAEA,oBAAoB;AAClB,UAAM,cAAc,KAAK,OAAO,SAAS,KAAK,OAAO,QAAQ,EAAE,IAAI,UAAQ,KAAK,KAAK,IAAI,CAAC;AAC1F,SAAK,SAAS;AACd,SAAK,OAAO,KAAK,IAAI,kBAAkB,MAAM,WAAW,CAAC;AACzD,SAAK,YAAY,KAAK,WAAW;AACjC,SAAK,UAAU,WAAW;AAC1B,SAAK,mBAAmB,aAAa;AAAA,EACvC;AAAA;AAAA,EAEA,iBAAiB;AACf,SAAK,WAAW;AAChB,SAAK,mBAAmB,aAAa;AACrC,SAAK,aAAa,KAAK;AAAA,EACzB;AAAA,EACA,OAAO,OAAO,SAAS,oBAAoB,mBAAmB;AAC5D,WAAO,KAAK,qBAAqB,cAAa;AAAA,EAChD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,eAAe,CAAC;AAAA,IAC7B,gBAAgB,SAAS,2BAA2B,IAAI,KAAK,UAAU;AACrE,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,YAAY,CAAC;AAAA,MAC3C;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,SAAS;AAAA,MAC5D;AAAA,IACF;AAAA,IACA,WAAW,CAAC,GAAG,oBAAoB,qBAAqB,wBAAwB;AAAA,IAChF,UAAU;AAAA,IACV,cAAc,SAAS,yBAAyB,IAAI,KAAK;AACvD,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,SAAS,SAAS,uCAAuC;AACrE,iBAAO,IAAI,MAAM;AAAA,QACnB,CAAC,EAAE,QAAQ,SAAS,sCAAsC;AACxD,iBAAO,IAAI,MAAM;AAAA,QACnB,CAAC;AAAA,MACH;AACA,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,QAAQ,IAAI,IAAI,EAAE,YAAY,IAAI,YAAY,IAAI,UAAU,IAAI,OAAO,WAAW,IAAI,KAAK,IAAI,QAAQ,EAAE,iBAAiB,IAAI,SAAS,SAAS,CAAC,EAAE,gBAAgB,IAAI,UAAU;AAChM,QAAG,YAAY,8BAA8B,IAAI,QAAQ,EAAE,6BAA6B,IAAI,UAAU,EAAE,8BAA8B,IAAI,QAAQ;AAAA,MACpJ;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,MACtD,aAAa;AAAA,MACb,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,MACtD,OAAO;AAAA,MACP,mBAAmB;AAAA,IACrB;AAAA,IACA,SAAS;AAAA,MACP,QAAQ;AAAA,MACR,aAAa;AAAA,IACf;AAAA,IACA,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,GAAM,0BAA0B;AAAA,IAClC,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,QAAQ,gBAAgB,GAAG,+BAA+B,CAAC;AAAA,IACrE,UAAU,SAAS,qBAAqB,IAAI,KAAK;AAC/C,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,QAAG,aAAa,CAAC;AACjB,QAAG,aAAa;AAAA,MAClB;AAAA,IACF;AAAA,IACA,QAAQ,CAAC,GAAG;AAAA,IACZ,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,MAKV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,eAAe;AAAA,QACf,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,sCAAsC;AAAA,QACtC,qCAAqC;AAAA,QACrC,sCAAsC;AAAA,QACtC,WAAW;AAAA,QACX,UAAU;AAAA,MACZ;AAAA,MACA,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,QAAQ,CAAC,g3BAAg3B;AAAA,IAC33B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA;AAAA;AAAA,QAGjB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,cAAc,OAAO,UAAU;AAAA;AAAA,EAE/B,UAAU;AAAA;AAAA,EAEV,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,QAAI,OAAO;AACT,WAAK,YAAY;AACjB,WAAK,UAAU,cAAc,IAAI;AAAA,IACnC;AAAA,EACF;AAAA,EACA;AAAA;AAAA;AAAA;AAAA,EAIA,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMZ;AAAA;AAAA,EAEA,UAAU,IAAI,aAAa;AAAA;AAAA,EAE3B,cAAc;AAAA;AAAA,EAEd,KAAK,OAAO,YAAY,EAAE,MAAM,0BAA0B;AAAA;AAAA,EAE1D,IAAI,WAAW;AACb,WAAO,KAAK,aAAa,KAAK,aAAa,KAAK,UAAU;AAAA,EAC5D;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,YAAY;AAAA;AAAA,EAEZ,IAAI,QAAQ;AACV,WAAO,CAAC,KAAK,aAAa;AAAA,EAC5B;AAAA;AAAA,EAEA;AAAA,EACA,cAAc;AACZ,UAAM,iBAAiB,OAAO,yBAAyB;AACvD,UAAM,YAAY,OAAO,gBAAgB;AAAA,MACvC,UAAU;AAAA,IACZ,CAAC;AACD,SAAK,eAAe,KAAK,YAAY;AACrC,SAAK,oBAAoB,eAAe;AACxC,QAAI,WAAW;AACb,WAAK,aAAa,UAAU,IAAI,kCAAkC;AAAA,IACpE;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,UAAU,aAAa,KAAK;AAAA,EACnC;AAAA,EACA,cAAc;AACZ,SAAK,QAAQ,SAAS;AAAA,EACxB;AAAA;AAAA,EAEA,SAAS,OAAO;AACd,QAAI,KAAK,SAAS,MAAM,YAAY,WAAW;AAG7C,UAAI,CAAC,MAAM,QAAQ;AACjB,aAAK,UAAU,eAAe;AAAA,MAChC;AACA,YAAM,eAAe;AAAA,IACvB,OAAO;AACL,WAAK,aAAa,KAAK;AAAA,IACzB;AAAA,EACF;AAAA;AAAA,EAEA,QAAQ;AACN,QAAI,KAAK,WAAW;AAClB,WAAK,aAAa;AAAA,IACpB;AACA,SAAK,UAAU;AAEf,QAAI,CAAC,KAAK,UAAU,SAAS;AAC3B,WAAK,UAAU,MAAM;AAAA,IACvB;AACA,SAAK,UAAU,aAAa,KAAK;AAAA,EACnC;AAAA,EACA,SAAS;AACP,SAAK,UAAU;AACf,SAAK,UAAU,aAAa,KAAK;AAAA,EACnC;AAAA;AAAA,EAEA,aAAa,OAAO;AAClB,QAAI,CAAC,SAAS,KAAK,gBAAgB,KAAK,KAAK,CAAC,MAAM,QAAQ;AAC1D,WAAK,QAAQ,KAAK;AAAA,QAChB,OAAO,KAAK;AAAA,QACZ,OAAO,KAAK,aAAa;AAAA,QACzB,WAAW;AAAA,MACb,CAAC;AACD,aAAO,eAAe;AAAA,IACxB;AAAA,EACF;AAAA,EACA,WAAW;AAET,SAAK,UAAU,aAAa,KAAK;AAAA,EACnC;AAAA;AAAA,EAEA,QAAQ;AACN,SAAK,aAAa,MAAM;AAAA,EAC1B;AAAA;AAAA,EAEA,QAAQ;AACN,SAAK,aAAa,QAAQ;AAAA,EAC5B;AAAA,EACA,kBAAkB,KAAK;AACrB,UAAM,UAAU,KAAK,YAAY;AAGjC,QAAI,IAAI,QAAQ;AACd,cAAQ,aAAa,oBAAoB,IAAI,KAAK,GAAG,CAAC;AAAA,IACxD,OAAO;AACL,cAAQ,gBAAgB,kBAAkB;AAAA,IAC5C;AAAA,EACF;AAAA;AAAA,EAEA,gBAAgB,OAAO;AACrB,WAAO,CAAC,eAAe,KAAK,KAAK,IAAI,IAAI,KAAK,iBAAiB,EAAE,IAAI,MAAM,OAAO;AAAA,EACpF;AAAA,EACA,OAAO,OAAO,SAAS,qBAAqB,mBAAmB;AAC7D,WAAO,KAAK,qBAAqB,eAAc;AAAA,EACjD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,SAAS,mBAAmB,EAAE,CAAC;AAAA,IAC5C,WAAW,CAAC,GAAG,sBAAsB,yBAAyB,yBAAyB,mBAAmB;AAAA,IAC1G,UAAU;AAAA,IACV,cAAc,SAAS,0BAA0B,IAAI,KAAK;AACxD,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,WAAW,SAAS,wCAAwC,QAAQ;AAChF,iBAAO,IAAI,SAAS,MAAM;AAAA,QAC5B,CAAC,EAAE,QAAQ,SAAS,uCAAuC;AACzD,iBAAO,IAAI,MAAM;AAAA,QACnB,CAAC,EAAE,SAAS,SAAS,wCAAwC;AAC3D,iBAAO,IAAI,OAAO;AAAA,QACpB,CAAC,EAAE,SAAS,SAAS,wCAAwC;AAC3D,iBAAO,IAAI,SAAS;AAAA,QACtB,CAAC;AAAA,MACH;AACA,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,MAAM,IAAI,EAAE;AAC9B,QAAG,YAAY,YAAY,IAAI,YAAY,IAAI,EAAE,eAAe,IAAI,eAAe,IAAI,EAAE,gBAAgB,IAAI,aAAa,IAAI,UAAU,YAAY,IAAI,UAAU,UAAU,UAAU,IAAI,EAAE,iBAAiB,IAAI,aAAa,IAAI,UAAU,YAAY,IAAI,EAAE,YAAY,IAAI,aAAa,IAAI,UAAU,YAAY,IAAI;AAAA,MAC3T;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,UAAU,CAAC,GAAG,mBAAmB,UAAU;AAAA,MAC3C,WAAW,CAAC,GAAG,yBAAyB,aAAa,gBAAgB;AAAA,MACrE,mBAAmB,CAAC,GAAG,iCAAiC,mBAAmB;AAAA,MAC3E,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,IACxD;AAAA,IACA,SAAS;AAAA,MACP,SAAS;AAAA,IACX;AAAA,IACA,UAAU,CAAC,gBAAgB,iBAAiB;AAAA,IAC5C,UAAU,CAAI,oBAAoB;AAAA,EACpC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA;AAAA;AAAA;AAAA,QAIJ,SAAS;AAAA,QACT,aAAa;AAAA,QACb,UAAU;AAAA,QACV,WAAW;AAAA,QACX,WAAW;AAAA,QACX,QAAQ;AAAA,QACR,mBAAmB;AAAA,QACnB,sBAAsB;AAAA,QACtB,uBAAuB;AAAA,QACvB,wBAAwB;AAAA,QACxB,mBAAmB;AAAA,MACrB;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,MACN,MAAM,CAAC,+BAA+B;AAAA,IACxC,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,sBAAsB;AAAA,IAC/B,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,IAAI,CAAC;AAAA,MACH,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,oBAAoB,CAAC,SAAS,eAAe,kBAAkB,aAAa,cAAc,gBAAgB,eAAe,eAAe,YAAY,YAAY,mBAAmB;AACzL,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,OAAO,OAAO,SAAS,uBAAuB,mBAAmB;AAC/D,WAAO,KAAK,qBAAqB,iBAAgB;AAAA,EACnD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,iBAAiB,iBAAiB,eAAe,SAAS,eAAe,kBAAkB,aAAa,cAAc,gBAAgB,eAAe,eAAe,YAAY,YAAY,mBAAmB;AAAA,IACzN,SAAS,CAAC,iBAAiB,SAAS,eAAe,kBAAkB,aAAa,cAAc,gBAAgB,eAAe,eAAe,YAAY,YAAY,mBAAmB;AAAA,EAC3L,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,WAAW,CAAC,mBAAmB;AAAA,MAC7B,SAAS;AAAA,MACT,UAAU;AAAA,QACR,mBAAmB,CAAC,KAAK;AAAA,MAC3B;AAAA,IACF,CAAC;AAAA,IACD,SAAS,CAAC,iBAAiB,iBAAiB,eAAe;AAAA,EAC7D,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,iBAAiB,iBAAiB,eAAe,iBAAiB;AAAA,MAC5E,SAAS,CAAC,iBAAiB,iBAAiB;AAAA,MAC5C,WAAW,CAAC,mBAAmB;AAAA,QAC7B,SAAS;AAAA,QACT,UAAU;AAAA,UACR,mBAAmB,CAAC,KAAK;AAAA,QAC3B;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}