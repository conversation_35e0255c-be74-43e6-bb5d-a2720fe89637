"""
Abid Ansari AI Assistant - Production Ready Version
Features:
- Cascading API fallback system (Gemini → Mistral → OpenRouter → OpenAI)
- Email-only authentication with Firebase
- Session management with countdown timer
- Optimized for production distribution
- Lazy loading for minimal initial footprint
"""

import sys
import os
import json
import time
import threading
import logging
from datetime import datetime, timed<PERSON>ta
from typing import Optional, Dict, Any, Tuple
import traceback

# Core imports - always loaded
from PyQt5.QtWidgets import (QApplication, QWidget, QVBoxLayout, QHBoxLayout,
                            QLabel, QPushButton, QTextEdit, QLineEdit, QDialog,
                            QMessageBox, QProgressBar, QFrame)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread, QObject
from PyQt5.QtGui import QFont, QIcon, QPixmap, QPainter, QColor

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Global configuration
CONFIG = {
    'app_name': 'Abid Ansari AI Assistant',
    'version': '2.0.0',
    'session_duration': 7200,  # 2 hours in seconds
    'free_time_limit': 300,    # 5 minutes in seconds
    'timer_update_interval': 1000,  # 1 second in milliseconds
    'firebase_config': {
        "type": "service_account",
        "project_id": "my-blogs-*************",
        "private_key_id": "placeholder",
        "private_key": "placeholder",
        "client_email": "<EMAIL>",
        "client_id": "placeholder",
        "auth_uri": "https://accounts.google.com/o/oauth2/auth",
        "token_uri": "https://oauth2.googleapis.com/token",
        "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
        "client_x509_cert_url": "placeholder"
    }
}

class LazyLoader:
    """Lazy loading manager for heavy dependencies"""
    
    def __init__(self):
        self._firebase_admin = None
        self._firestore = None
        self._genai = None
        self._mistral = None
        self._openai = None
        self._httpx = None
        
    @property
    def firebase_admin(self):
        if self._firebase_admin is None:
            try:
                import firebase_admin
                from firebase_admin import credentials, firestore
                self._firebase_admin = firebase_admin
                self._firestore = firestore
                logger.info("✅ Firebase modules loaded")
            except ImportError as e:
                logger.error(f"❌ Failed to load Firebase: {e}")
                raise
        return self._firebase_admin
    
    @property
    def firestore(self):
        if self._firestore is None:
            _ = self.firebase_admin  # Trigger firebase loading
        return self._firestore
    
    @property
    def genai(self):
        if self._genai is None:
            try:
                import google.generativeai as genai
                self._genai = genai
                logger.info("✅ Gemini API module loaded")
            except ImportError as e:
                logger.error(f"❌ Failed to load Gemini: {e}")
        return self._genai
    
    @property
    def mistral(self):
        if self._mistral is None:
            try:
                from mistralai.client import MistralClient
                self._mistral = MistralClient
                logger.info("✅ Mistral API module loaded")
            except ImportError as e:
                logger.error(f"❌ Failed to load Mistral: {e}")
        return self._mistral
    
    @property
    def openai(self):
        if self._openai is None:
            try:
                import openai
                self._openai = openai
                logger.info("✅ OpenAI API module loaded")
            except ImportError as e:
                logger.error(f"❌ Failed to load OpenAI: {e}")
        return self._openai
    
    @property
    def httpx(self):
        if self._httpx is None:
            try:
                import httpx
                self._httpx = httpx
                logger.info("✅ HTTPX module loaded")
            except ImportError as e:
                logger.error(f"❌ Failed to load HTTPX: {e}")
        return self._httpx

# Global lazy loader instance
lazy = LazyLoader()

class APIManager:
    """Manages cascading API fallback system"""
    
    def __init__(self):
        self.api_keys = {}
        self.clients = {}
        
    def set_api_keys(self, keys: Dict[str, str]):
        """Set API keys for all services"""
        self.api_keys = keys
        self._initialize_clients()
    
    def _initialize_clients(self):
        """Initialize API clients with available keys"""
        self.clients = {}
        
        # Initialize Gemini
        if self.api_keys.get('gemini'):
            try:
                lazy.genai.configure(api_key=self.api_keys['gemini'])
                self.clients['gemini'] = lazy.genai.GenerativeModel('gemini-pro')
                logger.info("✅ Gemini client initialized")
            except Exception as e:
                logger.error(f"❌ Gemini initialization failed: {e}")
        
        # Initialize Mistral
        if self.api_keys.get('mistral'):
            try:
                self.clients['mistral'] = lazy.mistral(api_key=self.api_keys['mistral'])
                logger.info("✅ Mistral client initialized")
            except Exception as e:
                logger.error(f"❌ Mistral initialization failed: {e}")
        
        # Initialize OpenRouter
        if self.api_keys.get('openrouter'):
            try:
                self.clients['openrouter'] = lazy.httpx.Client(
                    headers={
                        "Authorization": f"Bearer {self.api_keys['openrouter']}",
                        "Content-Type": "application/json"
                    }
                )
                logger.info("✅ OpenRouter client initialized")
            except Exception as e:
                logger.error(f"❌ OpenRouter initialization failed: {e}")
        
        # Initialize OpenAI
        if self.api_keys.get('openai'):
            try:
                self.clients['openai'] = lazy.openai.OpenAI(api_key=self.api_keys['openai'])
                logger.info("✅ OpenAI client initialized")
            except Exception as e:
                logger.error(f"❌ OpenAI initialization failed: {e}")
    
    def get_response(self, prompt: str) -> Tuple[str, str]:
        """Get AI response with cascading fallback"""
        api_order = ['gemini', 'mistral', 'openrouter', 'openai']
        
        for api_name in api_order:
            if api_name not in self.clients:
                logger.warning(f"⚠️ {api_name.upper()} client not available")
                continue
                
            try:
                logger.info(f"🤖 Trying {api_name.upper()} API...")
                response = self._get_response_from_api(api_name, prompt)
                if response:
                    logger.info(f"✅ {api_name.upper()} responded successfully")
                    return response, api_name.upper()
            except Exception as e:
                logger.error(f"❌ {api_name.upper()} failed: {e}")
                continue
        
        return "❌ All AI services are currently unavailable. Please check your API keys.", "ERROR"
    
    def _get_response_from_api(self, api_name: str, prompt: str) -> Optional[str]:
        """Get response from specific API"""
        client = self.clients[api_name]
        
        if api_name == 'gemini':
            response = client.generate_content(prompt)
            return response.text if response else None
            
        elif api_name == 'mistral':
            response = client.chat(
                model="mistral-medium",
                messages=[{"role": "user", "content": prompt}]
            )
            return response.choices[0].message.content if response.choices else None
            
        elif api_name == 'openrouter':
            response = client.post(
                "https://openrouter.ai/api/v1/chat/completions",
                json={
                    "model": "openai/gpt-3.5-turbo",
                    "messages": [{"role": "user", "content": prompt}]
                }
            )
            data = response.json()
            return data['choices'][0]['message']['content'] if data.get('choices') else None
            
        elif api_name == 'openai':
            response = client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[{"role": "user", "content": prompt}]
            )
            return response.choices[0].message.content if response.choices else None
        
        return None

class FirebaseManager:
    """Firebase authentication and user management"""
    
    def __init__(self):
        self.app = None
        self.db = None
        self._initialize_firebase()
    
    def _initialize_firebase(self):
        """Initialize Firebase with lazy loading"""
        try:
            cred = lazy.firebase_admin.credentials.Certificate(CONFIG['firebase_config'])
            self.app = lazy.firebase_admin.initialize_app(cred)
            self.db = lazy.firestore.client()
            logger.info("✅ Firebase initialized successfully")
        except Exception as e:
            logger.error(f"❌ Firebase initialization failed: {e}")
            self.app = None
            self.db = None
    
    def authenticate_user(self, email: str) -> Tuple[bool, str, Optional[Dict]]:
        """Authenticate user with email only"""
        if not self.db:
            return False, "Firebase not initialized", None
        
        try:
            # Check if user exists in Firestore
            users_ref = self.db.collection('users')
            query = users_ref.where('email', '==', email).limit(1).get()
            
            if not query:
                return False, "User not found. Please register first.", None
            
            user_doc = query[0]
            user_data = user_doc.to_dict()
            
            # Check if account is active
            if not user_data.get('isActive', True):
                return False, "Account is deactivated", None
            
            # Update last login
            user_doc.reference.update({'lastLogin': datetime.now().isoformat()})
            
            logger.info(f"✅ User authenticated: {email}")
            return True, "Login successful", user_data
            
        except Exception as e:
            logger.error(f"❌ Authentication error: {e}")
            return False, f"Authentication error: {e}", None
    
    def get_user_api_keys(self, email: str) -> Dict[str, str]:
        """Get user's API keys from Firestore"""
        if not self.db:
            return {}
        
        try:
            users_ref = self.db.collection('users')
            query = users_ref.where('email', '==', email).limit(1).get()
            
            if query:
                user_data = query[0].to_dict()
                return user_data.get('apiKeys', {})
        except Exception as e:
            logger.error(f"❌ Error fetching API keys: {e}")
        
        return {}

class SessionManager(QObject):
    """Manages user session and time tracking"""
    
    time_updated = pyqtSignal(int)  # Remaining time in seconds
    session_expired = pyqtSignal()
    
    def __init__(self):
        super().__init__()
        self.remaining_time = 0
        self.timer = QTimer()
        self.timer.timeout.connect(self._update_time)
        self.is_active = False
    
    def start_session(self, duration_seconds: int):
        """Start a new session with specified duration"""
        self.remaining_time = duration_seconds
        self.is_active = True
        self.timer.start(CONFIG['timer_update_interval'])
        self.time_updated.emit(self.remaining_time)
        logger.info(f"🕐 Session started: {duration_seconds} seconds")
    
    def stop_session(self):
        """Stop the current session"""
        self.timer.stop()
        self.is_active = False
        logger.info("🛑 Session stopped")
    
    def _update_time(self):
        """Update remaining time and emit signals"""
        if self.remaining_time > 0:
            self.remaining_time -= 1
            self.time_updated.emit(self.remaining_time)
        else:
            self.stop_session()
            self.session_expired.emit()
    
    def get_formatted_time(self) -> str:
        """Get formatted time string (HH:MM:SS)"""
        hours = self.remaining_time // 3600
        minutes = (self.remaining_time % 3600) // 60
        seconds = self.remaining_time % 60
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"

class LoginDialog(QDialog):
    """Email-only login dialog"""

    def __init__(self, firebase_manager: FirebaseManager):
        super().__init__()
        self.firebase_manager = firebase_manager
        self.user_data = None
        self.setup_ui()

    def setup_ui(self):
        """Setup login dialog UI"""
        self.setWindowTitle("Login - Abid AI Assistant")
        self.setFixedSize(400, 250)
        self.setModal(True)

        layout = QVBoxLayout()

        # Title
        title = QLabel("🔐 Login to Abid AI Assistant")
        title.setAlignment(Qt.AlignCenter)
        title.setFont(QFont("Arial", 16, QFont.Bold))
        layout.addWidget(title)

        # Email input
        email_label = QLabel("Email Address:")
        self.email_input = QLineEdit()
        self.email_input.setPlaceholderText("Enter your email address")
        self.email_input.returnPressed.connect(self.login)
        layout.addWidget(email_label)
        layout.addWidget(self.email_input)

        # Status label
        self.status_label = QLabel("")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("color: red;")
        layout.addWidget(self.status_label)

        # Buttons
        button_layout = QHBoxLayout()

        login_btn = QPushButton("🔑 Login")
        login_btn.clicked.connect(self.login)
        login_btn.setDefault(True)

        cancel_btn = QPushButton("❌ Cancel")
        cancel_btn.clicked.connect(self.reject)

        button_layout.addWidget(login_btn)
        button_layout.addWidget(cancel_btn)
        layout.addLayout(button_layout)

        # Info text
        info_text = QLabel("Note: Only registered users can access the application.\nContact support for registration.")
        info_text.setAlignment(Qt.AlignCenter)
        info_text.setStyleSheet("color: gray; font-size: 10px;")
        layout.addWidget(info_text)

        self.setLayout(layout)

    def login(self):
        """Perform login with email only"""
        email = self.email_input.text().strip()

        if not email:
            self.status_label.setText("Please enter your email address")
            return

        if not self._validate_email(email):
            self.status_label.setText("Please enter a valid email address")
            return

        self.status_label.setText("Authenticating...")
        self.status_label.setStyleSheet("color: blue;")

        # Perform authentication
        success, message, user_data = self.firebase_manager.authenticate_user(email)

        if success:
            self.user_data = user_data
            self.status_label.setText("Login successful!")
            self.status_label.setStyleSheet("color: green;")
            self.accept()
        else:
            self.status_label.setText(message)
            self.status_label.setStyleSheet("color: red;")

    def _validate_email(self, email: str) -> bool:
        """Basic email validation"""
        import re
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None

class MainWindow(QWidget):
    """Main application window"""

    def __init__(self):
        super().__init__()
        self.firebase_manager = FirebaseManager()
        self.api_manager = APIManager()
        self.session_manager = SessionManager()
        self.current_user = None

        # Connect session signals
        self.session_manager.time_updated.connect(self.update_timer_display)
        self.session_manager.session_expired.connect(self.handle_session_expired)

        self.setup_ui()
        self.show_login_dialog()

    def setup_ui(self):
        """Setup main window UI"""
        self.setWindowTitle(f"{CONFIG['app_name']} v{CONFIG['version']}")
        self.setGeometry(100, 100, 800, 600)

        layout = QVBoxLayout()

        # Header with timer
        header_layout = QHBoxLayout()

        # App title
        title = QLabel(CONFIG['app_name'])
        title.setFont(QFont("Arial", 18, QFont.Bold))
        header_layout.addWidget(title)

        # Timer display
        self.timer_label = QLabel("⏰ Not logged in")
        self.timer_label.setFont(QFont("Arial", 14, QFont.Bold))
        self.timer_label.setStyleSheet("color: red; background-color: #f0f0f0; padding: 5px; border-radius: 5px;")
        header_layout.addWidget(self.timer_label)

        # Logout button
        self.logout_btn = QPushButton("🚪 Logout")
        self.logout_btn.clicked.connect(self.logout)
        self.logout_btn.setVisible(False)
        header_layout.addWidget(self.logout_btn)

        layout.addLayout(header_layout)

        # Separator
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        layout.addWidget(separator)

        # Chat interface
        self.setup_chat_interface(layout)

        self.setLayout(layout)

    def setup_chat_interface(self, layout):
        """Setup chat interface"""
        # Chat display area
        self.chat_display = QTextEdit()
        self.chat_display.setReadOnly(True)
        self.chat_display.setFont(QFont("Arial", 11))
        layout.addWidget(self.chat_display)

        # Input area
        input_layout = QHBoxLayout()

        self.input_field = QLineEdit()
        self.input_field.setPlaceholderText("Type your message here...")
        self.input_field.returnPressed.connect(self.send_message)
        self.input_field.setEnabled(False)

        self.send_btn = QPushButton("📤 Send")
        self.send_btn.clicked.connect(self.send_message)
        self.send_btn.setEnabled(False)

        input_layout.addWidget(self.input_field)
        input_layout.addWidget(self.send_btn)
        layout.addLayout(input_layout)

        # Status bar
        self.status_bar = QLabel("Please log in to start using the AI assistant")
        self.status_bar.setStyleSheet("color: gray; padding: 5px;")
        layout.addWidget(self.status_bar)

    def show_login_dialog(self):
        """Show login dialog"""
        login_dialog = LoginDialog(self.firebase_manager)

        if login_dialog.exec_() == QDialog.Accepted:
            self.current_user = login_dialog.user_data
            self.handle_successful_login()
        else:
            # User cancelled login, close application
            self.close()

    def handle_successful_login(self):
        """Handle successful login"""
        if not self.current_user:
            return

        email = self.current_user.get('email', 'Unknown')

        # Load user's API keys
        api_keys = self.firebase_manager.get_user_api_keys(email)
        self.api_manager.set_api_keys(api_keys)

        # Determine session duration
        time_limit = self.current_user.get('timeLimit', CONFIG['free_time_limit'])

        # Start session
        self.session_manager.start_session(time_limit)

        # Update UI
        self.input_field.setEnabled(True)
        self.send_btn.setEnabled(True)
        self.logout_btn.setVisible(True)
        self.status_bar.setText(f"Logged in as: {email}")

        # Welcome message
        self.add_message("System", f"Welcome {email}! You have {self.session_manager.get_formatted_time()} remaining.")

        logger.info(f"✅ User logged in successfully: {email}")

    def update_timer_display(self, remaining_seconds: int):
        """Update timer display"""
        formatted_time = self.session_manager.get_formatted_time()

        if remaining_seconds > 60:
            color = "green"
        elif remaining_seconds > 30:
            color = "orange"
        else:
            color = "red"

        self.timer_label.setText(f"⏰ Time: {formatted_time}")
        self.timer_label.setStyleSheet(f"color: {color}; background-color: #f0f0f0; padding: 5px; border-radius: 5px; font-weight: bold;")

    def handle_session_expired(self):
        """Handle session expiration"""
        self.add_message("System", "⏰ Your session has expired. Please contact support to extend your access.")

        # Disable interface
        self.input_field.setEnabled(False)
        self.send_btn.setEnabled(False)

        # Show contact info
        QMessageBox.information(
            self,
            "Session Expired",
            "Your session has expired.\n\nContact Support:\n📧 <EMAIL>\n📱 WhatsApp: +91 8104184175"
        )

        logger.info("⏰ Session expired")

    def send_message(self):
        """Send message to AI"""
        if not self.session_manager.is_active:
            return

        message = self.input_field.text().strip()
        if not message:
            return

        self.input_field.clear()
        self.add_message("You", message)

        # Show typing indicator
        self.status_bar.setText("AI is thinking...")

        # Get AI response in separate thread
        thread = threading.Thread(target=self._get_ai_response, args=(message,))
        thread.daemon = True
        thread.start()

    def _get_ai_response(self, message: str):
        """Get AI response in background thread"""
        try:
            response, api_used = self.api_manager.get_response(message)

            # Update UI in main thread
            self.add_message(f"AI ({api_used})", response)
            self.status_bar.setText("Ready")

        except Exception as e:
            logger.error(f"❌ Error getting AI response: {e}")
            self.add_message("System", f"❌ Error: {str(e)}")
            self.status_bar.setText("Error occurred")

    def add_message(self, sender: str, message: str):
        """Add message to chat display"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] <b>{sender}:</b> {message}<br>"
        self.chat_display.append(formatted_message)

    def logout(self):
        """Logout user"""
        self.session_manager.stop_session()
        self.current_user = None

        # Reset UI
        self.input_field.setEnabled(False)
        self.send_btn.setEnabled(False)
        self.logout_btn.setVisible(False)
        self.timer_label.setText("⏰ Not logged in")
        self.timer_label.setStyleSheet("color: red; background-color: #f0f0f0; padding: 5px; border-radius: 5px;")
        self.status_bar.setText("Logged out")
        self.chat_display.clear()

        # Show login dialog again
        self.show_login_dialog()

    def closeEvent(self, event):
        """Handle window close event"""
        self.session_manager.stop_session()
        event.accept()

def main():
    """Main application entry point"""
    try:
        app = QApplication(sys.argv)
        app.setApplicationName(CONFIG['app_name'])
        app.setApplicationVersion(CONFIG['version'])

        # Set application icon if available
        try:
            app.setWindowIcon(QIcon('icon.ico'))
        except:
            pass

        # Create and show main window
        window = MainWindow()
        window.show()

        logger.info(f"🚀 {CONFIG['app_name']} v{CONFIG['version']} started")

        sys.exit(app.exec_())

    except Exception as e:
        logger.error(f"❌ Application error: {e}")
        logger.error(traceback.format_exc())
        sys.exit(1)

if __name__ == "__main__":
    main()
