#!/usr/bin/env python3
"""
Test Script for Bug Fixes - Abid Ansari AI Assistant
This script helps verify that all the implemented bug fixes are working correctly.
"""

import sys
import time
import psutil
import gc
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

def test_memory_usage():
    """Test for memory leaks"""
    print("🧪 Testing Memory Usage...")
    
    # Get initial memory usage
    process = psutil.Process()
    initial_memory = process.memory_info().rss / 1024 / 1024  # MB
    print(f"   Initial memory: {initial_memory:.2f} MB")
    
    return initial_memory

def test_application_startup():
    """Test application startup behavior"""
    print("🧪 Testing Application Startup...")
    
    try:
        # Import the main module
        import abidansari
        print("   ✅ Module imports successfully")
        
        # Check if required classes exist
        assert hasattr(abidansari, 'MainPopup'), "MainPopup class not found"
        assert hasattr(abidansari, 'LoginDialog'), "LoginDialog class not found"
        assert hasattr(abidansari, 'FirebaseManager'), "FirebaseManager class not found"
        print("   ✅ Required classes exist")
        
        # Check if main function exists
        assert hasattr(abidansari, 'main'), "main function not found"
        print("   ✅ Main function exists")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Startup test failed: {e}")
        return False

def test_dialog_state_management():
    """Test dialog state management implementation"""
    print("🧪 Testing Dialog State Management...")
    
    try:
        import abidansari
        
        # Create a test application (don't run it)
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # Create MainPopup instance
        popup = abidansari.MainPopup()
        
        # Check if dialog state variables exist
        assert hasattr(popup, 'dialog_active'), "dialog_active not found"
        assert hasattr(popup, 'login_dialog_active'), "login_dialog_active not found"
        assert hasattr(popup, 'create_account_dialog_active'), "create_account_dialog_active not found"
        print("   ✅ Dialog state variables exist")
        
        # Check initial state
        assert popup.dialog_active == False, "dialog_active should be False initially"
        assert popup.login_dialog_active == False, "login_dialog_active should be False initially"
        assert popup.create_account_dialog_active == False, "create_account_dialog_active should be False initially"
        print("   ✅ Initial dialog states are correct")
        
        # Check if methods exist
        assert hasattr(popup, 'check_authentication'), "check_authentication method not found"
        assert hasattr(popup, 'show_login_popup'), "show_login_popup method not found"
        assert hasattr(popup, 'show_create_account_popup'), "show_create_account_popup method not found"
        print("   ✅ Required methods exist")
        
        # Cleanup
        popup.close_application()
        
        return True
        
    except Exception as e:
        print(f"   ❌ Dialog state test failed: {e}")
        return False

def test_cleanup_methods():
    """Test resource cleanup methods"""
    print("🧪 Testing Cleanup Methods...")
    
    try:
        import abidansari
        
        # Create a test application
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # Create MainPopup instance
        popup = abidansari.MainPopup()
        
        # Check if cleanup-related attributes exist
        cleanup_attrs = ['cursor_timer', 'visibility_timer', 'response_streamer', 'keyboard_listener']
        for attr in cleanup_attrs:
            if hasattr(popup, attr):
                print(f"   ✅ {attr} exists")
            else:
                print(f"   ⚠️ {attr} not found (may be created later)")
        
        # Check if close_application method exists and has proper cleanup
        assert hasattr(popup, 'close_application'), "close_application method not found"
        print("   ✅ close_application method exists")
        
        # Test cleanup (this should not raise exceptions)
        popup.close_application()
        print("   ✅ Cleanup executed without errors")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Cleanup test failed: {e}")
        return False

def test_error_handling():
    """Test error handling improvements"""
    print("🧪 Testing Error Handling...")
    
    try:
        import abidansari
        
        # Test FirebaseManager initialization
        firebase_manager = abidansari.FirebaseManager()
        print("   ✅ FirebaseManager initializes without crashing")
        
        # Test authentication with invalid inputs
        result = firebase_manager.authenticate_user("", "", "")
        assert result[0] == False, "Should fail with empty inputs"
        assert "Missing required" in result[1] or "Invalid" in result[1], "Should have proper error message"
        print("   ✅ Authentication properly handles invalid inputs")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error handling test failed: {e}")
        return False

def run_all_tests():
    """Run all tests and provide summary"""
    print("🧪 Starting Comprehensive Bug Fix Tests")
    print("=" * 50)
    
    tests = [
        ("Application Startup", test_application_startup),
        ("Dialog State Management", test_dialog_state_management),
        ("Cleanup Methods", test_cleanup_methods),
        ("Error Handling", test_error_handling),
    ]
    
    results = []
    initial_memory = test_memory_usage()
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"   ✅ {test_name} PASSED")
            else:
                print(f"   ❌ {test_name} FAILED")
        except Exception as e:
            print(f"   ❌ {test_name} CRASHED: {e}")
            results.append((test_name, False))
    
    # Final memory check
    print(f"\n🧪 Final Memory Check...")
    try:
        gc.collect()  # Force garbage collection
        process = psutil.Process()
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_diff = final_memory - initial_memory
        print(f"   Final memory: {final_memory:.2f} MB")
        print(f"   Memory difference: {memory_diff:.2f} MB")
        
        if memory_diff < 50:  # Less than 50MB increase is acceptable
            print("   ✅ Memory usage is acceptable")
        else:
            print("   ⚠️ Memory usage increased significantly")
    except Exception as e:
        print(f"   ❌ Memory check failed: {e}")
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"   {test_name}: {status}")
    
    print(f"\n🎯 Overall Result: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! Bug fixes are working correctly.")
        return True
    else:
        print("⚠️ Some tests failed. Please review the issues above.")
        return False

if __name__ == "__main__":
    print("🔧 Abid Ansari AI Assistant - Bug Fix Test Suite")
    print("This script tests the implemented bug fixes.")
    print("Make sure you have all required dependencies installed.\n")
    
    try:
        success = run_all_tests()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test suite crashed: {e}")
        sys.exit(1)
