# 🥷 Complete Stealth Mode Guide - Abid Ansari AI Assistant

## 🎯 Overview
Yeh application ab **PERFECT STEALTH MODE** mein run karti hai. Matlab:
- ✅ **Aapko CLEARLY dikhegi** (100% visible)
- ✅ **Taskbar mein NAHI dikhegi**
- ✅ **Screen sharing mein NAHI dikhegi** (Zoom, Teams, etc.)
- ✅ **Online meetings mein interviewer ko NAHI dikhegi**
- ✅ **Screen recording mein NAHI aayegi**
- 🎯 **Perfect balance: <PERSON><PERSON><PERSON> dikh rahi, interviewer ko nahi!**

## 🚀 Quick Start

### 1. Application Start Karein
```bash
python abidansari.py
```

### 2. Stealth Mode Confirmation
Console mein yeh messages dikhenge:
```
🥷 Application started in STEALTH MODE
🥷 Window is HIDDEN from:
   ✅ Taskbar
   ✅ Screen sharing/recording
   ✅ Online meetings (Zoom, Teams, etc.)
   ✅ All external visibility
👁️ Only YOU can see it!
```

## 🔧 Stealth Features

### 1. Taskbar Hiding
- Application taskbar mein bilkul nahi dikhegi
- Windows API use karke completely hidden
- Alt+Tab mein bhi nahi aayegi

### 2. Screen Sharing Protection
- Zoom, Teams, Google Meet mein invisible
- Screen recording software mein nahi aayegi
- Windows Display Affinity API use karta hai

### 3. Popup Hiding
- Login dialog hidden from screen sharing
- Time expired popup hidden
- All message boxes stealth mode mein

### 4. Invisible Launch
- Application start hone par koi indication nahi
- Background mein silently start hoti hai
- Focus steal nahi karti

## 🎮 Usage During Interview

### Interview Se Pehle:
1. Application start karein: `python abidansari.py`
2. Login karein (popup sirf aapko dikhega)
3. Window ko suitable position par move karein
4. Caps Lock test karein

### Interview Ke Dauraan:
1. **Caps Lock** press karein - question input dikhega (sirf aapko)
2. Question type karein
3. **Enter** press karein - AI response aayega (invisible)
4. **Alt** press karein - screenshot le sakte hain (invisible)

### Emergency Controls:
- **Hide Button**: Window ko temporarily hide karne ke liye
- **Shift + Arrow Keys**: Window position change karne ke liye

## 🔍 Transparency Levels

### Maximum Stealth (Recommended for Interviews):
```python
# transparency_config.py mein:
apply_complete_stealth()  # 0.08 transparency
```

### Different Stealth Levels:
- `invisible_mode`: 0.05 transparency (nearly invisible)
- `complete_stealth`: 0.08 transparency (maximum stealth)
- `ghost_mode`: 0.15 transparency (ghost-like)
- `stealth_interview`: 0.2 transparency (interview optimized)

## 🛡️ Technical Implementation

### Window Flags Applied:
```python
Qt.FramelessWindowHint |           # No window frame
Qt.WindowStaysOnTopHint |          # Stay on top
Qt.Tool |                          # Tool window
Qt.WindowDoesNotAcceptFocus |      # Don't steal focus
Qt.SubWindow |                     # Subwindow
Qt.Popup                           # Popup window
```

### Windows API Calls:
- `SetWindowDisplayAffinity()` - Hide from screen capture
- `SetWindowLongW()` - Hide from taskbar
- `WS_EX_TOOLWINDOW` - Tool window style
- `WS_EX_NOACTIVATE` - No activation

## 🔧 Configuration

### Enable/Disable Stealth Mode:
```python
# config.py mein:
ENABLE_STEALTH_MODE = True          # Main toggle
STEALTH_HIDE_FROM_TASKBAR = True    # Taskbar hiding
STEALTH_HIDE_FROM_SCREEN_SHARING = True  # Screen sharing protection
STEALTH_HIDE_ALL_POPUPS = True      # All popups hidden
STEALTH_INVISIBLE_LAUNCH = True     # Silent startup
```

### Runtime Transparency Change:
```python
# Console mein:
popup.set_response_area_transparency(0.05)  # Nearly invisible
popup.set_response_area_transparency(0.3)   # Balanced
```

## 🎯 Interview Tips

### Best Practices:
1. **Position**: Window ko screen ke corner mein rakhein
2. **Size**: Default size (800x600) optimal hai
3. **Transparency**: 0.05-0.1 use karein interviews ke liye
4. **Testing**: Pehle test kar lein ki screen sharing mein nahi dikh rahi

### Keyboard Shortcuts:
- **Caps Lock**: Question input toggle
- **Alt**: Screenshot (300px around cursor)
- **Shift + Arrows**: Window movement
- **Delete**: Screenshot remove
- **Enter**: Submit question
- **Shift + Enter**: New line

## 🚨 Troubleshooting

### Agar Window Dikh Rahi Hai:
1. Check console messages for stealth mode confirmation
2. Restart application
3. Check `stealth_mode.py` file exists

### Agar Taskbar Mein Aa Rahi Hai:
1. Windows API calls check karein
2. Administrator privileges try karein
3. `STEALTH_HIDE_FROM_TASKBAR = True` confirm karein

### Agar Screen Sharing Mein Dikh Rahi Hai:
1. `SetWindowDisplayAffinity` working check karein
2. Different screen sharing software test karein
3. Transparency increase karein (0.05 tak)

## 📞 Quick Commands

```bash
# Maximum stealth start
python abidansari.py

# Check stealth status
# Console mein "🥷 STEALTH MODE ACTIVE" message dekhein

# Emergency hide
# Hide button click karein ya window minimize karein
```

## 🎉 Success Indicators

Application successfully stealth mode mein hai agar:
- ✅ Console mein "🥷 STEALTH MODE ACTIVE" message
- ✅ Taskbar mein icon nahi dikh raha
- ✅ Alt+Tab mein nahi aa raha
- ✅ Screen sharing test mein invisible
- ✅ Sirf aapko window dikh rahi hai

**🎯 Ab aap confidently interviews de sakte hain! Application completely invisible hai interviewer ke liye.**
