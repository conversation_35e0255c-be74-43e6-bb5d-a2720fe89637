"""
Transparency Configuration Helper for Abid Ansari AI Assistant

Use this file to easily adjust transparency settings without modifying the main code.
"""

# ============================================================================
# TRANSPARENCY SETTINGS - ADJUST THESE VALUES AS NEEDED
# ============================================================================

# Response Area Transparency - FIXED for user visibility
# 0.1 = Very transparent (almost invisible)
# 0.3 = Moderately transparent (recommended for stealth)
# 0.5 = Semi-transparent
# 0.7 = Slightly transparent
# 0.9 = Almost opaque (barely transparent)
# 1.0 = Completely opaque (fully visible)
RESPONSE_AREA_TRANSPARENCY = 1.0  # FIXED: Fully visible to user (stealth comes from Windows API)

# Click-through Duration (in milliseconds)
# How long the window stays transparent when clicked
# 100 = Very fast
# 150 = Recommended
# 200 = Slower
CLICK_THROUGH_DURATION = 150

# ============================================================================
# USAGE EXAMPLES
# ============================================================================

def apply_stealth_mode():
    """Apply maximum stealth settings"""
    global RESPONSE_AREA_TRANSPARENCY, CLICK_THROUGH_DURATION
    RESPONSE_AREA_TRANSPARENCY = 0.1  # Very transparent
    CLICK_THROUGH_DURATION = 100      # Fast click-through
    print("🥷 Stealth mode activated - Maximum transparency")

def apply_normal_mode():
    """Apply normal visibility settings"""
    global RESPONSE_AREA_TRANSPARENCY, CLICK_THROUGH_DURATION
    RESPONSE_AREA_TRANSPARENCY = 0.7  # More visible
    CLICK_THROUGH_DURATION = 200      # Slower click-through
    print("👁️ Normal mode activated - Better visibility")

def apply_interview_mode():
    """Apply recommended settings for technical interviews"""
    global RESPONSE_AREA_TRANSPARENCY, CLICK_THROUGH_DURATION
    RESPONSE_AREA_TRANSPARENCY = 0.3  # Balanced transparency
    CLICK_THROUGH_DURATION = 150      # Balanced speed
    print("🎯 Interview mode activated - Balanced stealth")

# ============================================================================
# RUNTIME ADJUSTMENT FUNCTIONS
# ============================================================================

def set_transparency(value):
    """Set transparency value (0.1 to 0.9)"""
    global RESPONSE_AREA_TRANSPARENCY
    RESPONSE_AREA_TRANSPARENCY = max(0.1, min(0.9, value))
    print(f"🔧 Transparency set to: {RESPONSE_AREA_TRANSPARENCY}")

def set_click_duration(duration):
    """Set click-through duration in milliseconds"""
    global CLICK_THROUGH_DURATION
    CLICK_THROUGH_DURATION = max(50, min(500, duration))
    print(f"⏱️ Click-through duration set to: {CLICK_THROUGH_DURATION}ms")

def get_current_settings():
    """Display current settings"""
    print("📊 Current Settings:")
    print(f"   • Transparency: {RESPONSE_AREA_TRANSPARENCY}")
    print(f"   • Click Duration: {CLICK_THROUGH_DURATION}ms")

# ============================================================================
# QUICK PRESETS
# ============================================================================

PRESETS = {
    "maximum_stealth": {"transparency": 0.8, "duration": 100},  # FIXED: Still visible to user
    "interview_mode": {"transparency": 0.9, "duration": 150},   # FIXED: Clearly visible to user
    "normal_use": {"transparency": 1.0, "duration": 200},       # FIXED: Fully visible to user
    "debug_mode": {"transparency": 1.0, "duration": 300},       # FIXED: Fully visible to user
    "user_visible": {"transparency": 1.0, "duration": 150}      # NEW: Guaranteed user visibility
}

def apply_preset(preset_name):
    """Apply a predefined preset"""
    global RESPONSE_AREA_TRANSPARENCY, CLICK_THROUGH_DURATION

    if preset_name in PRESETS:
        preset = PRESETS[preset_name]
        RESPONSE_AREA_TRANSPARENCY = preset["transparency"]
        CLICK_THROUGH_DURATION = preset["duration"]
        print(f"✅ Applied preset '{preset_name}':")
        print(f"   • Transparency: {RESPONSE_AREA_TRANSPARENCY}")
        print(f"   • Duration: {CLICK_THROUGH_DURATION}ms")
    else:
        print(f"❌ Preset '{preset_name}' not found")
        print(f"Available presets: {list(PRESETS.keys())}")

# ============================================================================
# EXAMPLE USAGE
# ============================================================================

if __name__ == "__main__":
    print("🔧 Transparency Configuration Helper")
    print("=" * 50)

    # Show current settings
    get_current_settings()

    print("\n📋 Available presets:")
    for name, settings in PRESETS.items():
        print(f"   • {name}: transparency={settings['transparency']}, duration={settings['duration']}ms")

    print("\n💡 Usage examples:")
    print("   apply_preset('maximum_stealth')")
    print("   set_transparency(0.2)")
    print("   set_click_duration(120)")
