# 🥷 Stealth Settings Guide - Abid Ansari AI Assistant

## 🎯 Problem Solved

✅ **Cursor Issue Fixed**: Response area ab hamesha arrow cursor di<PERSON><PERSON><PERSON>, text cursor nahi  
✅ **Blink Issue Fixed**: Click karne par response area blink nahi karega  
✅ **Click-Through**: Response area par click karne se underlying application ko click pass ho jayega  
✅ **Configurable Transparency**: Aap easily transparency adjust kar sakte hain  

## 🔧 Quick Transparency Adjustment

### Method 1: Edit transparency_config.py file
```python
# transparency_config.py file mein ye values change karein:
RESPONSE_AREA_TRANSPARENCY = 0.3  # 0.1 = very transparent, 0.9 = almost opaque
CLICK_THROUGH_DURATION = 150      # milliseconds
```

### Method 2: Runtime mein change karein
```python
# Application running hai to console mein ye commands use karein:
popup.set_response_area_transparency(0.2)  # More transparent
popup.set_response_area_transparency(0.8)  # Less transparent
```

## 📊 Transparency Values Guide

| Value | Description | Use Case |
|-------|-------------|----------|
| 0.1   | Very transparent (almost invisible) | Maximum stealth for interviews |
| 0.3   | Moderately transparent | **Recommended for interviews** |
| 0.5   | Semi-transparent | Normal use with some stealth |
| 0.7   | Slightly transparent | Debug/development |
| 0.9   | Almost opaque | Testing purposes |

## 🎮 Quick Presets

### Maximum Stealth (Interview Mode)
```python
# transparency_config.py mein:
apply_preset('maximum_stealth')
# Result: transparency=0.1, duration=100ms
```

### Balanced Mode (Recommended)
```python
apply_preset('interview_mode')
# Result: transparency=0.3, duration=150ms
```

### Normal Visibility
```python
apply_preset('normal_use')
# Result: transparency=0.7, duration=200ms
```

## 🔍 Features Implemented

### 1. Cursor Behavior Fix
- Response area par hover karne se cursor arrow hi rahega
- Text cursor kabhi nahi dikhega
- Periodic enforcement har 100ms mein

### 2. Click-Through Functionality
- Response area par click karne se underlying app ko click pass hoga
- Configurable transparency duration
- Window temporarily transparent ho jata hai

### 3. Blink Prevention
- Focus policy set to NoFocus
- Text interaction disabled
- Automatic focus clearing

### 4. Stealth Enhancements
- Selection background transparent
- No outline on focus
- Consistent border styling

## 🚀 Usage Examples

### During Interview:
1. Start application: `python abidansari.py`
2. Set maximum stealth: Edit `transparency_config.py` → `RESPONSE_AREA_TRANSPARENCY = 0.1`
3. Response area ab almost invisible hoga
4. Click-through automatically enabled

### For Development:
1. Set higher transparency: `RESPONSE_AREA_TRANSPARENCY = 0.8`
2. Response area clearly visible for debugging

### Runtime Adjustment:
```python
# Console mein type karein:
popup.set_response_area_transparency(0.2)  # Very transparent
popup.get_current_transparency()           # Check current value
```

## 🎯 Technical Details

### Files Modified:
- `abidansari.py` - Main application with stealth features
- `transparency_config.py` - Easy configuration file
- `STEALTH_SETTINGS.md` - This guide

### Key Changes:
1. **Cursor Control**: `setCursor(Qt.ArrowCursor)` + periodic enforcement
2. **Focus Prevention**: `setFocusPolicy(Qt.NoFocus)`
3. **Text Interaction**: `setTextInteractionFlags(Qt.NoTextInteraction)`
4. **Click-Through**: Temporary window opacity manipulation
5. **Configurable Values**: External config file support

## 💡 Tips for Best Stealth

1. **Set transparency to 0.1-0.3** for interviews
2. **Position window carefully** - not over important areas
3. **Test click-through** before interview
4. **Use Caps Lock** for quick activation
5. **Keep response area small** if possible

## 🔧 Troubleshooting

### If cursor still changes:
- Check if `enforce_cursor_behavior()` is running
- Verify `Qt.ArrowCursor` is set
- Restart application

### If click-through not working:
- Check `CLICK_THROUGH_DURATION` value
- Verify transparency settings
- Test with different applications

### If transparency not applying:
- Check `transparency_config.py` file exists
- Verify import is successful
- Use runtime method as backup

## 📞 Quick Commands

```bash
# Start with maximum stealth
python abidansari.py

# Check current settings (in Python console)
popup.get_current_transparency()

# Change transparency (in Python console)
popup.set_response_area_transparency(0.1)  # Very transparent
popup.set_response_area_transparency(0.5)  # Medium
popup.set_response_area_transparency(0.8)  # Less transparent
```

**Perfect for technical interviews! 🎯**
