import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatChipsModule } from '@angular/material/chips';
import { MatDividerModule } from '@angular/material/divider';
import { FirebaseService, UserData } from '../../services/firebase.service';
import { TimeManagementService, TimeStatus } from '../../services/time-management.service';
import { environment } from '../../../environments/environment';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatProgressBarModule,
    MatChipsModule,
    MatDividerModule
  ],
  template: `
    <div class="dashboard-container">
      <div class="dashboard-header">
        <h1>Welcome back, {{ userData?.displayName || 'User' }}!</h1>
        <p>Manage your AI Assistant account and settings</p>
      </div>

      <div class="dashboard-grid">
        <!-- Account Overview -->
        <mat-card class="overview-card">
          <mat-card-header>
            <mat-card-title>
              <mat-icon>account_circle</mat-icon>
              Account Overview
            </mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div class="account-info">
              <div class="info-item">
                <span class="label">Email:</span>
                <span class="value">{{ userData?.email }}</span>
              </div>
              <div class="info-item">
                <span class="label">Member since:</span>
                <span class="value">{{ getFormattedDate(userData?.lastLogin) }}</span>
              </div>
              <div class="info-item">
                <span class="label">Account status:</span>
                <mat-chip color="primary">Active</mat-chip>
              </div>
            </div>
          </mat-card-content>
        </mat-card>

        <!-- Usage Statistics -->
        <mat-card class="usage-card">
          <mat-card-header>
            <mat-card-title>
              <mat-icon>analytics</mat-icon>
              Usage Statistics
            </mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div class="usage-stats">
              <div class="stat-item">
                <div class="stat-value" [class]="getTimeWarningClass()">{{ getRemainingTime() }}</div>
                <div class="stat-label">Time Remaining</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ timeStatus?.timeLimit || 300 }}s</div>
                <div class="stat-label">Time Limit</div>
              </div>
              <div class="stat-item">
                <div class="stat-value" [class]="getTimeWarningClass()">{{ getUsagePercentage().toFixed(1) }}%</div>
                <div class="stat-label">Usage</div>
              </div>
            </div>
            <mat-progress-bar
              mode="determinate"
              [value]="getUsagePercentage()"
              [color]="timeStatus?.warningLevel === 'critical' ? 'warn' : 'primary'"
              class="usage-progress">
            </mat-progress-bar>
            <div class="time-status" *ngIf="timeStatus">
              <mat-chip
                [color]="canUseService() ? 'primary' : 'warn'"
                [class.expired-chip]="timeStatus.isExpired">
                <mat-icon>{{ canUseService() ? 'check_circle' : 'warning' }}</mat-icon>
                {{ canUseService() ? 'Service Available' : 'Time Expired - Purchase More Time' }}
              </mat-chip>
            </div>
          </mat-card-content>
        </mat-card>

        <!-- API Keys Status -->
        <mat-card class="api-keys-card">
          <mat-card-header>
            <mat-card-title>
              <mat-icon>vpn_key</mat-icon>
              API Keys Status
            </mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div class="api-status-grid">
              <div class="api-item" [class.configured]="hasApiKey('gemini')">
                <mat-icon>{{ hasApiKey('gemini') ? 'check_circle' : 'radio_button_unchecked' }}</mat-icon>
                <span>Gemini API</span>
              </div>
              <div class="api-item" [class.configured]="hasApiKey('mistral')">
                <mat-icon>{{ hasApiKey('mistral') ? 'check_circle' : 'radio_button_unchecked' }}</mat-icon>
                <span>Mistral API</span>
              </div>
              <div class="api-item" [class.configured]="hasApiKey('openai')">
                <mat-icon>{{ hasApiKey('openai') ? 'check_circle' : 'radio_button_unchecked' }}</mat-icon>
                <span>OpenAI API</span>
              </div>
              <div class="api-item" [class.configured]="hasApiKey('openrouter')">
                <mat-icon>{{ hasApiKey('openrouter') ? 'check_circle' : 'radio_button_unchecked' }}</mat-icon>
                <span>OpenRouter API</span>
              </div>
            </div>
            <mat-divider></mat-divider>
            <div class="api-actions">
              <a mat-button routerLink="/api-keys" color="primary">
                <mat-icon>settings</mat-icon>
                Manage API Keys
              </a>
            </div>
          </mat-card-content>
        </mat-card>

        <!-- Quick Actions -->
        <mat-card class="actions-card">
          <mat-card-header>
            <mat-card-title>
              <mat-icon>flash_on</mat-icon>
              Quick Actions
            </mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div class="action-buttons">
              <a mat-raised-button color="primary" routerLink="/download">
                <mat-icon>download</mat-icon>
                Download App
              </a>
              <a mat-button routerLink="/documentation">
                <mat-icon>description</mat-icon>
                Documentation
              </a>
              <a mat-button routerLink="/api-keys">
                <mat-icon>vpn_key</mat-icon>
                API Keys
              </a>
              <a mat-button routerLink="/profile">
                <mat-icon>person</mat-icon>
                Profile
              </a>
            </div>
          </mat-card-content>
        </mat-card>

        <!-- Pricing Information -->
        <mat-card class="pricing-card">
          <mat-card-header>
            <mat-card-title>
              <mat-icon>monetization_on</mat-icon>
              Pricing
            </mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div class="pricing-info">
              <div class="price-display">
                <span class="currency">{{ pricing.currency }}</span>
                <span class="amount">{{ pricing.sessionPrice }}</span>
                <span class="period">per session</span>
              </div>
              <p>Pay only for what you use. No monthly subscriptions or hidden fees.</p>
              <a mat-button routerLink="/pricing" color="primary">
                View Pricing Details
              </a>
            </div>
          </mat-card-content>
        </mat-card>

        <!-- Recent Activity -->
        <mat-card class="activity-card">
          <mat-card-header>
            <mat-card-title>
              <mat-icon>history</mat-icon>
              Recent Activity
            </mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div class="activity-list">
              <div class="activity-item">
                <mat-icon>login</mat-icon>
                <div class="activity-details">
                  <div class="activity-title">Last Login</div>
                  <div class="activity-time">{{ getFormattedDate(userData?.lastLogin) }}</div>
                </div>
              </div>
              <div class="activity-item">
                <mat-icon>vpn_key</mat-icon>
                <div class="activity-details">
                  <div class="activity-title">API Keys</div>
                  <div class="activity-time">{{ getConfiguredKeysCount() }} configured</div>
                </div>
              </div>
              <div class="activity-item">
                <mat-icon>timer</mat-icon>
                <div class="activity-details">
                  <div class="activity-title">Time Usage</div>
                  <div class="activity-time">{{ userData?.timeUsage || 0 }}s used</div>
                </div>
              </div>
            </div>
          </mat-card-content>
        </mat-card>
      </div>
    </div>
  `,
  styles: [`
    .dashboard-container {
      min-height: 100vh;
      padding: 80px 24px 24px;
      background-color: #f5f5f5;
    }

    .dashboard-header {
      text-align: center;
      margin-bottom: 32px;
      max-width: 800px;
      margin-left: auto;
      margin-right: auto;
    }

    .dashboard-header h1 {
      font-size: 2.5rem;
      color: #333;
      margin-bottom: 8px;
    }

    .dashboard-header p {
      font-size: 1.1rem;
      color: #666;
    }

    .dashboard-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
      gap: 24px;
      max-width: 1200px;
      margin: 0 auto;
    }

    .overview-card {
      grid-column: span 2;
    }

    .account-info {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .info-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .label {
      font-weight: 500;
      color: #666;
    }

    .value {
      color: #333;
    }

    .usage-stats {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 16px;
      margin-bottom: 16px;
    }

    .stat-item {
      text-align: center;
    }

    .stat-value {
      font-size: 1.5rem;
      font-weight: 600;
      color: #3f51b5;
    }

    .stat-label {
      font-size: 0.875rem;
      color: #666;
      margin-top: 4px;
    }

    .usage-progress {
      height: 8px;
      border-radius: 4px;
    }

    .api-status-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 12px;
      margin-bottom: 16px;
    }

    .api-item {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px;
      border-radius: 4px;
      background-color: #f5f5f5;
      color: #666;
    }

    .api-item.configured {
      background-color: #e8f5e8;
      color: #2e7d32;
    }

    .api-item mat-icon {
      font-size: 20px;
      width: 20px;
      height: 20px;
    }

    .api-actions {
      margin-top: 16px;
    }

    .time-status {
      margin-top: 16px;
      text-align: center;
    }

    .time-low {
      color: #ff9800 !important;
    }

    .time-medium {
      color: #ff5722 !important;
    }

    .time-high {
      color: #f44336 !important;
    }

    .time-critical {
      color: #d32f2f !important;
      font-weight: bold;
    }

    .expired-chip {
      background-color: #ffebee !important;
      color: #d32f2f !important;
    }

    .action-buttons {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 12px;
    }

    .action-buttons a {
      justify-content: center;
    }

    .pricing-info {
      text-align: center;
    }

    .price-display {
      display: flex;
      align-items: baseline;
      justify-content: center;
      gap: 4px;
      margin-bottom: 8px;
    }

    .currency {
      font-size: 1.2rem;
      color: #666;
    }

    .amount {
      font-size: 2rem;
      font-weight: 600;
      color: #3f51b5;
    }

    .period {
      font-size: 1rem;
      color: #666;
    }

    .pricing-info p {
      color: #666;
      margin-bottom: 16px;
      font-size: 0.875rem;
    }

    .activity-list {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .activity-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 8px;
      background-color: #f8f9fa;
      border-radius: 4px;
    }

    .activity-item mat-icon {
      color: #3f51b5;
      font-size: 20px;
      width: 20px;
      height: 20px;
    }

    .activity-details {
      flex: 1;
    }

    .activity-title {
      font-weight: 500;
      color: #333;
    }

    .activity-time {
      font-size: 0.875rem;
      color: #666;
    }

    @media (max-width: 768px) {
      .dashboard-container {
        padding: 80px 16px 16px;
      }

      .dashboard-header h1 {
        font-size: 2rem;
      }

      .dashboard-grid {
        grid-template-columns: 1fr;
      }

      .overview-card {
        grid-column: span 1;
      }

      .usage-stats {
        grid-template-columns: 1fr;
      }

      .api-status-grid {
        grid-template-columns: 1fr;
      }

      .action-buttons {
        grid-template-columns: 1fr;
      }
    }
  `]
})
export class DashboardComponent implements OnInit {
  userData: UserData | null = null;
  timeStatus: TimeStatus | null = null;
  pricing = environment.pricing;

  constructor(
    private firebaseService: FirebaseService,
    private timeManagementService: TimeManagementService
  ) {}

  async ngOnInit(): Promise<void> {
    await this.loadUserData();
    this.subscribeToTimeStatus();
  }

  async loadUserData(): Promise<void> {
    const user = this.firebaseService.getCurrentUser();
    if (user) {
      this.userData = await this.firebaseService.getUserData(user.uid);
      await this.timeManagementService.loadUserTimeData();
    }
  }

  private subscribeToTimeStatus(): void {
    this.timeManagementService.timeStatus$.subscribe(status => {
      this.timeStatus = status;
    });
  }

  hasApiKey(provider: string): boolean {
    return this.userData?.apiKeys?.[provider as keyof typeof this.userData.apiKeys] ? true : false;
  }

  getUsagePercentage(): number {
    if (!this.timeStatus) return 0;
    return this.timeStatus.percentageUsed;
  }

  getRemainingTime(): string {
    if (!this.timeStatus) return '0m 0s';
    return this.timeStatus.displayTime;
  }

  getTimeWarningClass(): string {
    if (!this.timeStatus) return '';

    switch (this.timeStatus.warningLevel) {
      case 'critical': return 'time-critical';
      case 'high': return 'time-high';
      case 'medium': return 'time-medium';
      case 'low': return 'time-low';
      default: return '';
    }
  }

  canUseService(): boolean {
    return this.timeStatus?.canUseService ?? false;
  }

  async startSession(): Promise<void> {
    const result = await this.timeManagementService.startSession();
    if (!result.success) {
      // Handle error - could show a snackbar or dialog
      console.error('Failed to start session:', result.message);
    }
  }

  getConfiguredKeysCount(): number {
    if (!this.userData?.apiKeys) return 0;
    return Object.values(this.userData.apiKeys).filter(key => key && key.trim()).length;
  }

  getFormattedDate(dateString?: string): string {
    if (!dateString) return 'N/A';
    try {
      return new Date(dateString).toLocaleDateString();
    } catch {
      return 'N/A';
    }
  }
}
