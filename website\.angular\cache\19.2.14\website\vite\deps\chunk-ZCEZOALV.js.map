{"version": 3, "sources": ["../../../../../../node_modules/@angular/cdk/fesm2022/style-loader-Cu9AvjH9.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/backwards-compatibility-DHR38MsD.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, Injector, EnvironmentInjector, ApplicationRef, createComponent, Injectable } from '@angular/core';\n\n/** Apps in which we've loaded styles. */\nconst appsWithLoaders = new WeakMap();\n/**\n * Service that loads structural styles dynamically\n * and ensures that they're only loaded once per app.\n */\nclass _CdkPrivateStyleLoader {\n  _appRef;\n  _injector = inject(Injector);\n  _environmentInjector = inject(EnvironmentInjector);\n  /**\n   * Loads a set of styles.\n   * @param loader Component which will be instantiated to load the styles.\n   */\n  load(loader) {\n    // Resolve the app ref lazily to avoid circular dependency errors if this is called too early.\n    const appRef = this._appRef = this._appRef || this._injector.get(ApplicationRef);\n    let data = appsWithLoaders.get(appRef);\n    // If we haven't loaded for this app before, we have to initialize it.\n    if (!data) {\n      data = {\n        loaders: new Set(),\n        refs: []\n      };\n      appsWithLoaders.set(appRef, data);\n      // When the app is destroyed, we need to clean up all the related loaders.\n      appRef.onDestroy(() => {\n        appsWithLoaders.get(appRef)?.refs.forEach(ref => ref.destroy());\n        appsWithLoaders.delete(appRef);\n      });\n    }\n    // If the loader hasn't been loaded before, we need to instatiate it.\n    if (!data.loaders.has(loader)) {\n      data.loaders.add(loader);\n      data.refs.push(createComponent(loader, {\n        environmentInjector: this._environmentInjector\n      }));\n    }\n  }\n  static ɵfac = function _CdkPrivateStyleLoader_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || _CdkPrivateStyleLoader)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: _CdkPrivateStyleLoader,\n    factory: _CdkPrivateStyleLoader.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_CdkPrivateStyleLoader, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nexport { _CdkPrivateStyleLoader as _ };\n", "import { VERSION } from '@angular/core';\n\n// TODO(crisbeto): remove this function when making breaking changes for v20.\n/**\n * Binds an event listener with specific options in a backwards-compatible way.\n * This function is necessary, because `Renderer2.listen` only supports listener options\n * after 19.1 and during the v19 period we support any 19.x version.\n * @docs-private\n */\nfunction _bindEventWithOptions(renderer, target, eventName, callback, options) {\n  const major = parseInt(VERSION.major);\n  const minor = parseInt(VERSION.minor);\n  // Event options in `listen` are only supported in 19.1 and beyond.\n  // We also allow 0.0.x, because that indicates a build at HEAD.\n  if (major > 19 || major === 19 && minor > 0 || major === 0 && minor === 0) {\n    return renderer.listen(target, eventName, callback, options);\n  }\n  target.addEventListener(eventName, callback, options);\n  return () => {\n    target.removeEventListener(eventName, callback, options);\n  };\n}\nexport { _bindEventWithOptions as _ };\n"], "mappings": ";;;;;;;;;;;;;AAIA,IAAM,kBAAkB,oBAAI,QAAQ;AAKpC,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAC3B;AAAA,EACA,YAAY,OAAO,QAAQ;AAAA,EAC3B,uBAAuB,OAAO,mBAAmB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKjD,KAAK,QAAQ;AAEX,UAAM,SAAS,KAAK,UAAU,KAAK,WAAW,KAAK,UAAU,IAAI,cAAc;AAC/E,QAAI,OAAO,gBAAgB,IAAI,MAAM;AAErC,QAAI,CAAC,MAAM;AACT,aAAO;AAAA,QACL,SAAS,oBAAI,IAAI;AAAA,QACjB,MAAM,CAAC;AAAA,MACT;AACA,sBAAgB,IAAI,QAAQ,IAAI;AAEhC,aAAO,UAAU,MAAM;AACrB,wBAAgB,IAAI,MAAM,GAAG,KAAK,QAAQ,SAAO,IAAI,QAAQ,CAAC;AAC9D,wBAAgB,OAAO,MAAM;AAAA,MAC/B,CAAC;AAAA,IACH;AAEA,QAAI,CAAC,KAAK,QAAQ,IAAI,MAAM,GAAG;AAC7B,WAAK,QAAQ,IAAI,MAAM;AACvB,WAAK,KAAK,KAAK,gBAAgB,QAAQ;AAAA,QACrC,qBAAqB,KAAK;AAAA,MAC5B,CAAC,CAAC;AAAA,IACJ;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,+BAA+B,mBAAmB;AACvE,WAAO,KAAK,qBAAqB,yBAAwB;AAAA,EAC3D;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,wBAAuB;AAAA,IAChC,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;ACjDH,SAAS,sBAAsB,UAAU,QAAQ,WAAW,UAAU,SAAS;AAC7E,QAAM,QAAQ,SAAS,QAAQ,KAAK;AACpC,QAAM,QAAQ,SAAS,QAAQ,KAAK;AAGpC,MAAI,QAAQ,MAAM,UAAU,MAAM,QAAQ,KAAK,UAAU,KAAK,UAAU,GAAG;AACzE,WAAO,SAAS,OAAO,QAAQ,WAAW,UAAU,OAAO;AAAA,EAC7D;AACA,SAAO,iBAAiB,WAAW,UAAU,OAAO;AACpD,SAAO,MAAM;AACX,WAAO,oBAAoB,WAAW,UAAU,OAAO;AAAA,EACzD;AACF;", "names": []}