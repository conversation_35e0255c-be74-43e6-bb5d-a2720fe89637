"""
Configuration file for Abid Ansari AI Assistant
Contains all configurable settings including WhatsApp contact number
"""

# ============================================================================
# CONTACT INFORMATION
# ============================================================================

# WhatsApp contact number for access expired popup
WHATSAPP_NUMBER = "8104184175"

# Support email (optional)
SUPPORT_EMAIL = "<EMAIL>"

# ============================================================================
# TIME MANAGEMENT SETTINGS
# ============================================================================

# Default time limit for new users (in seconds)
DEFAULT_TIME_LIMIT = 300  # 5 minutes

# Time update interval (in seconds)
TIME_UPDATE_INTERVAL = 1  # Update every second

# Warning threshold (in seconds) - when to show warning
TIME_WARNING_THRESHOLD = 60  # Show warning when 1 minute left

# ============================================================================
# AUTHENTICATION SETTINGS
# ============================================================================

# Password requirements
MIN_PASSWORD_LENGTH = 6
REQUIRE_UPPERCASE = False
REQUIRE_LOWERCASE = False
REQUIRE_NUMBERS = False
REQUIRE_SPECIAL_CHARS = False

# Session settings
SESSION_TIMEOUT = 3600  # 1 hour in seconds
REMEMBER_ME_DURATION = 86400 * 7  # 7 days in seconds

# ============================================================================
# UI SETTINGS
# ============================================================================

# Popup modal dimensions
LOGIN_POPUP_WIDTH = 400
LOGIN_POPUP_HEIGHT = 300
SIGNUP_POPUP_WIDTH = 450
SIGNUP_POPUP_HEIGHT = 400
ACCESS_EXPIRED_POPUP_WIDTH = 350
ACCESS_EXPIRED_POPUP_HEIGHT = 200

# Time display format
TIME_DISPLAY_FORMAT = "H:M:S"  # Hours:Minutes:Seconds

# ============================================================================
# STEALTH MODE SETTINGS
# ============================================================================

# Enable stealth mode (hides from taskbar, screen sharing, etc.)
ENABLE_STEALTH_MODE = True

# Stealth mode features
STEALTH_HIDE_FROM_TASKBAR = True
STEALTH_HIDE_FROM_SCREEN_SHARING = True
STEALTH_HIDE_ALL_POPUPS = True
STEALTH_INVISIBLE_LAUNCH = True
STEALTH_HIDE_SYSTEM_TRAY = True

# Stealth mode window behavior
STEALTH_START_HIDDEN = True
STEALTH_NO_FOCUS_STEAL = True
STEALTH_BYPASS_WINDOW_MANAGER = True

# ============================================================================
# DEVICE RESTRICTION SETTINGS
# ============================================================================

# Enable device-based restrictions
ENABLE_DEVICE_RESTRICTIONS = True

# Maximum devices per user
MAX_DEVICES_PER_USER = 1

# Device fingerprint method
DEVICE_ID_METHOD = "hardware"  # "hardware" or "simple"

# ============================================================================
# FIREBASE SETTINGS
# ============================================================================

# Collection names
USERS_COLLECTION = "users"
SESSIONS_COLLECTION = "sessions"
DEVICES_COLLECTION = "devices"

# ============================================================================
# ERROR MESSAGES
# ============================================================================

ERROR_MESSAGES = {
    "invalid_email": "Please enter a valid email address.",
    "password_too_short": f"Password must be at least {MIN_PASSWORD_LENGTH} characters long.",
    "passwords_dont_match": "Passwords do not match.",
    "email_already_exists": "An account with this email already exists.",
    "invalid_credentials": "Invalid email or password.",
    "network_error": "Network error. Please check your connection.",
    "account_creation_failed": "Failed to create account. Please try again.",
    "login_failed": "Login failed. Please check your credentials.",
    "time_expired": "Your time has expired. Please contact support.",
    "device_limit_exceeded": "Device limit exceeded. Please contact support.",
    "authentication_required": "Please log in to continue.",
    "session_expired": "Your session has expired. Please log in again."
}

# ============================================================================
# SUCCESS MESSAGES
# ============================================================================

SUCCESS_MESSAGES = {
    "account_created": "Account created successfully! You have 5 minutes of access.",
    "login_successful": "Login successful! Welcome back.",
    "logout_successful": "Logged out successfully.",
    "time_updated": "Time limit updated successfully."
}

# ============================================================================
# UTILITY FUNCTIONS
# ============================================================================

def get_whatsapp_url():
    """Generate WhatsApp URL for contact"""
    message = "Hi, I need help with my Abid AI Assistant access."
    import urllib.parse
    encoded_message = urllib.parse.quote(message)
    return f"https://wa.me/{WHATSAPP_NUMBER}?text={encoded_message}"

def format_time(seconds):
    """Format seconds into H:M:S format"""
    if seconds < 0:
        return "0:0:0"

    hours = seconds // 3600
    minutes = (seconds % 3600) // 60
    secs = seconds % 60

    return f"{hours}:{minutes:02d}:{secs:02d}"

def validate_email(email):
    """Basic email validation"""
    import re
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def validate_password(password):
    """Validate password based on requirements"""
    if len(password) < MIN_PASSWORD_LENGTH:
        return False, ERROR_MESSAGES["password_too_short"]

    if REQUIRE_UPPERCASE and not any(c.isupper() for c in password):
        return False, "Password must contain at least one uppercase letter."

    if REQUIRE_LOWERCASE and not any(c.islower() for c in password):
        return False, "Password must contain at least one lowercase letter."

    if REQUIRE_NUMBERS and not any(c.isdigit() for c in password):
        return False, "Password must contain at least one number."

    if REQUIRE_SPECIAL_CHARS and not any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password):
        return False, "Password must contain at least one special character."

    return True, "Password is valid."

def get_device_id():
    """Generate unique device identifier"""
    import hashlib
    import uuid

    if DEVICE_ID_METHOD == "hardware":
        try:
            import platform
            import subprocess

            # Get hardware info
            system = platform.system()
            machine = platform.machine()
            processor = platform.processor()

            # Get additional hardware identifiers
            if system == "Windows":
                try:
                    # Get motherboard serial
                    result = subprocess.run(['wmic', 'baseboard', 'get', 'serialnumber'],
                                          capture_output=True, text=True)
                    motherboard = result.stdout.strip().split('\n')[-1].strip()
                except:
                    motherboard = "unknown"
            else:
                motherboard = "unknown"

            # Create device fingerprint
            device_string = f"{system}-{machine}-{processor}-{motherboard}"
            device_id = hashlib.sha256(device_string.encode()).hexdigest()[:16]
            return device_id

        except Exception as e:
            print(f"Error generating hardware device ID: {e}")
            # Fallback to simple method
            return get_simple_device_id()
    else:
        return get_simple_device_id()

def get_simple_device_id():
    """Generate simple device identifier"""
    import hashlib
    import uuid

    try:
        import platform
        device_string = f"{platform.node()}-{platform.system()}-{platform.machine()}"
        device_id = hashlib.sha256(device_string.encode()).hexdigest()[:16]
        return device_id
    except:
        # Ultimate fallback
        return hashlib.sha256(str(uuid.getnode()).encode()).hexdigest()[:16]

# ============================================================================
# CONFIGURATION VALIDATION
# ============================================================================

def validate_config():
    """Validate configuration settings"""
    errors = []

    if not WHATSAPP_NUMBER or not WHATSAPP_NUMBER.isdigit():
        errors.append("Invalid WhatsApp number")

    if DEFAULT_TIME_LIMIT <= 0:
        errors.append("Default time limit must be positive")

    if TIME_UPDATE_INTERVAL <= 0:
        errors.append("Time update interval must be positive")

    if MIN_PASSWORD_LENGTH < 1:
        errors.append("Minimum password length must be at least 1")

    return errors

# Validate configuration on import
config_errors = validate_config()
if config_errors:
    print("Configuration errors found:")
    for error in config_errors:
        print(f"  - {error}")
