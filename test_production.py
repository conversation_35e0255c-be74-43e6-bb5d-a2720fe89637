"""
Test Suite for Abid Ansari AI Assistant Production Version
Comprehensive testing of all features and functionality
"""

import sys
import os
import unittest
import time
import threading
from unittest.mock import Mock, patch, MagicMock

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class TestProductionConfig(unittest.TestCase):
    """Test production configuration"""
    
    def setUp(self):
        """Setup test environment"""
        try:
            from production_config import ProductionConfig
            self.config = ProductionConfig
        except ImportError:
            self.skipTest("Production config not available")
    
    def test_config_validation(self):
        """Test configuration validation"""
        self.assertTrue(self.config.validate_config())
    
    def test_required_settings(self):
        """Test required configuration settings"""
        self.assertIsNotNone(self.config.APP_NAME)
        self.assertIsNotNone(self.config.VERSION)
        self.assertGreater(self.config.DEFAULT_SESSION_DURATION, 0)
        self.assertGreater(self.config.FREE_TIME_LIMIT, 0)
        self.assertIsInstance(self.config.API_FALLBACK_ORDER, list)
        self.assertGreater(len(self.config.API_FALLBACK_ORDER), 0)
    
    def test_contact_info(self):
        """Test contact information"""
        self.assertIn('email', self.config.CONTACT_INFO)
        self.assertIn('whatsapp', self.config.CONTACT_INFO)
        self.assertTrue(self.config.CONTACT_INFO['email'])
        self.assertTrue(self.config.CONTACT_INFO['whatsapp'])

class TestLazyLoader(unittest.TestCase):
    """Test lazy loading functionality"""
    
    def setUp(self):
        """Setup test environment"""
        try:
            from abid_ai_production import LazyLoader
            self.loader = LazyLoader()
        except ImportError:
            self.skipTest("Main application not available")
    
    def test_lazy_loading_properties(self):
        """Test lazy loading properties exist"""
        # Test that properties exist without triggering loading
        self.assertTrue(hasattr(self.loader, 'firebase_admin'))
        self.assertTrue(hasattr(self.loader, 'firestore'))
        self.assertTrue(hasattr(self.loader, 'genai'))
        self.assertTrue(hasattr(self.loader, 'mistral'))
        self.assertTrue(hasattr(self.loader, 'openai'))
        self.assertTrue(hasattr(self.loader, 'httpx'))

class TestAPIManager(unittest.TestCase):
    """Test API management and fallback system"""
    
    def setUp(self):
        """Setup test environment"""
        try:
            from abid_ai_production import APIManager
            self.api_manager = APIManager()
        except ImportError:
            self.skipTest("Main application not available")
    
    def test_api_manager_initialization(self):
        """Test API manager initialization"""
        self.assertIsInstance(self.api_manager.api_keys, dict)
        self.assertIsInstance(self.api_manager.clients, dict)
    
    def test_set_api_keys(self):
        """Test setting API keys"""
        test_keys = {
            'gemini': 'test_gemini_key',
            'mistral': 'test_mistral_key',
            'openrouter': 'test_openrouter_key',
            'openai': 'test_openai_key'
        }
        
        self.api_manager.set_api_keys(test_keys)
        self.assertEqual(self.api_manager.api_keys, test_keys)
    
    @patch('abid_ai_production.lazy')
    def test_fallback_order(self, mock_lazy):
        """Test API fallback order"""
        # Mock the lazy loader
        mock_lazy.genai = Mock()
        mock_lazy.mistral = Mock()
        mock_lazy.httpx = Mock()
        mock_lazy.openai = Mock()
        
        # Test that fallback order is correct
        test_keys = {
            'gemini': 'test_key',
            'mistral': 'test_key',
            'openrouter': 'test_key',
            'openai': 'test_key'
        }
        
        self.api_manager.set_api_keys(test_keys)
        
        # The fallback order should be: gemini, mistral, openrouter, openai
        response, api_used = self.api_manager.get_response("test prompt")
        # Since we're mocking, we expect an error response
        self.assertIn("unavailable", response.lower())

class TestSessionManager(unittest.TestCase):
    """Test session management functionality"""
    
    def setUp(self):
        """Setup test environment"""
        try:
            from abid_ai_production import SessionManager
            from PyQt5.QtWidgets import QApplication
            
            # Create QApplication if it doesn't exist
            if not QApplication.instance():
                self.app = QApplication([])
            else:
                self.app = QApplication.instance()
            
            self.session_manager = SessionManager()
        except ImportError:
            self.skipTest("Main application not available")
    
    def test_session_initialization(self):
        """Test session manager initialization"""
        self.assertEqual(self.session_manager.remaining_time, 0)
        self.assertFalse(self.session_manager.is_active)
    
    def test_start_session(self):
        """Test starting a session"""
        duration = 300  # 5 minutes
        self.session_manager.start_session(duration)
        
        self.assertEqual(self.session_manager.remaining_time, duration)
        self.assertTrue(self.session_manager.is_active)
    
    def test_stop_session(self):
        """Test stopping a session"""
        self.session_manager.start_session(300)
        self.session_manager.stop_session()
        
        self.assertFalse(self.session_manager.is_active)
    
    def test_formatted_time(self):
        """Test time formatting"""
        self.session_manager.remaining_time = 3661  # 1 hour, 1 minute, 1 second
        formatted = self.session_manager.get_formatted_time()
        self.assertEqual(formatted, "01:01:01")
        
        self.session_manager.remaining_time = 61  # 1 minute, 1 second
        formatted = self.session_manager.get_formatted_time()
        self.assertEqual(formatted, "00:01:01")

class TestFirebaseManager(unittest.TestCase):
    """Test Firebase integration"""
    
    def setUp(self):
        """Setup test environment"""
        try:
            from abid_ai_production import FirebaseManager
            self.firebase_manager = FirebaseManager()
        except ImportError:
            self.skipTest("Main application not available")
    
    def test_firebase_initialization(self):
        """Test Firebase manager initialization"""
        # Firebase may not be properly configured in test environment
        # Just test that the manager exists
        self.assertIsNotNone(self.firebase_manager)
    
    def test_email_validation(self):
        """Test email validation in login dialog"""
        try:
            from abid_ai_production import LoginDialog
            from PyQt5.QtWidgets import QApplication
            
            if not QApplication.instance():
                app = QApplication([])
            
            dialog = LoginDialog(self.firebase_manager)
            
            # Test valid emails
            self.assertTrue(dialog._validate_email("<EMAIL>"))
            self.assertTrue(dialog._validate_email("<EMAIL>"))
            
            # Test invalid emails
            self.assertFalse(dialog._validate_email("invalid-email"))
            self.assertFalse(dialog._validate_email("@domain.com"))
            self.assertFalse(dialog._validate_email("user@"))
            
        except ImportError:
            self.skipTest("UI components not available")

class TestIntegration(unittest.TestCase):
    """Integration tests for the complete system"""
    
    def setUp(self):
        """Setup integration test environment"""
        try:
            from PyQt5.QtWidgets import QApplication
            if not QApplication.instance():
                self.app = QApplication([])
            else:
                self.app = QApplication.instance()
        except ImportError:
            self.skipTest("PyQt5 not available")
    
    def test_application_startup(self):
        """Test application startup sequence"""
        try:
            from launcher import LauncherApp
            launcher = LauncherApp()
            
            # Test that launcher can be created
            self.assertIsNotNone(launcher)
            
        except ImportError:
            self.skipTest("Launcher not available")
    
    def test_configuration_loading(self):
        """Test configuration loading"""
        try:
            from production_config import ProductionConfig
            config = ProductionConfig.get_config()
            
            # Test that configuration is properly structured
            self.assertIn('app', config)
            self.assertIn('session', config)
            self.assertIn('api', config)
            self.assertIn('firebase', config)
            self.assertIn('contact', config)
            
        except ImportError:
            self.skipTest("Configuration not available")

class TestBuildSystem(unittest.TestCase):
    """Test build system functionality"""
    
    def test_build_script_exists(self):
        """Test that build script exists"""
        self.assertTrue(os.path.exists('build_production.py'))
    
    def test_requirements_file(self):
        """Test requirements file"""
        self.assertTrue(os.path.exists('requirements.txt'))
        
        with open('requirements.txt', 'r') as f:
            content = f.read()
            
        # Check for essential dependencies
        self.assertIn('PyQt5', content)
        self.assertIn('firebase-admin', content)
        self.assertIn('google-generativeai', content)
        self.assertIn('mistralai', content)
        self.assertIn('openai', content)
        self.assertIn('httpx', content)

def run_performance_test():
    """Run performance tests"""
    print("\n🚀 Running Performance Tests...")
    
    # Test startup time
    start_time = time.time()
    try:
        import abid_ai_production
        import_time = time.time() - start_time
        print(f"✅ Main module import time: {import_time:.2f}s")
        
        if import_time > 5.0:
            print("⚠️ Warning: Import time is high, consider optimization")
        
    except ImportError as e:
        print(f"❌ Failed to import main module: {e}")
    
    # Test configuration loading
    start_time = time.time()
    try:
        import production_config
        config_time = time.time() - start_time
        print(f"✅ Configuration loading time: {config_time:.2f}s")
        
    except ImportError as e:
        print(f"❌ Failed to load configuration: {e}")

def main():
    """Main test runner"""
    print("🧪 Abid Ansari AI Assistant - Production Test Suite")
    print("=" * 60)
    
    # Run unit tests
    print("\n📋 Running Unit Tests...")
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Add test classes
    test_classes = [
        TestProductionConfig,
        TestLazyLoader,
        TestAPIManager,
        TestSessionManager,
        TestFirebaseManager,
        TestIntegration,
        TestBuildSystem
    ]
    
    for test_class in test_classes:
        tests = loader.loadTestsFromTestCase(test_class)
        suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Run performance tests
    run_performance_test()
    
    # Summary
    print("\n📊 Test Summary:")
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Skipped: {len(result.skipped)}")
    
    if result.failures:
        print("\n❌ Failures:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback}")
    
    if result.errors:
        print("\n💥 Errors:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback}")
    
    # Return exit code
    if result.failures or result.errors:
        return 1
    else:
        print("\n✅ All tests passed!")
        return 0

if __name__ == "__main__":
    sys.exit(main())
