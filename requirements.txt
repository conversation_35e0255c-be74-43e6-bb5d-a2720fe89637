# Abid Ansari AI Assistant Requirements - Production Ready

# Core GUI Framework
PyQt5==5.15.10

# AI Services with Fallback Support
google-generativeai>=0.8.0
mistralai>=1.0.0
openai>=1.0.0
httpx>=0.25.0  # For OpenRouter API

# Firebase Integration
firebase-admin>=6.5.0
google-cloud-firestore>=2.16.0

# Input/Output Handling
pynput>=1.7.6
Pillow>=10.0.0
pyautogui>=0.9.54

# HTTP Requests
requests>=2.31.0

# System Integration
pywin32>=306; sys_platform == "win32"
psutil>=5.9.0

# Build Tools (for development)
pyinstaller>=6.0.0

# Additional Dependencies
python-dateutil>=2.8.2
six>=1.16.0
certifi>=2023.7.22
charset-normalizer>=3.2.0
idna>=3.4
urllib3>=2.0.4

# Production Dependencies
cryptography>=41.0.0  # For secure API key handling
python-dotenv>=1.0.0  # For environment variables
