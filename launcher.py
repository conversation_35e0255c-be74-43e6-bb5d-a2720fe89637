"""
Abid Ansari AI Assistant - Optimized Launcher
Minimal initial footprint with lazy loading for production distribution
"""

import sys
import os
import time
import threading
from pathlib import Path

# Minimal imports for fast startup
from PyQt5.QtWidgets import QApplication, QSplashScreen, QLabel, QVBoxLayout, QWidget, QProgressBar
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread
from PyQt5.QtGui import QFont, QPixmap, QPainter, QColor

class LoadingThread(QThread):
    """Background thread for loading heavy dependencies"""
    
    progress_updated = pyqtSignal(int, str)
    loading_complete = pyqtSignal()
    error_occurred = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        self.modules_loaded = {}
    
    def run(self):
        """Load modules in background"""
        modules_to_load = [
            ("production_config", "Loading configuration..."),
            ("firebase_admin", "Loading Firebase..."),
            ("google.generativeai", "Loading Gemini AI..."),
            ("mistralai", "Loading Mistral AI..."),
            ("openai", "Loading OpenAI..."),
            ("httpx", "Loading HTTP client..."),
            ("abid_ai_production", "Loading main application...")
        ]
        
        total_modules = len(modules_to_load)
        
        for i, (module_name, description) in enumerate(modules_to_load):
            try:
                self.progress_updated.emit(int((i / total_modules) * 100), description)
                
                if module_name == "abid_ai_production":
                    # Import main application
                    import abid_ai_production
                    self.modules_loaded['main_app'] = abid_ai_production
                else:
                    # Import other modules
                    if module_name == "production_config":
                        import production_config
                        self.modules_loaded[module_name] = production_config
                    elif module_name == "firebase_admin":
                        try:
                            import firebase_admin
                            self.modules_loaded[module_name] = firebase_admin
                        except ImportError:
                            print(f"⚠️ {module_name} not available - will be loaded on demand")
                    elif module_name == "google.generativeai":
                        try:
                            import google.generativeai
                            self.modules_loaded[module_name] = google.generativeai
                        except ImportError:
                            print(f"⚠️ {module_name} not available - will be loaded on demand")
                    elif module_name == "mistralai":
                        try:
                            import mistralai
                            self.modules_loaded[module_name] = mistralai
                        except ImportError:
                            print(f"⚠️ {module_name} not available - will be loaded on demand")
                    elif module_name == "openai":
                        try:
                            import openai
                            self.modules_loaded[module_name] = openai
                        except ImportError:
                            print(f"⚠️ {module_name} not available - will be loaded on demand")
                    elif module_name == "httpx":
                        try:
                            import httpx
                            self.modules_loaded[module_name] = httpx
                        except ImportError:
                            print(f"⚠️ {module_name} not available - will be loaded on demand")
                
                # Small delay to show progress
                time.sleep(0.1)
                
            except Exception as e:
                error_msg = f"Failed to load {module_name}: {str(e)}"
                print(f"❌ {error_msg}")
                # Don't emit error for optional modules
                if module_name in ["firebase_admin", "google.generativeai", "mistralai", "openai", "httpx"]:
                    continue
                else:
                    self.error_occurred.emit(error_msg)
                    return
        
        self.progress_updated.emit(100, "Loading complete!")
        time.sleep(0.5)  # Brief pause to show completion
        self.loading_complete.emit()

class SplashScreen(QSplashScreen):
    """Custom splash screen with loading progress"""
    
    def __init__(self):
        # Create a simple splash screen
        pixmap = QPixmap(400, 300)
        pixmap.fill(QColor(45, 45, 45))
        
        super().__init__(pixmap)
        self.setWindowFlags(Qt.WindowStaysOnTopHint | Qt.SplashScreen)
        
        # Setup UI elements
        self.setup_ui()
        
        # Start loading thread
        self.loading_thread = LoadingThread()
        self.loading_thread.progress_updated.connect(self.update_progress)
        self.loading_thread.loading_complete.connect(self.loading_finished)
        self.loading_thread.error_occurred.connect(self.handle_error)
        
        self.main_app = None
    
    def setup_ui(self):
        """Setup splash screen UI"""
        # Create layout
        widget = QWidget()
        layout = QVBoxLayout()
        
        # Title
        title = QLabel("Abid Ansari AI Assistant")
        title.setAlignment(Qt.AlignCenter)
        title.setFont(QFont("Arial", 18, QFont.Bold))
        title.setStyleSheet("color: white; margin: 20px;")
        layout.addWidget(title)
        
        # Version
        version = QLabel("Version 2.0.0")
        version.setAlignment(Qt.AlignCenter)
        version.setFont(QFont("Arial", 12))
        version.setStyleSheet("color: #cccccc; margin-bottom: 20px;")
        layout.addWidget(version)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #555555;
                border-radius: 5px;
                text-align: center;
                background-color: #333333;
                color: white;
            }
            QProgressBar::chunk {
                background-color: #4CAF50;
                border-radius: 3px;
            }
        """)
        layout.addWidget(self.progress_bar)
        
        # Status label
        self.status_label = QLabel("Initializing...")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setFont(QFont("Arial", 10))
        self.status_label.setStyleSheet("color: #cccccc; margin: 10px;")
        layout.addWidget(self.status_label)
        
        # Footer
        footer = QLabel("© 2024 Abid Ansari. All rights reserved.")
        footer.setAlignment(Qt.AlignCenter)
        footer.setFont(QFont("Arial", 8))
        footer.setStyleSheet("color: #888888; margin-top: 20px;")
        layout.addWidget(footer)
        
        widget.setLayout(layout)
        
        # Paint the widget onto the splash screen
        painter = QPainter(self.pixmap())
        widget.render(painter)
        painter.end()
    
    def start_loading(self):
        """Start the loading process"""
        self.show()
        self.loading_thread.start()
    
    def update_progress(self, value, message):
        """Update progress bar and status"""
        self.progress_bar.setValue(value)
        self.status_label.setText(message)
        
        # Repaint the splash screen
        self.repaint()
    
    def loading_finished(self):
        """Handle loading completion"""
        try:
            # Get the main application from loading thread
            main_app_module = self.loading_thread.modules_loaded.get('main_app')
            
            if main_app_module:
                # Create main window
                self.main_app = main_app_module.MainWindow()
                self.main_app.show()
                
                # Close splash screen
                self.close()
            else:
                self.handle_error("Failed to load main application")
                
        except Exception as e:
            self.handle_error(f"Error starting application: {str(e)}")
    
    def handle_error(self, error_message):
        """Handle loading errors"""
        print(f"❌ {error_message}")
        
        # Show error message
        from PyQt5.QtWidgets import QMessageBox
        msg = QMessageBox()
        msg.setIcon(QMessageBox.Critical)
        msg.setWindowTitle("Loading Error")
        msg.setText("Failed to start application")
        msg.setDetailedText(error_message)
        msg.setStandardButtons(QMessageBox.Ok)
        msg.exec_()
        
        # Close application
        QApplication.quit()

class LauncherApp:
    """Main launcher application"""
    
    def __init__(self):
        self.app = None
        self.splash = None
    
    def run(self):
        """Run the launcher"""
        try:
            # Create QApplication
            self.app = QApplication(sys.argv)
            self.app.setApplicationName("Abid Ansari AI Assistant")
            self.app.setApplicationVersion("2.0.0")
            
            # Set application properties for better Windows integration
            if sys.platform == "win32":
                try:
                    import ctypes
                    # Set app user model ID for proper taskbar grouping
                    ctypes.windll.shell32.SetCurrentProcessExplicitAppUserModelID("AbidAnsari.AIAssistant.2.0")
                except:
                    pass
            
            # Create and show splash screen
            self.splash = SplashScreen()
            self.splash.start_loading()
            
            # Run application
            return self.app.exec_()
            
        except Exception as e:
            print(f"❌ Launcher error: {e}")
            return 1
    
    def cleanup(self):
        """Cleanup resources"""
        if self.splash:
            self.splash.close()
        if self.app:
            self.app.quit()

def main():
    """Main entry point"""
    print("🚀 Starting Abid Ansari AI Assistant...")
    
    launcher = LauncherApp()
    
    try:
        exit_code = launcher.run()
        return exit_code
    except KeyboardInterrupt:
        print("\n⏹️ Application interrupted by user")
        return 0
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return 1
    finally:
        launcher.cleanup()

if __name__ == "__main__":
    sys.exit(main())
