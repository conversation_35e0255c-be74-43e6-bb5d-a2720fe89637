"""
Stealth Mode Manager for Abid Ansari AI Assistant

This module provides comprehensive stealth functionality to hide the application
from taskbar, screen sharing, and all external visibility while maintaining
full functionality.
"""

import ctypes
from ctypes import wintypes
from PyQt5.QtCore import Qt
from PyQt5.QtWidgets import QWidget, QD<PERSON>og, QMessageBox

# Import configuration
try:
    from config import (
        ENABLE_STEALTH_MODE, STEALTH_HIDE_FROM_TASKBAR, STEALTH_HIDE_FROM_SCREEN_SHARING,
        STEALTH_HIDE_ALL_POPUPS, STEALTH_INVISIBLE_LAUNCH, STEALTH_HIDE_SYSTEM_TRAY,
        STEALTH_START_HIDDEN, STEALTH_NO_FOCUS_STEAL, STEALTH_BYPASS_WINDOW_MANAGER
    )
except ImportError:
    # Fallback values if config import fails
    ENABLE_STEALTH_MODE = True
    STEALTH_HIDE_FROM_TASKBAR = True
    STEALTH_HIDE_FROM_SCREEN_SHARING = True
    STEALTH_HIDE_ALL_POPUPS = True
    STEALTH_INVISIBLE_LAUNCH = True
    STEALTH_HIDE_SYSTEM_TRAY = True
    STEALTH_START_HIDDEN = True
    STEALTH_NO_FOCUS_STEAL = True
    STEALTH_BYPASS_WINDOW_MANAGER = True

class StealthModeManager:
    """Manages all stealth mode functionality"""

    def __init__(self):
        self.stealth_enabled = ENABLE_STEALTH_MODE
        self.original_window_flags = {}
        print(f"🥷 Stealth Mode Manager initialized - Enabled: {self.stealth_enabled}")

    def get_stealth_window_flags(self):
        """Get window flags for stealth mode - FIXED for user visibility"""
        if not self.stealth_enabled:
            return Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint | Qt.Tool

        # FIXED: Less aggressive flags that maintain user visibility and input functionality
        # Only use flags that hide from taskbar but preserve user interaction
        flags = (
            Qt.FramelessWindowHint |           # No window frame
            Qt.WindowStaysOnTopHint |          # Stay on top
            Qt.Tool                            # Tool window (helps hide from taskbar)
            # REMOVED: Qt.WindowDoesNotAcceptFocus - this prevents input field interaction
            # REMOVED: Qt.SubWindow - this can make window invisible to user
            # REMOVED: Qt.Popup - this can cause window to disappear
        )

        # Only add bypass window manager on Linux if specifically needed
        if STEALTH_BYPASS_WINDOW_MANAGER and hasattr(Qt, 'X11BypassWindowManagerHint'):
            flags |= Qt.X11BypassWindowManagerHint

        return flags

    def apply_stealth_to_widget(self, widget):
        """Apply stealth mode to any widget - CRITICAL FIX for input functionality"""
        if not self.stealth_enabled:
            return

        try:
            # Store original flags
            widget_id = id(widget)
            self.original_window_flags[widget_id] = widget.windowFlags()

            # CRITICAL FIX: Don't apply ANY window flags to dialogs with input fields
            # Only apply stealth flags to main window, not dialogs
            if hasattr(widget, 'email_input') or hasattr(widget, 'password_input'):
                print(f"🔧 SKIPPING window flags for dialog with input fields: {type(widget).__name__}")
            else:
                # Apply stealth flags only to main window
                stealth_flags = self.get_stealth_window_flags()
                widget.setWindowFlags(stealth_flags)

            # CRITICAL FIX: Don't apply ANY Qt attributes that break input
            # Only apply screen capture protection via Windows API

            # Hide from screen capture (this is the main stealth feature)
            if STEALTH_HIDE_FROM_SCREEN_SHARING:
                self.hide_from_screen_capture(widget)

            # Hide from taskbar using Windows API
            if STEALTH_HIDE_FROM_TASKBAR:
                self.hide_from_taskbar(widget)

            print(f"🥷 Applied INPUT-SAFE stealth mode to widget: {type(widget).__name__}")

        except Exception as e:
            print(f"❌ Error applying stealth mode: {e}")

    def hide_from_screen_capture(self, widget):
        """Hide widget from screen capture and screen sharing"""
        try:
            hwnd = int(widget.winId())

            # Windows API constant for excluding from capture
            WDA_EXCLUDEFROMCAPTURE = 0x00000011
            result = ctypes.windll.user32.SetWindowDisplayAffinity(hwnd, WDA_EXCLUDEFROMCAPTURE)

            if result:
                print(f"✅ Hidden from screen capture: {type(widget).__name__}")
            else:
                print(f"⚠️ Failed to hide from screen capture: {type(widget).__name__}")

        except Exception as e:
            print(f"❌ Error hiding from screen capture: {e}")

    def hide_from_taskbar(self, widget):
        """Hide widget from Windows taskbar"""
        try:
            hwnd = int(widget.winId())

            # Windows API constants
            GWL_EXSTYLE = -20
            WS_EX_TOOLWINDOW = 0x00000080
            WS_EX_NOACTIVATE = 0x08000000

            # Get current extended style
            current_style = ctypes.windll.user32.GetWindowLongW(hwnd, GWL_EXSTYLE)

            # Add tool window and no activate styles
            new_style = current_style | WS_EX_TOOLWINDOW | WS_EX_NOACTIVATE

            # Apply new style
            result = ctypes.windll.user32.SetWindowLongW(hwnd, GWL_EXSTYLE, new_style)

            if result:
                print(f"✅ Hidden from taskbar: {type(widget).__name__}")
            else:
                print(f"⚠️ Failed to hide from taskbar: {type(widget).__name__}")

        except Exception as e:
            print(f"❌ Error hiding from taskbar: {e}")

    def create_stealth_messagebox(self, title, text, icon=QMessageBox.Information, parent=None):
        """Create a stealth message box that's hidden from taskbar and screen sharing"""
        if not STEALTH_HIDE_ALL_POPUPS:
            # Use normal message box if stealth popups disabled
            msg = QMessageBox(parent)
            msg.setWindowTitle(title)
            msg.setText(text)
            msg.setIcon(icon)
            return msg

        # Create stealth message box
        msg = QMessageBox(parent)
        msg.setWindowTitle(title)
        msg.setText(text)
        msg.setIcon(icon)

        # Apply stealth mode
        self.apply_stealth_to_widget(msg)

        return msg

    def create_stealth_dialog(self, dialog_class, *args, **kwargs):
        """Create a stealth dialog that's hidden from taskbar and screen sharing"""
        dialog = dialog_class(*args, **kwargs)

        if STEALTH_HIDE_ALL_POPUPS:
            self.apply_stealth_to_widget(dialog)

        return dialog

    def enable_stealth_mode(self):
        """Enable stealth mode"""
        self.stealth_enabled = True
        print("🥷 Stealth mode ENABLED")

    def disable_stealth_mode(self):
        """Disable stealth mode"""
        self.stealth_enabled = False
        print("👁️ Stealth mode DISABLED")

    def is_stealth_enabled(self):
        """Check if stealth mode is enabled"""
        return self.stealth_enabled

    def restore_widget_flags(self, widget):
        """Restore original window flags for a widget"""
        try:
            widget_id = id(widget)
            if widget_id in self.original_window_flags:
                original_flags = self.original_window_flags[widget_id]
                widget.setWindowFlags(original_flags)
                del self.original_window_flags[widget_id]
                print(f"🔧 Restored original flags for: {type(widget).__name__}")
        except Exception as e:
            print(f"❌ Error restoring widget flags: {e}")

# Global stealth mode manager instance
stealth_manager = StealthModeManager()

def apply_stealth_mode(widget):
    """Convenience function to apply stealth mode to a widget"""
    stealth_manager.apply_stealth_to_widget(widget)

def create_stealth_messagebox(title, text, icon=QMessageBox.Information, parent=None):
    """Convenience function to create stealth message box"""
    return stealth_manager.create_stealth_messagebox(title, text, icon, parent)

def create_stealth_dialog(dialog_class, *args, **kwargs):
    """Convenience function to create stealth dialog"""
    return stealth_manager.create_stealth_dialog(dialog_class, *args, **kwargs)

def get_stealth_window_flags():
    """Convenience function to get stealth window flags"""
    return stealth_manager.get_stealth_window_flags()

def is_stealth_enabled():
    """Convenience function to check if stealth mode is enabled"""
    return stealth_manager.is_stealth_enabled()

print("🥷 Stealth Mode Manager loaded successfully")
