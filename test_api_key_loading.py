#!/usr/bin/env python3
"""
Test script to verify API key loading functionality
This script tests the fixes for dummy response issues
"""

import sys
import os

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_api_key_loading():
    """Test API key loading functionality"""
    print("🧪 Testing API Key Loading Functionality")
    print("=" * 50)
    
    try:
        # Import the main application modules
        from abidansari import FirebaseManager, AIService, API_KEYS
        
        print("✅ Successfully imported modules")
        print(f"📊 Initial API_KEYS state: {API_KEYS}")
        
        # Test Firebase Manager
        print("\n🔥 Testing Firebase Manager...")
        firebase_manager = FirebaseManager()
        
        if firebase_manager.db:
            print("✅ Firebase connection established")
        else:
            print("❌ Firebase connection failed")
            return False
        
        # Test AI Service
        print("\n🤖 Testing AI Service...")
        ai_service = AIService()
        print("✅ AI Service initialized")
        
        # Test API key loading with a test email (this will fail but we can see the flow)
        print("\n🔑 Testing API key loading flow...")
        test_email = "<EMAIL>"
        
        try:
            user_api_keys = firebase_manager.get_user_api_keys(test_email)
            print(f"📊 Retrieved API keys for {test_email}: {user_api_keys}")
        except Exception as e:
            print(f"⚠️ Expected error getting API keys for test email: {e}")
        
        # Test AI client refresh
        print("\n🔄 Testing AI client refresh...")
        ai_service.refresh_ai_clients()
        print("✅ AI client refresh completed")
        
        print("\n✅ All tests completed successfully!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Make sure all dependencies are installed")
        return False
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

def test_configuration():
    """Test configuration loading"""
    print("\n🔧 Testing Configuration...")
    
    try:
        from abidansari import PRODUCTION_CONFIG
        print("✅ Production config loaded")
        print(f"📊 API fallback order: {PRODUCTION_CONFIG['api_fallback_order']}")
        
        # Test config values
        required_keys = ['app_name', 'version', 'api_fallback_order', 'api_timeout']
        for key in required_keys:
            if key in PRODUCTION_CONFIG:
                print(f"✅ {key}: {PRODUCTION_CONFIG[key]}")
            else:
                print(f"❌ Missing config key: {key}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test error: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Starting API Key Loading Tests")
    print("=" * 60)
    
    # Test configuration first
    config_ok = test_configuration()
    
    # Test API key loading
    api_key_ok = test_api_key_loading()
    
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS:")
    print(f"   Configuration: {'✅ PASS' if config_ok else '❌ FAIL'}")
    print(f"   API Key Loading: {'✅ PASS' if api_key_ok else '❌ FAIL'}")
    
    if config_ok and api_key_ok:
        print("\n🎉 ALL TESTS PASSED!")
        print("💡 The API key loading fixes should work correctly")
    else:
        print("\n❌ SOME TESTS FAILED!")
        print("💡 Please check the error messages above")
    
    return config_ok and api_key_ok

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
