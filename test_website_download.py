#!/usr/bin/env python3
"""
Test script to verify website download functionality
This script checks if the download file exists and configurations are correct
"""

import os
import json
from pathlib import Path

def test_download_file_exists():
    """Test if the download file exists"""
    print("📁 Testing Download File Existence...")
    
    download_path = Path("website/src/assets/downloads/AbidAnsariAI-Setup.exe")
    
    if download_path.exists():
        file_size = download_path.stat().st_size
        file_size_mb = file_size / (1024 * 1024)
        print(f"✅ Download file exists: {download_path}")
        print(f"📊 File size: {file_size_mb:.1f} MB")
        return True, file_size_mb
    else:
        print(f"❌ Download file not found: {download_path}")
        return False, 0

def test_environment_configs():
    """Test environment configuration files"""
    print("\n🔧 Testing Environment Configurations...")
    
    configs = [
        "website/src/environments/environment.ts",
        "website/src/environments/environment.prod.ts"
    ]
    
    results = {}
    
    for config_path in configs:
        print(f"\n📄 Checking {config_path}...")
        
        if not os.path.exists(config_path):
            print(f"❌ Config file not found: {config_path}")
            results[config_path] = False
            continue
        
        try:
            with open(config_path, 'r') as f:
                content = f.read()
            
            # Check for download URL
            if 'AbidAnsariAI-Setup.exe' in content:
                print("✅ Correct download filename found")
                results[config_path] = True
            elif 'abid-ai-assistant.exe' in content:
                print("⚠️ Old download filename found - needs update")
                results[config_path] = False
            else:
                print("❌ No download filename found")
                results[config_path] = False
                
            # Check for download URL structure
            if '/assets/downloads/' in content:
                print("✅ Correct download path found")
            else:
                print("❌ Download path not found")
                results[config_path] = False
                
        except Exception as e:
            print(f"❌ Error reading config: {e}")
            results[config_path] = False
    
    return results

def test_angular_config():
    """Test Angular configuration for assets"""
    print("\n⚙️ Testing Angular Configuration...")
    
    angular_config_path = "website/angular.json"
    
    if not os.path.exists(angular_config_path):
        print(f"❌ Angular config not found: {angular_config_path}")
        return False
    
    try:
        with open(angular_config_path, 'r') as f:
            config = json.load(f)
        
        # Check if assets are properly configured
        build_options = config.get('projects', {}).get('website', {}).get('architect', {}).get('build', {}).get('options', {})
        assets = build_options.get('assets', [])
        
        # Look for assets configuration
        assets_configured = False
        for asset in assets:
            if isinstance(asset, dict) and asset.get('input') == 'src/assets':
                assets_configured = True
                print("✅ Assets folder properly configured")
                break
        
        if not assets_configured:
            print("❌ Assets folder not properly configured")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error reading Angular config: {e}")
        return False

def test_download_component():
    """Test download component configuration"""
    print("\n🔽 Testing Download Component...")
    
    component_path = "website/src/app/pages/download/download.component.ts"
    
    if not os.path.exists(component_path):
        print(f"❌ Download component not found: {component_path}")
        return False
    
    try:
        with open(component_path, 'r') as f:
            content = f.read()
        
        # Check for download functionality
        if 'downloadApp()' in content:
            print("✅ Download function found")
        else:
            print("❌ Download function not found")
            return False
        
        # Check for proper download implementation
        if 'link.href = this.appDownload.downloadUrl' in content:
            print("✅ Download implementation looks correct")
        else:
            print("⚠️ Download implementation might need review")
        
        return True
        
    except Exception as e:
        print(f"❌ Error reading download component: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Starting Website Download Tests")
    print("=" * 60)
    
    # Test download file existence
    file_exists, file_size = test_download_file_exists()
    
    # Test environment configurations
    env_results = test_environment_configs()
    
    # Test Angular configuration
    angular_ok = test_angular_config()
    
    # Test download component
    component_ok = test_download_component()
    
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS:")
    print(f"   Download File Exists: {'✅ PASS' if file_exists else '❌ FAIL'}")
    if file_exists:
        print(f"   File Size: {file_size:.1f} MB")
    
    env_all_ok = all(env_results.values())
    print(f"   Environment Configs: {'✅ PASS' if env_all_ok else '❌ FAIL'}")
    for config, result in env_results.items():
        print(f"     {config}: {'✅' if result else '❌'}")
    
    print(f"   Angular Config: {'✅ PASS' if angular_ok else '❌ FAIL'}")
    print(f"   Download Component: {'✅ PASS' if component_ok else '❌ FAIL'}")
    
    all_tests_pass = file_exists and env_all_ok and angular_ok and component_ok
    
    if all_tests_pass:
        print("\n🎉 ALL TESTS PASSED!")
        print("💡 Website download should work correctly")
    else:
        print("\n❌ SOME TESTS FAILED!")
        print("💡 Please check the error messages above")
    
    return all_tests_pass

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
