#!/usr/bin/env python3
"""
Comprehensive test script to verify all fixes
Tests both Python app and website configurations
"""

import os
import sys
import json
from pathlib import Path

def test_python_app_authentication():
    """Test Python app email-only authentication"""
    print("🐍 Testing Python App Authentication...")
    
    try:
        # Add current directory to path for imports
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        from abidansari import FirebaseManager, API_KEYS
        
        print("✅ Successfully imported Python modules")
        
        # Test Firebase Manager
        firebase_manager = FirebaseManager()
        
        if firebase_manager.db:
            print("✅ Firebase connection established")
        else:
            print("❌ Firebase connection failed")
            return False
        
        # Test email-only authentication method exists
        if hasattr(firebase_manager, 'authenticate_user_by_email'):
            print("✅ Email-only authentication method found")
        else:
            print("❌ Email-only authentication method missing")
            return False
        
        print("✅ Python app authentication setup correct")
        return True
        
    except Exception as e:
        print(f"❌ Python app test error: {e}")
        return False

def test_website_configurations():
    """Test website configuration files"""
    print("\n🌐 Testing Website Configurations...")
    
    # Test environment files
    env_files = [
        "website/src/environments/environment.ts",
        "website/src/environments/environment.prod.ts"
    ]
    
    env_ok = True
    for env_file in env_files:
        print(f"\n📄 Checking {env_file}...")
        
        if not os.path.exists(env_file):
            print(f"❌ Environment file not found: {env_file}")
            env_ok = False
            continue
        
        try:
            with open(env_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check for Firebase config
            if 'firebase:' in content:
                print("✅ Firebase configuration found")
            else:
                print("❌ Firebase configuration missing")
                env_ok = False
            
            # Check for download config
            if 'appDownload:' in content:
                print("✅ App download configuration found")
            else:
                print("❌ App download configuration missing")
                env_ok = False
                
        except Exception as e:
            print(f"❌ Error reading {env_file}: {e}")
            env_ok = False
    
    return env_ok

def test_angular_configuration():
    """Test Angular configuration"""
    print("\n⚙️ Testing Angular Configuration...")
    
    angular_config_path = "website/angular.json"
    
    if not os.path.exists(angular_config_path):
        print(f"❌ Angular config not found: {angular_config_path}")
        return False
    
    try:
        with open(angular_config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # Check assets configuration
        build_options = config.get('projects', {}).get('website', {}).get('architect', {}).get('build', {}).get('options', {})
        assets = build_options.get('assets', [])
        
        assets_configured = False
        for asset in assets:
            if isinstance(asset, dict) and asset.get('input') == 'src/assets':
                assets_configured = True
                print("✅ Assets folder properly configured")
                break
        
        if not assets_configured:
            print("❌ Assets folder not properly configured")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error reading Angular config: {e}")
        return False

def test_component_imports():
    """Test component imports for UI fixes"""
    print("\n🧩 Testing Component Imports...")
    
    components = [
        "website/src/app/pages/contact/contact.component.ts",
        "website/src/app/pages/pricing/pricing.component.ts"
    ]
    
    components_ok = True
    
    for component_path in components:
        print(f"\n📄 Checking {component_path}...")
        
        if not os.path.exists(component_path):
            print(f"❌ Component not found: {component_path}")
            components_ok = False
            continue
        
        try:
            with open(component_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check for RouterModule import
            if 'RouterModule' in content:
                print("✅ RouterModule import found")
            else:
                print("⚠️ RouterModule import missing (might cause routing issues)")
            
            # Check for responsive design
            if '@media (max-width:' in content:
                print("✅ Responsive design styles found")
            else:
                print("❌ Responsive design styles missing")
                components_ok = False
                
        except Exception as e:
            print(f"❌ Error reading {component_path}: {e}")
            components_ok = False
    
    return components_ok

def test_firebase_service():
    """Test Firebase service error handling"""
    print("\n🔥 Testing Firebase Service...")
    
    service_path = "website/src/app/services/firebase.service.ts"
    
    if not os.path.exists(service_path):
        print(f"❌ Firebase service not found: {service_path}")
        return False
    
    try:
        with open(service_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for improved error handling
        if 'auth/invalid-credential' in content:
            print("✅ Enhanced error handling found")
        else:
            print("❌ Enhanced error handling missing")
            return False
        
        # Check for user existence check
        if 'getUserDataByEmail' in content:
            print("✅ User existence check found")
        else:
            print("❌ User existence check missing")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error reading Firebase service: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Starting Comprehensive Fix Tests")
    print("=" * 60)
    
    # Run all tests
    python_ok = test_python_app_authentication()
    website_ok = test_website_configurations()
    angular_ok = test_angular_configuration()
    components_ok = test_component_imports()
    firebase_ok = test_firebase_service()
    
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS:")
    print(f"   Python App Authentication: {'✅ PASS' if python_ok else '❌ FAIL'}")
    print(f"   Website Configurations: {'✅ PASS' if website_ok else '❌ FAIL'}")
    print(f"   Angular Configuration: {'✅ PASS' if angular_ok else '❌ FAIL'}")
    print(f"   Component UI Fixes: {'✅ PASS' if components_ok else '❌ FAIL'}")
    print(f"   Firebase Service: {'✅ PASS' if firebase_ok else '❌ FAIL'}")
    
    all_tests_pass = python_ok and website_ok and angular_ok and components_ok and firebase_ok
    
    if all_tests_pass:
        print("\n🎉 ALL TESTS PASSED!")
        print("💡 All fixes should work correctly")
        print("\n📋 Next Steps:")
        print("   1. Rebuild the Angular website: ng build --configuration=production")
        print("   2. Test login with valid credentials")
        print("   3. Test Python app with email-only authentication")
        print("   4. Verify UI responsiveness on mobile devices")
    else:
        print("\n❌ SOME TESTS FAILED!")
        print("💡 Please check the error messages above")
    
    return all_tests_pass

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
