#!/usr/bin/env python3
"""
Test script for Abid Ansari AI Assistant configuration
"""

def test_config():
    """Test the configuration file"""
    try:
        import config
        print("✅ Configuration loaded successfully!")
        print(f"📱 WhatsApp Number: {config.WHATSAPP_NUMBER}")
        print(f"⏰ Default Time Limit: {config.DEFAULT_TIME_LIMIT} seconds ({config.format_time(config.DEFAULT_TIME_LIMIT)})")
        print(f"🔗 WhatsApp URL: {config.get_whatsapp_url()}")

        # Test email validation
        test_emails = [
            "<EMAIL>",
            "invalid-email",
            "<EMAIL>",
            "not-an-email"
        ]

        print("\n📧 Email Validation Tests:")
        for email in test_emails:
            is_valid = config.validate_email(email)
            status = "✅" if is_valid else "❌"
            print(f"   {status} {email}")

        # Test password validation
        test_passwords = [
            "123",
            "password123",
            "short",
            "verylongpassword"
        ]

        print("\n🔐 Password Validation Tests:")
        for password in test_passwords:
            is_valid, message = config.validate_password(password)
            status = "✅" if is_valid else "❌"
            print(f"   {status} '{password}' - {message}")

        # Test device ID generation
        device_id = config.get_device_id()
        print(f"\n💻 Device ID: {device_id}")

        # Test time formatting
        test_times = [0, 59, 60, 3599, 3600, 3661]
        print("\n⏰ Time Formatting Tests:")
        for seconds in test_times:
            formatted = config.format_time(seconds)
            print(f"   {seconds} seconds = {formatted}")

        print("\n✅ All configuration tests passed!")
        return True

    except ImportError as e:
        print(f"❌ Configuration import failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

def test_firebase_config():
    """Test Firebase configuration"""
    try:
        from abidansari import FIREBASE_CONFIG
        print("\n🔥 Firebase Configuration:")
        print(f"   Project ID: {FIREBASE_CONFIG.get('project_id', 'Not set')}")
        print(f"   Client Email: {FIREBASE_CONFIG.get('client_email', 'Not set')}")

        if FIREBASE_CONFIG.get('project_id') == 'your-project-id':
            print("⚠️  Warning: Firebase configuration uses placeholder values")
            print("   Please update FIREBASE_CONFIG in abidansari.py with your actual Firebase credentials")
        else:
            print("✅ Firebase configuration appears to be customized")

    except ImportError as e:
        print(f"❌ Firebase configuration test failed: {e}")

def test_imports():
    """Test all required imports"""
    print("\n📦 Testing Imports:")

    # Test PyQt5
    try:
        from PyQt5.QtWidgets import QApplication
        print("   ✅ PyQt5")
    except ImportError:
        print("   ❌ PyQt5 - Run: pip install PyQt5")

    # Test Firebase
    try:
        import firebase_admin
        print("   ✅ Firebase Admin")
    except ImportError:
        print("   ❌ Firebase Admin - Run: pip install firebase-admin")

    # Test AI libraries
    try:
        import google.generativeai as genai
        print("   ✅ Google Generative AI")
    except ImportError:
        print("   ❌ Google Generative AI - Run: pip install google-generativeai")

    try:
        from mistralai.client import MistralClient
        print("   ✅ Mistral AI")
    except ImportError:
        print("   ❌ Mistral AI - Run: pip install mistralai")

    try:
        import openai
        print("   ✅ OpenAI")
    except ImportError:
        print("   ❌ OpenAI - Run: pip install openai")

    # Test other dependencies
    try:
        from pynput import keyboard
        print("   ✅ Pynput")
    except ImportError:
        print("   ❌ Pynput - Run: pip install pynput")

    try:
        from PIL import ImageGrab
        print("   ✅ Pillow")
    except ImportError:
        print("   ❌ Pillow - Run: pip install pillow")

    try:
        import pyautogui
        print("   ✅ PyAutoGUI")
    except ImportError:
        print("   ❌ PyAutoGUI - Run: pip install pyautogui")

if __name__ == "__main__":
    print("🧪 Abid Ansari AI Assistant - Configuration Test")
    print("=" * 50)

    # Test imports first
    test_imports()

    # Test configuration
    config_ok = test_config()

    # Test Firebase config
    test_firebase_config()

    print("\n" + "=" * 50)
    if config_ok:
        print("🎉 Configuration test completed successfully!")
        print("💡 You can now run: python abidansari.py")
    else:
        print("❌ Configuration test failed!")
        print("💡 Please fix the configuration issues before running the main application")

    print("\n📝 Next Steps:")
    print("1. Update Firebase configuration in abidansari.py")
    print("2. Install any missing dependencies")
    print("3. Run the main application: python abidansari.py")
