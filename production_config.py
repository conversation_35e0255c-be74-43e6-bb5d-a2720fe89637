"""
Production Configuration for Abid Ansari AI Assistant
Centralized configuration management for production deployment
"""

import os
from typing import Dict, Any

class ProductionConfig:
    """Production configuration manager"""
    
    # Application Information
    APP_NAME = "Abid Ansari AI Assistant"
    VERSION = "2.0.0"
    DESCRIPTION = "AI Assistant with cascading API fallback and session management"
    
    # Session Management
    DEFAULT_SESSION_DURATION = 7200  # 2 hours in seconds
    FREE_TIME_LIMIT = 300  # 5 minutes in seconds
    TIMER_UPDATE_INTERVAL = 1000  # 1 second in milliseconds
    WARNING_THRESHOLD = 60  # Show warning when 1 minute left
    
    # API Configuration
    API_FALLBACK_ORDER = ['gemini', 'mistral', 'openrouter', 'openai']
    API_TIMEOUT = 30  # seconds
    MAX_RETRIES = 3
    
    # Firebase Configuration
    FIREBASE_CONFIG = {
        "type": "service_account",
        "project_id": "my-blogs-*************",
        "private_key_id": os.getenv('FIREBASE_PRIVATE_KEY_ID', 'placeholder'),
        "private_key": os.getenv('FIREBASE_PRIVATE_KEY', 'placeholder'),
        "client_email": os.getenv('FIREBASE_CLIENT_EMAIL', '<EMAIL>'),
        "client_id": os.getenv('FIREBASE_CLIENT_ID', 'placeholder'),
        "auth_uri": "https://accounts.google.com/o/oauth2/auth",
        "token_uri": "https://oauth2.googleapis.com/token",
        "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
        "client_x509_cert_url": "placeholder"
    }
    
    # UI Configuration
    WINDOW_CONFIG = {
        'width': 800,
        'height': 600,
        'min_width': 600,
        'min_height': 400,
        'title': f"{APP_NAME} v{VERSION}"
    }
    
    # Styling
    STYLES = {
        'timer_normal': "color: green; background-color: #f0f0f0; padding: 5px; border-radius: 5px; font-weight: bold;",
        'timer_warning': "color: orange; background-color: #f0f0f0; padding: 5px; border-radius: 5px; font-weight: bold;",
        'timer_critical': "color: red; background-color: #f0f0f0; padding: 5px; border-radius: 5px; font-weight: bold;",
        'timer_inactive': "color: red; background-color: #f0f0f0; padding: 5px; border-radius: 5px;",
        'status_normal': "color: gray; padding: 5px;",
        'status_error': "color: red; padding: 5px;",
        'status_success': "color: green; padding: 5px;"
    }
    
    # Contact Information
    CONTACT_INFO = {
        'email': '<EMAIL>',
        'whatsapp': '+91 8104184175',
        'support_hours': 'Mon-Fri: 9 AM - 6 PM IST'
    }
    
    # Distribution Settings
    DISTRIBUTION = {
        'target_size_mb': 50,
        'compression_level': 9,
        'include_debug': False,
        'optimize_level': 2
    }
    
    # Logging Configuration
    LOGGING = {
        'level': 'INFO',
        'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        'file_enabled': False,
        'console_enabled': True
    }
    
    # Security Settings
    SECURITY = {
        'encrypt_api_keys': True,
        'secure_storage': True,
        'session_encryption': True,
        'validate_certificates': True
    }
    
    # Performance Settings
    PERFORMANCE = {
        'lazy_loading': True,
        'cache_responses': False,  # Don't cache AI responses for privacy
        'preload_modules': ['PyQt5.QtCore', 'PyQt5.QtWidgets', 'PyQt5.QtGui'],
        'background_loading': True
    }
    
    # Error Messages
    ERROR_MESSAGES = {
        'auth_failed': "Authentication failed. Please check your email and try again.",
        'session_expired': "Your session has expired. Please contact support to extend access.",
        'api_unavailable': "All AI services are currently unavailable. Please check your API keys.",
        'network_error': "Network error. Please check your internet connection.",
        'firebase_error': "Unable to connect to authentication service.",
        'invalid_email': "Please enter a valid email address.",
        'user_not_found': "User not found. Please contact support for registration.",
        'account_inactive': "Your account is inactive. Please contact support."
    }
    
    # Success Messages
    SUCCESS_MESSAGES = {
        'login_success': "Login successful! Welcome back.",
        'logout_success': "Logged out successfully.",
        'session_started': "Session started successfully.",
        'api_connected': "AI services connected successfully."
    }
    
    # API Endpoints
    API_ENDPOINTS = {
        'gemini': 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent',
        'mistral': 'https://api.mistral.ai/v1/chat/completions',
        'openrouter': 'https://openrouter.ai/api/v1/chat/completions',
        'openai': 'https://api.openai.com/v1/chat/completions'
    }
    
    # Model Configuration
    MODEL_CONFIG = {
        'gemini': {
            'model': 'gemini-pro',
            'temperature': 0.7,
            'max_tokens': 1000
        },
        'mistral': {
            'model': 'mistral-medium',
            'temperature': 0.7,
            'max_tokens': 1000
        },
        'openrouter': {
            'model': 'openai/gpt-3.5-turbo',
            'temperature': 0.7,
            'max_tokens': 1000
        },
        'openai': {
            'model': 'gpt-3.5-turbo',
            'temperature': 0.7,
            'max_tokens': 1000
        }
    }
    
    @classmethod
    def get_config(cls) -> Dict[str, Any]:
        """Get complete configuration as dictionary"""
        return {
            'app': {
                'name': cls.APP_NAME,
                'version': cls.VERSION,
                'description': cls.DESCRIPTION
            },
            'session': {
                'default_duration': cls.DEFAULT_SESSION_DURATION,
                'free_time_limit': cls.FREE_TIME_LIMIT,
                'timer_interval': cls.TIMER_UPDATE_INTERVAL,
                'warning_threshold': cls.WARNING_THRESHOLD
            },
            'api': {
                'fallback_order': cls.API_FALLBACK_ORDER,
                'timeout': cls.API_TIMEOUT,
                'max_retries': cls.MAX_RETRIES,
                'endpoints': cls.API_ENDPOINTS,
                'models': cls.MODEL_CONFIG
            },
            'firebase': cls.FIREBASE_CONFIG,
            'ui': {
                'window': cls.WINDOW_CONFIG,
                'styles': cls.STYLES
            },
            'contact': cls.CONTACT_INFO,
            'distribution': cls.DISTRIBUTION,
            'logging': cls.LOGGING,
            'security': cls.SECURITY,
            'performance': cls.PERFORMANCE,
            'messages': {
                'errors': cls.ERROR_MESSAGES,
                'success': cls.SUCCESS_MESSAGES
            }
        }
    
    @classmethod
    def validate_config(cls) -> bool:
        """Validate configuration settings"""
        errors = []
        
        # Validate session settings
        if cls.DEFAULT_SESSION_DURATION <= 0:
            errors.append("Default session duration must be positive")
        
        if cls.FREE_TIME_LIMIT <= 0:
            errors.append("Free time limit must be positive")
        
        # Validate API settings
        if not cls.API_FALLBACK_ORDER:
            errors.append("API fallback order cannot be empty")
        
        if cls.API_TIMEOUT <= 0:
            errors.append("API timeout must be positive")
        
        # Validate contact info
        if not cls.CONTACT_INFO['email']:
            errors.append("Contact email is required")
        
        if not cls.CONTACT_INFO['whatsapp']:
            errors.append("WhatsApp contact is required")
        
        if errors:
            print("Configuration validation errors:")
            for error in errors:
                print(f"  - {error}")
            return False
        
        return True
    
    @classmethod
    def load_from_env(cls):
        """Load configuration from environment variables"""
        # Update Firebase config from environment
        if os.getenv('FIREBASE_PROJECT_ID'):
            cls.FIREBASE_CONFIG['project_id'] = os.getenv('FIREBASE_PROJECT_ID')
        
        if os.getenv('FIREBASE_PRIVATE_KEY'):
            cls.FIREBASE_CONFIG['private_key'] = os.getenv('FIREBASE_PRIVATE_KEY')
        
        if os.getenv('FIREBASE_CLIENT_EMAIL'):
            cls.FIREBASE_CONFIG['client_email'] = os.getenv('FIREBASE_CLIENT_EMAIL')
        
        # Update contact info from environment
        if os.getenv('SUPPORT_EMAIL'):
            cls.CONTACT_INFO['email'] = os.getenv('SUPPORT_EMAIL')
        
        if os.getenv('WHATSAPP_NUMBER'):
            cls.CONTACT_INFO['whatsapp'] = os.getenv('WHATSAPP_NUMBER')
        
        # Update logging level from environment
        if os.getenv('LOG_LEVEL'):
            cls.LOGGING['level'] = os.getenv('LOG_LEVEL')

# Validate configuration on import
if not ProductionConfig.validate_config():
    print("⚠️ Configuration validation failed")

# Load environment variables
ProductionConfig.load_from_env()
