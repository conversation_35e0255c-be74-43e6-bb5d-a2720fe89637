import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatExpansionModule } from '@angular/material/expansion';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'app-documentation',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatIconModule,
    MatExpansionModule,
    RouterModule,
  ],
  template: `
    <div class="documentation-container">
      <div class="hero-section">
        <h1>Documentation</h1>
        <p>Complete guide to using Abid Ansari AI Assistant for technical interviews</p>
      </div>

      <div class="content-section">
        <!-- Quick Start Guide -->
        <mat-card class="section-card">
          <mat-card-header>
            <mat-icon mat-card-avatar>rocket_launch</mat-icon>
            <mat-card-title>Quick Start Guide</mat-card-title>
            <mat-card-subtitle>Get up and running in minutes</mat-card-subtitle>
          </mat-card-header>
          <mat-card-content>
            <div class="steps-list">
              <div class="step">
                <div class="step-number">1</div>
                <div class="step-content">
                  <h3>Create Account</h3>
                  <p>Register on our website and set up your profile with secure API key management.</p>
                  <a mat-button color="primary" routerLink="/register">Register Now</a>
                </div>
              </div>
              <div class="step">
                <div class="step-number">2</div>
                <div class="step-content">
                  <h3>Configure API Keys</h3>
                  <p>Add your AI API keys (Gemini, OpenAI, Mistral, OpenRouter) in the secure dashboard.</p>
                  <a mat-button color="primary" routerLink="/api-keys">Manage API Keys</a>
                </div>
              </div>
              <div class="step">
                <div class="step-number">3</div>
                <div class="step-content">
                  <h3>Download Application</h3>
                  <p>Download and install the desktop application on your system.</p>
                  <a mat-button color="primary" routerLink="/download">Download App</a>
                </div>
              </div>
              <div class="step">
                <div class="step-number">4</div>
                <div class="step-content">
                  <h3>Start Using</h3>
                  <p>Launch the app, login with your credentials, and start using AI assistance during interviews.</p>
                </div>
              </div>
            </div>
          </mat-card-content>
        </mat-card>

        <!-- Hotkey Controls -->
        <mat-card class="section-card">
          <mat-card-header>
            <mat-icon mat-card-avatar>keyboard</mat-icon>
            <mat-card-title>Hotkey Controls</mat-card-title>
            <mat-card-subtitle>Master the keyboard shortcuts for efficient usage</mat-card-subtitle>
          </mat-card-header>
          <mat-card-content>
            <div class="hotkeys-grid">
              <div class="hotkey-item">
                <div class="hotkey-key">Caps Lock</div>
                <div class="hotkey-description">
                  <h4>Ask Questions</h4>
                  <p>Press and hold Caps Lock, type your question, then release to get AI assistance.</p>
                </div>
              </div>
              <div class="hotkey-item">
                <div class="hotkey-key">Alt</div>
                <div class="hotkey-description">
                  <h4>Screenshot Analysis</h4>
                  <p>Press Alt to capture and analyze screenshots with AI vision capabilities.</p>
                </div>
              </div>
              <div class="hotkey-item">
                <div class="hotkey-key">Ctrl + Space</div>
                <div class="hotkey-description">
                  <h4>Toggle Window</h4>
                  <p>Show/hide the AI assistant window for stealth mode during interviews.</p>
                </div>
              </div>
              <div class="hotkey-item">
                <div class="hotkey-key">Esc</div>
                <div class="hotkey-description">
                  <h4>Clear/Cancel</h4>
                  <p>Clear current response or cancel ongoing AI request.</p>
                </div>
              </div>
            </div>
          </mat-card-content>
        </mat-card>

        <!-- Features Guide -->
        <mat-card class="section-card">
          <mat-card-header>
            <mat-icon mat-card-avatar>features</mat-icon>
            <mat-card-title>Features Guide</mat-card-title>
            <mat-card-subtitle>Detailed explanation of all features</mat-card-subtitle>
          </mat-card-header>
          <mat-card-content>
            <mat-accordion>
              <mat-expansion-panel>
                <mat-expansion-panel-header>
                  <mat-panel-title>AI Models Integration</mat-panel-title>
                  <mat-panel-description>Multiple AI providers for best results</mat-panel-description>
                </mat-expansion-panel-header>
                <div class="feature-content">
                  <p>The application supports multiple AI models with automatic fallback:</p>
                  <ul>
                    <li><strong>Gemini 2.0 Flash:</strong> Google's latest model with vision capabilities</li>
                    <li><strong>Mistral Large:</strong> Advanced reasoning and coding assistance</li>
                    <li><strong>OpenAI GPT:</strong> Reliable general-purpose AI assistance</li>
                    <li><strong>OpenRouter:</strong> Access to Claude and other premium models</li>
                  </ul>
                  <p>The system automatically tries models in order until one responds successfully.</p>
                </div>
              </mat-expansion-panel>

              <mat-expansion-panel>
                <mat-expansion-panel-header>
                  <mat-panel-title>Screenshot Analysis</mat-panel-title>
                  <mat-panel-description>AI-powered visual analysis</mat-panel-description>
                </mat-expansion-panel-header>
                <div class="feature-content">
                  <p>Advanced screenshot analysis capabilities:</p>
                  <ul>
                    <li>Code analysis and debugging assistance</li>
                    <li>Technical diagram interpretation</li>
                    <li>Error message analysis and solutions</li>
                    <li>UI/UX feedback and suggestions</li>
                    <li>Text extraction and OCR capabilities</li>
                  </ul>
                  <p>Simply press Alt to capture any part of your screen for AI analysis.</p>
                </div>
              </mat-expansion-panel>

              <mat-expansion-panel>
                <mat-expansion-panel-header>
                  <mat-panel-title>Stealth Mode</mat-panel-title>
                  <mat-panel-description>Discreet usage during interviews</mat-panel-description>
                </mat-expansion-panel-header>
                <div class="feature-content">
                  <p>Professional stealth features for interview scenarios:</p>
                  <ul>
                    <li>Transparent window overlay</li>
                    <li>Quick hide/show with Ctrl+Space</li>
                    <li>Minimal visual footprint</li>
                    <li>Silent operation mode</li>
                    <li>Customizable transparency levels</li>
                  </ul>
                  <p>Configure transparency settings in the application preferences.</p>
                </div>
              </mat-expansion-panel>

              <mat-expansion-panel>
                <mat-expansion-panel-header>
                  <mat-panel-title>Security & Privacy</mat-panel-title>
                  <mat-panel-description>Your data protection is our priority</mat-panel-description>
                </mat-expansion-panel-header>
                <div class="feature-content">
                  <p>Comprehensive security measures:</p>
                  <ul>
                    <li>Encrypted API key storage</li>
                    <li>Local data processing when possible</li>
                    <li>No conversation logging</li>
                    <li>Secure Firebase authentication</li>
                    <li>Device-based access control</li>
                  </ul>
                  <p>Your API keys and conversations are never stored on our servers.</p>
                </div>
              </mat-expansion-panel>
            </mat-accordion>
          </mat-card-content>
        </mat-card>

        <!-- API Keys Setup -->
        <mat-card class="section-card">
          <mat-card-header>
            <mat-icon mat-card-avatar>vpn_key</mat-icon>
            <mat-card-title>API Keys Setup</mat-card-title>
            <mat-card-subtitle>How to obtain and configure your AI API keys</mat-card-subtitle>
          </mat-card-header>
          <mat-card-content>
            <div class="api-providers">
              <div class="provider-card">
                <h4>Google Gemini</h4>
                <p>Get your free API key from Google AI Studio:</p>
                <ol>
                  <li>Visit <a href="https://aistudio.google.com" target="_blank">Google AI Studio</a></li>
                  <li>Sign in with your Google account</li>
                  <li>Click "Get API Key" and create a new key</li>
                  <li>Copy the key and add it to your profile</li>
                </ol>
              </div>
              <div class="provider-card">
                <h4>OpenAI</h4>
                <p>Get your API key from OpenAI platform:</p>
                <ol>
                  <li>Visit <a href="https://platform.openai.com" target="_blank">OpenAI Platform</a></li>
                  <li>Create an account and add billing information</li>
                  <li>Go to API Keys section</li>
                  <li>Create a new secret key and copy it</li>
                </ol>
              </div>
              <div class="provider-card">
                <h4>Mistral AI</h4>
                <p>Get your API key from Mistral platform:</p>
                <ol>
                  <li>Visit <a href="https://console.mistral.ai" target="_blank">Mistral Console</a></li>
                  <li>Create an account and verify email</li>
                  <li>Navigate to API Keys</li>
                  <li>Generate a new key and copy it</li>
                </ol>
              </div>
              <div class="provider-card">
                <h4>OpenRouter</h4>
                <p>Access multiple models through OpenRouter:</p>
                <ol>
                  <li>Visit <a href="https://openrouter.ai" target="_blank">OpenRouter</a></li>
                  <li>Sign up and add credits</li>
                  <li>Go to Keys section</li>
                  <li>Create a new API key</li>
                </ol>
              </div>
            </div>
          </mat-card-content>
        </mat-card>

        <!-- Troubleshooting -->
        <mat-card class="section-card">
          <mat-card-header>
            <mat-icon mat-card-avatar>help</mat-icon>
            <mat-card-title>Troubleshooting</mat-card-title>
            <mat-card-subtitle>Common issues and solutions</mat-card-subtitle>
          </mat-card-header>
          <mat-card-content>
            <mat-accordion>
              <mat-expansion-panel>
                <mat-expansion-panel-header>
                  <mat-panel-title>Application Won't Start</mat-panel-title>
                </mat-expansion-panel-header>
                <div class="troubleshoot-content">
                  <p><strong>Possible causes and solutions:</strong></p>
                  <ul>
                    <li>Check if you're logged in with valid credentials</li>
                    <li>Ensure your internet connection is stable</li>
                    <li>Verify that your account has remaining time/credits</li>
                    <li>Try restarting the application</li>
                    <li>Check Windows Defender or antivirus settings</li>
                  </ul>
                </div>
              </mat-expansion-panel>

              <mat-expansion-panel>
                <mat-expansion-panel-header>
                  <mat-panel-title>AI Not Responding</mat-panel-title>
                </mat-expansion-panel-header>
                <div class="troubleshoot-content">
                  <p><strong>Check the following:</strong></p>
                  <ul>
                    <li>Verify your API keys are correctly configured</li>
                    <li>Check if you have sufficient API credits</li>
                    <li>Ensure your internet connection is working</li>
                    <li>Try a different AI model from the settings</li>
                    <li>Check for any error messages in the application</li>
                  </ul>
                </div>
              </mat-expansion-panel>

              <mat-expansion-panel>
                <mat-expansion-panel-header>
                  <mat-panel-title>Hotkeys Not Working</mat-panel-title>
                </mat-expansion-panel-header>
                <div class="troubleshoot-content">
                  <p><strong>Try these solutions:</strong></p>
                  <ul>
                    <li>Run the application as administrator</li>
                    <li>Check if other applications are blocking hotkeys</li>
                    <li>Restart the application</li>
                    <li>Verify the application has focus</li>
                    <li>Check Windows accessibility settings</li>
                  </ul>
                </div>
              </mat-expansion-panel>
            </mat-accordion>
          </mat-card-content>
        </mat-card>

        <!-- Support -->
        <mat-card class="section-card">
          <mat-card-header>
            <mat-icon mat-card-avatar>support</mat-icon>
            <mat-card-title>Need Help?</mat-card-title>
            <mat-card-subtitle>Get support when you need it</mat-card-subtitle>
          </mat-card-header>
          <mat-card-content>
            <div class="support-options">
              <div class="support-option">
                <mat-icon>email</mat-icon>
                <h4>Email Support</h4>
                <p>Send us an email for detailed technical support</p>
                <a mat-button color="primary" href="mailto:<EMAIL>">Send Email</a>
              </div>
              <div class="support-option">
                <mat-icon>chat</mat-icon>
                <h4>WhatsApp Support</h4>
                <p>Quick support via WhatsApp for urgent issues</p>
                <a mat-button color="primary" href="https://wa.me/8104184175" target="_blank">Chat Now</a>
              </div>
              <div class="support-option">
                <mat-icon>contact_support</mat-icon>
                <h4>Contact Form</h4>
                <p>Use our contact form for general inquiries</p>
                <a mat-button color="primary" routerLink="/contact">Contact Us</a>
              </div>
            </div>
          </mat-card-content>
        </mat-card>
      </div>
    </div>
  `,
  styleUrl: './documentation.component.scss',
})
export class DocumentationComponent {
}
