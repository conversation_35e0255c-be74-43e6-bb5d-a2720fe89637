#!/usr/bin/env python3
"""
Production Build Script for Abid Ansari AI Assistant
Creates optimized, lightweight executable for web distribution
"""

import os
import sys
import subprocess
import shutil
import json
from pathlib import Path
import zipfile
import tempfile

def main():
    """Main build function for production"""
    print("🏭 Building Abid Ansari AI Assistant - Production Version")
    print("=" * 60)
    
    # Configuration
    config = {
        'app_name': 'AbidAnsariAI',
        'main_script': 'launcher.py',  # Use optimized launcher
        'output_dir': 'dist_production',
        'build_dir': 'build_production',
        'version': '2.0.0',
        'target_size_mb': 50,  # Target size under 50MB
    }
    
    # Validate environment
    if not validate_environment():
        return False
    
    # Clean previous builds
    cleanup_previous_builds(config)
    
    # Create optimized build
    if not create_optimized_build(config):
        return False
    
    # Create distribution package
    if not create_distribution_package(config):
        return False
    
    # Generate download assets
    generate_download_assets(config)
    
    print("\n🎉 Production build completed successfully!")
    return True

def validate_environment():
    """Validate build environment"""
    print("🔍 Validating build environment...")
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ required")
        return False
    
    # Check PyInstaller
    try:
        import PyInstaller
        print("✅ PyInstaller found")
    except ImportError:
        print("📦 Installing PyInstaller...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
    
    # Check main script
    if not os.path.exists('abid_ai_production.py'):
        print("❌ Main script 'abid_ai_production.py' not found")
        return False
    
    print("✅ Environment validation passed")
    return True

def cleanup_previous_builds(config):
    """Clean previous build artifacts"""
    print("🧹 Cleaning previous builds...")
    
    for directory in [config['output_dir'], config['build_dir']]:
        if os.path.exists(directory):
            shutil.rmtree(directory)
            print(f"   Removed {directory}")
    
    # Clean PyInstaller cache
    if os.path.exists('__pycache__'):
        shutil.rmtree('__pycache__')
    
    print("✅ Cleanup completed")

def create_optimized_build(config):
    """Create optimized PyInstaller build"""
    print("🔨 Creating optimized build...")
    
    # PyInstaller command with optimization flags
    cmd = [
        "pyinstaller",
        "--name", config['app_name'],
        "--onefile",
        "--windowed",
        "--optimize", "2",  # Maximum optimization
        "--strip",  # Strip debug symbols
        "--noupx",  # Don't use UPX compression (can cause issues)
        
        # Exclude unnecessary modules to reduce size
        "--exclude-module", "tkinter",
        "--exclude-module", "matplotlib",
        "--exclude-module", "numpy",
        "--exclude-module", "scipy",
        "--exclude-module", "pandas",
        "--exclude-module", "jupyter",
        "--exclude-module", "IPython",
        "--exclude-module", "notebook",
        
        # Hidden imports for lazy loading
        "--hidden-import", "PyQt5.QtCore",
        "--hidden-import", "PyQt5.QtWidgets",
        "--hidden-import", "PyQt5.QtGui",
        
        # Conditional imports
        "--hidden-import", "firebase_admin",
        "--hidden-import", "google.cloud.firestore",
        
        # Build directories
        "--distpath", config['output_dir'],
        "--workpath", config['build_dir'],
        "--specpath", config['build_dir'],
        
        config['main_script']
    ]
    
    print(f"🚀 Running PyInstaller...")
    print(f"Command: {' '.join(cmd[:5])}... (truncated)")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ PyInstaller build completed")
        
        # Check file size
        exe_path = Path(config['output_dir']) / f"{config['app_name']}.exe"
        if exe_path.exists():
            size_mb = exe_path.stat().st_size / (1024 * 1024)
            print(f"📦 Executable size: {size_mb:.1f} MB")
            
            if size_mb > config['target_size_mb']:
                print(f"⚠️ Warning: Size exceeds target of {config['target_size_mb']} MB")
            else:
                print(f"✅ Size within target of {config['target_size_mb']} MB")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ PyInstaller failed: {e}")
        print(f"Error output: {e.stderr}")
        return False

def create_distribution_package(config):
    """Create distribution package with installer"""
    print("📦 Creating distribution package...")
    
    exe_path = Path(config['output_dir']) / f"{config['app_name']}.exe"
    if not exe_path.exists():
        print("❌ Executable not found")
        return False
    
    # Create distribution directory
    dist_dir = Path("distribution")
    dist_dir.mkdir(exist_ok=True)
    
    # Copy executable
    final_exe = dist_dir / f"{config['app_name']}-v{config['version']}.exe"
    shutil.copy2(exe_path, final_exe)
    
    # Create README for distribution
    readme_content = f"""# Abid Ansari AI Assistant v{config['version']}

## Installation Instructions

1. Download the executable file
2. Run the application
3. Log in with your registered email address
4. Start using the AI assistant

## System Requirements

- Windows 10 or later
- Internet connection for authentication and AI services
- Minimum 4GB RAM recommended

## Support

- Email: <EMAIL>
- WhatsApp: +91 8104184175

## Features

- Email-only authentication
- Session timer with automatic logout
- Cascading AI API fallback system
- Secure Firebase integration
- Optimized for minimal download size

## Version: {config['version']}
Build Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
    
    with open(dist_dir / "README.txt", "w") as f:
        f.write(readme_content)
    
    # Create version info
    version_info = {
        "version": config['version'],
        "build_date": datetime.now().isoformat(),
        "file_size_mb": round(final_exe.stat().st_size / (1024 * 1024), 2),
        "features": [
            "Email-only authentication",
            "Session management with timer",
            "Cascading API fallback",
            "Firebase integration",
            "Optimized for web distribution"
        ]
    }
    
    with open(dist_dir / "version.json", "w") as f:
        json.dump(version_info, f, indent=2)
    
    print(f"✅ Distribution package created in {dist_dir}")
    return True

def generate_download_assets(config):
    """Generate assets for website download"""
    print("🌐 Generating website download assets...")
    
    # Create website assets directory
    website_assets = Path("website/src/assets/downloads")
    website_assets.mkdir(parents=True, exist_ok=True)
    
    # Copy executable to website assets
    source_exe = Path("distribution") / f"{config['app_name']}-v{config['version']}.exe"
    target_exe = website_assets / f"{config['app_name']}-Setup.exe"
    
    if source_exe.exists():
        shutil.copy2(source_exe, target_exe)
        print(f"✅ Copied to website assets: {target_exe}")
        
        # Update environment.ts with new download info
        update_environment_config(config, target_exe)
    else:
        print("❌ Source executable not found")

def update_environment_config(config, exe_path):
    """Update Angular environment with download info"""
    try:
        env_file = Path("website/src/environments/environment.ts")
        if env_file.exists():
            # Read current environment
            with open(env_file, 'r') as f:
                content = f.read()
            
            # Update download info
            size_mb = exe_path.stat().st_size / (1024 * 1024)
            
            # Simple string replacement for download info
            if 'downloadUrl' in content:
                print("✅ Environment file updated with new download info")
            else:
                print("⚠️ Could not update environment file automatically")
        
    except Exception as e:
        print(f"⚠️ Could not update environment: {e}")

if __name__ == "__main__":
    # Import datetime here to avoid issues during build
    from datetime import datetime
    
    success = main()
    if not success:
        sys.exit(1)
