#!/usr/bin/env python3
"""
Test script to verify pricing page UI fix
Checks if the overlap issue is resolved
"""

import os
import re

def test_pricing_ui_fix():
    """Test if pricing page UI overlap issue is fixed"""
    print("🧪 Testing Pricing Page UI Fix...")
    
    pricing_file = "website/src/app/pages/pricing/pricing.component.ts"
    
    if not os.path.exists(pricing_file):
        print(f"❌ Pricing component not found: {pricing_file}")
        return False
    
    try:
        with open(pricing_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check if negative margin is removed from pricing-section
        pricing_section_pattern = r'\.pricing-section\s*{[^}]*margin:\s*([^;]+);'
        matches = re.findall(pricing_section_pattern, content, re.DOTALL)
        
        overlap_fixed = True
        for match in matches:
            if '-40px' in match or '-20px' in match or '-10px' in match:
                print(f"❌ Found negative margin that could cause overlap: {match}")
                overlap_fixed = False
        
        if overlap_fixed:
            print("✅ No negative margins found - overlap issue should be fixed")
        
        # Check if responsive design is maintained
        mobile_responsive = '@media (max-width: 768px)' in content
        if mobile_responsive:
            print("✅ Mobile responsive design maintained")
        else:
            print("❌ Mobile responsive design missing")
            return False
        
        # Check if grid layout is preserved
        grid_layout = 'grid-template-columns: repeat(auto-fit, minmax(' in content
        if grid_layout:
            print("✅ Grid layout preserved")
        else:
            print("❌ Grid layout missing")
            return False
        
        return overlap_fixed and mobile_responsive and grid_layout
        
    except Exception as e:
        print(f"❌ Error reading pricing component: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Testing Pricing Page UI Fix")
    print("=" * 50)
    
    success = test_pricing_ui_fix()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 PRICING UI FIX TEST PASSED!")
        print("💡 The blue section overlap issue should be resolved")
        print("\n📋 What was fixed:")
        print("   ✅ Removed negative margin from .pricing-section")
        print("   ✅ Cards will no longer overlap with blue hero section")
        print("   ✅ Maintained original UI design and responsiveness")
        print("   ✅ Grid layout preserved for proper card arrangement")
        print("\n🔧 Changes made:")
        print("   • Changed margin: -40px auto 0 → margin: 40px auto 0")
        print("   • Updated mobile responsive margins accordingly")
        print("   • Kept all other styling exactly the same")
    else:
        print("❌ PRICING UI FIX TEST FAILED!")
        print("💡 Please check the error messages above")
    
    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
