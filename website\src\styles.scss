/* Global Styles - Modern Design System */

// Import Google Fonts and Material Icons
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap');
@import url('https://fonts.googleapis.com/icon?family=Material+Icons');

// Design Tokens
:root {
  // Typography
  --font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-family-mono: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace;

  // Font Sizes (Type Scale)
  --font-size-xs: 0.75rem;    // 12px
  --font-size-sm: 0.875rem;   // 14px
  --font-size-base: 1rem;     // 16px
  --font-size-lg: 1.125rem;   // 18px
  --font-size-xl: 1.25rem;    // 20px
  --font-size-2xl: 1.5rem;    // 24px
  --font-size-3xl: 1.875rem;  // 30px
  --font-size-4xl: 2.25rem;   // 36px
  --font-size-5xl: 3rem;      // 48px
  --font-size-6xl: 3.75rem;   // 60px

  // Font Weights
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;

  // Line Heights
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;

  // Letter Spacing
  --letter-spacing-tighter: -0.05em;
  --letter-spacing-tight: -0.025em;
  --letter-spacing-normal: 0;
  --letter-spacing-wide: 0.025em;
  --letter-spacing-wider: 0.05em;
  --letter-spacing-widest: 0.1em;

  // Colors - Modern Palette
  --color-primary-50: #eff6ff;
  --color-primary-100: #dbeafe;
  --color-primary-200: #bfdbfe;
  --color-primary-300: #93c5fd;
  --color-primary-400: #60a5fa;
  --color-primary-500: #3b82f6;
  --color-primary-600: #2563eb;
  --color-primary-700: #1d4ed8;
  --color-primary-800: #1e40af;
  --color-primary-900: #1e3a8a;

  --color-secondary-50: #f8fafc;
  --color-secondary-100: #f1f5f9;
  --color-secondary-200: #e2e8f0;
  --color-secondary-300: #cbd5e1;
  --color-secondary-400: #94a3b8;
  --color-secondary-500: #64748b;
  --color-secondary-600: #475569;
  --color-secondary-700: #334155;
  --color-secondary-800: #1e293b;
  --color-secondary-900: #0f172a;

  --color-accent-50: #fdf4ff;
  --color-accent-100: #fae8ff;
  --color-accent-200: #f5d0fe;
  --color-accent-300: #f0abfc;
  --color-accent-400: #e879f9;
  --color-accent-500: #d946ef;
  --color-accent-600: #c026d3;
  --color-accent-700: #a21caf;
  --color-accent-800: #86198f;
  --color-accent-900: #701a75;

  --color-success-50: #f0fdf4;
  --color-success-100: #dcfce7;
  --color-success-200: #bbf7d0;
  --color-success-300: #86efac;
  --color-success-400: #4ade80;
  --color-success-500: #22c55e;
  --color-success-600: #16a34a;
  --color-success-700: #15803d;
  --color-success-800: #166534;
  --color-success-900: #14532d;

  --color-warning-50: #fffbeb;
  --color-warning-100: #fef3c7;
  --color-warning-200: #fde68a;
  --color-warning-300: #fcd34d;
  --color-warning-400: #fbbf24;
  --color-warning-500: #f59e0b;
  --color-warning-600: #d97706;
  --color-warning-700: #b45309;
  --color-warning-800: #92400e;
  --color-warning-900: #78350f;

  --color-error-50: #fef2f2;
  --color-error-100: #fee2e2;
  --color-error-200: #fecaca;
  --color-error-300: #fca5a5;
  --color-error-400: #f87171;
  --color-error-500: #ef4444;
  --color-error-600: #dc2626;
  --color-error-700: #b91c1c;
  --color-error-800: #991b1b;
  --color-error-900: #7f1d1d;

  // Neutral Colors
  --color-white: #ffffff;
  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;
  --color-black: #000000;

  // Spacing Scale
  --spacing-0: 0;
  --spacing-1: 0.25rem;   // 4px
  --spacing-2: 0.5rem;    // 8px
  --spacing-3: 0.75rem;   // 12px
  --spacing-4: 1rem;      // 16px
  --spacing-5: 1.25rem;   // 20px
  --spacing-6: 1.5rem;    // 24px
  --spacing-8: 2rem;      // 32px
  --spacing-10: 2.5rem;   // 40px
  --spacing-12: 3rem;     // 48px
  --spacing-16: 4rem;     // 64px
  --spacing-20: 5rem;     // 80px
  --spacing-24: 6rem;     // 96px
  --spacing-32: 8rem;     // 128px
  --spacing-40: 10rem;    // 160px
  --spacing-48: 12rem;    // 192px
  --spacing-56: 14rem;    // 224px
  --spacing-64: 16rem;    // 256px

  // Border Radius
  --radius-none: 0;
  --radius-sm: 0.125rem;   // 2px
  --radius-base: 0.25rem;  // 4px
  --radius-md: 0.375rem;   // 6px
  --radius-lg: 0.5rem;     // 8px
  --radius-xl: 0.75rem;    // 12px
  --radius-2xl: 1rem;      // 16px
  --radius-3xl: 1.5rem;    // 24px
  --radius-full: 9999px;

  // Shadows
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-base: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgb(0 0 0 / 0.05);

  // Z-Index Scale
  --z-index-dropdown: 1000;
  --z-index-sticky: 1020;
  --z-index-fixed: 1030;
  --z-index-modal-backdrop: 1040;
  --z-index-modal: 1050;
  --z-index-popover: 1060;
  --z-index-tooltip: 1070;

  // Transitions
  --transition-fast: 150ms ease-in-out;
  --transition-base: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
}

// Base Reset and Typography
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

body {
  font-family: var(--font-family-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  color: var(--color-gray-900);
  background-color: var(--color-white);
  letter-spacing: var(--letter-spacing-normal);
  overflow-x: hidden;
}

// Typography Hierarchy
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-family-primary);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  letter-spacing: var(--letter-spacing-tight);
  color: var(--color-gray-900);
  margin-bottom: var(--spacing-4);
}

h1 {
  font-size: var(--font-size-5xl);
  font-weight: var(--font-weight-extrabold);
  letter-spacing: var(--letter-spacing-tighter);

  @media (max-width: 768px) {
    font-size: var(--font-size-4xl);
  }
}

h2 {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);

  @media (max-width: 768px) {
    font-size: var(--font-size-3xl);
  }
}

h3 {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-semibold);

  @media (max-width: 768px) {
    font-size: var(--font-size-2xl);
  }
}

h4 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);

  @media (max-width: 768px) {
    font-size: var(--font-size-xl);
  }
}

h5 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-medium);

  @media (max-width: 768px) {
    font-size: var(--font-size-lg);
  }
}

h6 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);

  @media (max-width: 768px) {
    font-size: var(--font-size-base);
  }
}

p {
  font-size: var(--font-size-base);
  line-height: var(--line-height-relaxed);
  color: var(--color-gray-700);
  margin-bottom: var(--spacing-4);

  &.lead {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-normal);
    color: var(--color-gray-600);
  }

  &.small {
    font-size: var(--font-size-sm);
    color: var(--color-gray-600);
  }
}

// Links
a {
  color: var(--color-primary-600);
  text-decoration: none;
  transition: var(--transition-fast);

  &:hover {
    color: var(--color-primary-700);
    text-decoration: underline;
  }

  &:focus {
    outline: 2px solid var(--color-primary-500);
    outline-offset: 2px;
    border-radius: var(--radius-sm);
  }
}

// Lists
ul, ol {
  margin-bottom: var(--spacing-4);
  padding-left: var(--spacing-6);

  li {
    margin-bottom: var(--spacing-2);
    line-height: var(--line-height-relaxed);
    color: var(--color-gray-700);
  }
}

// Code
code {
  font-family: var(--font-family-mono);
  font-size: 0.875em;
  background-color: var(--color-gray-100);
  color: var(--color-gray-800);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-base);
  border: 1px solid var(--color-gray-200);
}

pre {
  font-family: var(--font-family-mono);
  background-color: var(--color-gray-900);
  color: var(--color-gray-100);
  padding: var(--spacing-4);
  border-radius: var(--radius-lg);
  overflow-x: auto;
  margin-bottom: var(--spacing-4);

  code {
    background: none;
    border: none;
    padding: 0;
    color: inherit;
  }
}

// Utility Classes
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);

  @media (min-width: 640px) {
    padding: 0 var(--spacing-6);
  }

  @media (min-width: 1024px) {
    padding: 0 var(--spacing-8);
  }
}

.container-sm {
  width: 100%;
  max-width: 640px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
}

.container-lg {
  width: 100%;
  max-width: 1440px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
}

// Spacing Utilities
.mt-0 { margin-top: var(--spacing-0) !important; }
.mt-1 { margin-top: var(--spacing-1) !important; }
.mt-2 { margin-top: var(--spacing-2) !important; }
.mt-3 { margin-top: var(--spacing-3) !important; }
.mt-4 { margin-top: var(--spacing-4) !important; }
.mt-5 { margin-top: var(--spacing-5) !important; }
.mt-6 { margin-top: var(--spacing-6) !important; }
.mt-8 { margin-top: var(--spacing-8) !important; }
.mt-10 { margin-top: var(--spacing-10) !important; }
.mt-12 { margin-top: var(--spacing-12) !important; }
.mt-16 { margin-top: var(--spacing-16) !important; }
.mt-20 { margin-top: var(--spacing-20) !important; }

.mb-0 { margin-bottom: var(--spacing-0) !important; }
.mb-1 { margin-bottom: var(--spacing-1) !important; }
.mb-2 { margin-bottom: var(--spacing-2) !important; }
.mb-3 { margin-bottom: var(--spacing-3) !important; }
.mb-4 { margin-bottom: var(--spacing-4) !important; }
.mb-5 { margin-bottom: var(--spacing-5) !important; }
.mb-6 { margin-bottom: var(--spacing-6) !important; }
.mb-8 { margin-bottom: var(--spacing-8) !important; }
.mb-10 { margin-bottom: var(--spacing-10) !important; }
.mb-12 { margin-bottom: var(--spacing-12) !important; }
.mb-16 { margin-bottom: var(--spacing-16) !important; }
.mb-20 { margin-bottom: var(--spacing-20) !important; }

.pt-0 { padding-top: var(--spacing-0) !important; }
.pt-1 { padding-top: var(--spacing-1) !important; }
.pt-2 { padding-top: var(--spacing-2) !important; }
.pt-3 { padding-top: var(--spacing-3) !important; }
.pt-4 { padding-top: var(--spacing-4) !important; }
.pt-5 { padding-top: var(--spacing-5) !important; }
.pt-6 { padding-top: var(--spacing-6) !important; }
.pt-8 { padding-top: var(--spacing-8) !important; }
.pt-10 { padding-top: var(--spacing-10) !important; }
.pt-12 { padding-top: var(--spacing-12) !important; }
.pt-16 { padding-top: var(--spacing-16) !important; }
.pt-20 { padding-top: var(--spacing-20) !important; }

.pb-0 { padding-bottom: var(--spacing-0) !important; }
.pb-1 { padding-bottom: var(--spacing-1) !important; }
.pb-2 { padding-bottom: var(--spacing-2) !important; }
.pb-3 { padding-bottom: var(--spacing-3) !important; }
.pb-4 { padding-bottom: var(--spacing-4) !important; }
.pb-5 { padding-bottom: var(--spacing-5) !important; }
.pb-6 { padding-bottom: var(--spacing-6) !important; }
.pb-8 { padding-bottom: var(--spacing-8) !important; }
.pb-10 { padding-bottom: var(--spacing-10) !important; }
.pb-12 { padding-bottom: var(--spacing-12) !important; }
.pb-16 { padding-bottom: var(--spacing-16) !important; }
.pb-20 { padding-bottom: var(--spacing-20) !important; }

// Text Utilities
.text-center { text-align: center !important; }
.text-left { text-align: left !important; }
.text-right { text-align: right !important; }

.text-xs { font-size: var(--font-size-xs) !important; }
.text-sm { font-size: var(--font-size-sm) !important; }
.text-base { font-size: var(--font-size-base) !important; }
.text-lg { font-size: var(--font-size-lg) !important; }
.text-xl { font-size: var(--font-size-xl) !important; }
.text-2xl { font-size: var(--font-size-2xl) !important; }
.text-3xl { font-size: var(--font-size-3xl) !important; }
.text-4xl { font-size: var(--font-size-4xl) !important; }
.text-5xl { font-size: var(--font-size-5xl) !important; }

.font-light { font-weight: var(--font-weight-light) !important; }
.font-normal { font-weight: var(--font-weight-normal) !important; }
.font-medium { font-weight: var(--font-weight-medium) !important; }
.font-semibold { font-weight: var(--font-weight-semibold) !important; }
.font-bold { font-weight: var(--font-weight-bold) !important; }
.font-extrabold { font-weight: var(--font-weight-extrabold) !important; }

// Color Utilities
.text-primary { color: var(--color-primary-600) !important; }
.text-secondary { color: var(--color-secondary-600) !important; }
.text-success { color: var(--color-success-600) !important; }
.text-warning { color: var(--color-warning-600) !important; }
.text-error { color: var(--color-error-600) !important; }
.text-gray-500 { color: var(--color-gray-500) !important; }
.text-gray-600 { color: var(--color-gray-600) !important; }
.text-gray-700 { color: var(--color-gray-700) !important; }
.text-gray-800 { color: var(--color-gray-800) !important; }
.text-gray-900 { color: var(--color-gray-900) !important; }

// Background Utilities
.bg-primary { background-color: var(--color-primary-600) !important; }
.bg-secondary { background-color: var(--color-secondary-100) !important; }
.bg-gray-50 { background-color: var(--color-gray-50) !important; }
.bg-gray-100 { background-color: var(--color-gray-100) !important; }
.bg-white { background-color: var(--color-white) !important; }

// Border Utilities
.rounded-none { border-radius: var(--radius-none) !important; }
.rounded-sm { border-radius: var(--radius-sm) !important; }
.rounded { border-radius: var(--radius-base) !important; }
.rounded-md { border-radius: var(--radius-md) !important; }
.rounded-lg { border-radius: var(--radius-lg) !important; }
.rounded-xl { border-radius: var(--radius-xl) !important; }
.rounded-2xl { border-radius: var(--radius-2xl) !important; }
.rounded-full { border-radius: var(--radius-full) !important; }

// Shadow Utilities
.shadow-sm { box-shadow: var(--shadow-sm) !important; }
.shadow { box-shadow: var(--shadow-base) !important; }
.shadow-md { box-shadow: var(--shadow-md) !important; }
.shadow-lg { box-shadow: var(--shadow-lg) !important; }
.shadow-xl { box-shadow: var(--shadow-xl) !important; }
.shadow-2xl { box-shadow: var(--shadow-2xl) !important; }

// Flexbox Utilities
.flex { display: flex !important; }
.flex-col { flex-direction: column !important; }
.flex-row { flex-direction: row !important; }
.items-center { align-items: center !important; }
.items-start { align-items: flex-start !important; }
.items-end { align-items: flex-end !important; }
.justify-center { justify-content: center !important; }
.justify-between { justify-content: space-between !important; }
.justify-around { justify-content: space-around !important; }
.justify-start { justify-content: flex-start !important; }
.justify-end { justify-content: flex-end !important; }

// Grid Utilities
.grid { display: grid !important; }
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)) !important; }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)) !important; }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)) !important; }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)) !important; }

.gap-1 { gap: var(--spacing-1) !important; }
.gap-2 { gap: var(--spacing-2) !important; }
.gap-3 { gap: var(--spacing-3) !important; }
.gap-4 { gap: var(--spacing-4) !important; }
.gap-5 { gap: var(--spacing-5) !important; }
.gap-6 { gap: var(--spacing-6) !important; }
.gap-8 { gap: var(--spacing-8) !important; }

// Material Icons Styling
.material-icons {
  font-family: 'Material Icons' !important;
  font-weight: normal !important;
  font-style: normal !important;
  font-size: 24px !important;
  line-height: 1 !important;
  letter-spacing: normal !important;
  text-transform: none !important;
  display: inline-block !important;
  white-space: nowrap !important;
  word-wrap: normal !important;
  direction: ltr !important;
  -webkit-font-feature-settings: 'liga' !important;
  -webkit-font-smoothing: antialiased !important;
  text-rendering: optimizeLegibility !important;
  -moz-osx-font-smoothing: grayscale !important;
  font-feature-settings: 'liga' !important;
}

// Material Icon Component Styling
.mat-icon {
  font-family: 'Material Icons' !important;
  font-weight: normal !important;
  font-style: normal !important;
  line-height: 1 !important;
  letter-spacing: normal !important;
  text-transform: none !important;
  display: inline-block !important;
  white-space: nowrap !important;
  word-wrap: normal !important;
  direction: ltr !important;
  -webkit-font-feature-settings: 'liga' !important;
  -webkit-font-smoothing: antialiased !important;
  text-rendering: optimizeLegibility !important;
  -moz-osx-font-smoothing: grayscale !important;
  font-feature-settings: 'liga' !important;
  vertical-align: middle !important;

  // Default icon size
  font-size: 24px !important;
  width: 24px !important;
  height: 24px !important;

  // Icon size variants
  &.mat-icon-sm {
    font-size: 18px !important;
    width: 18px !important;
    height: 18px !important;
  }

  &.mat-icon-lg {
    font-size: 32px !important;
    width: 32px !important;
    height: 32px !important;
  }

  &.mat-icon-xl {
    font-size: 48px !important;
    width: 48px !important;
    height: 48px !important;
  }
}

// Ensure Material Icons work in buttons
.mat-mdc-button .mat-icon,
.mat-mdc-raised-button .mat-icon,
.mat-mdc-outlined-button .mat-icon,
.mat-mdc-fab .mat-icon,
.mat-mdc-mini-fab .mat-icon {
  font-family: 'Material Icons' !important;
  vertical-align: middle !important;
  margin-right: 8px !important;

  &:last-child {
    margin-right: 0 !important;
  }
}

// Material Design Overrides
.mat-mdc-button {
  font-family: var(--font-family-primary) !important;
  font-weight: var(--font-weight-medium) !important;
  letter-spacing: var(--letter-spacing-wide) !important;
  border-radius: var(--radius-lg) !important;
  transition: var(--transition-base) !important;
}

.mat-mdc-raised-button {
  font-family: var(--font-family-primary) !important;
  font-weight: var(--font-weight-semibold) !important;
  letter-spacing: var(--letter-spacing-wide) !important;
  border-radius: var(--radius-lg) !important;
  box-shadow: var(--shadow-md) !important;
  transition: var(--transition-base) !important;

  &:hover {
    box-shadow: var(--shadow-lg) !important;
  }
}

.mat-mdc-card {
  border-radius: var(--radius-xl) !important;
  box-shadow: var(--shadow-md) !important;
  border: 1px solid var(--color-gray-200) !important;
  transition: var(--transition-base) !important;
}

.mat-mdc-card-header {
  .mat-mdc-card-title {
    font-family: var(--font-family-primary) !important;
    font-weight: var(--font-weight-semibold) !important;
    letter-spacing: var(--letter-spacing-tight) !important;
  }

  .mat-mdc-card-subtitle {
    font-family: var(--font-family-primary) !important;
    color: var(--color-gray-600) !important;
  }
}

.mat-expansion-panel {
  border-radius: var(--radius-lg) !important;
  box-shadow: var(--shadow-sm) !important;
  border: 1px solid var(--color-gray-200) !important;
  margin-bottom: var(--spacing-4) !important;

  &:not(.mat-expanded) {
    .mat-expansion-panel-header:hover {
      background: var(--color-gray-50) !important;
    }
  }
}

.mat-expansion-panel-header {
  font-family: var(--font-family-primary) !important;
  font-weight: var(--font-weight-medium) !important;
  padding: var(--spacing-4) var(--spacing-6) !important;
}

.mat-expansion-panel-content {
  .mat-expansion-panel-body {
    padding: var(--spacing-6) !important;
  }
}

.mat-mdc-menu-panel {
  border-radius: var(--radius-lg) !important;
  box-shadow: var(--shadow-xl) !important;
  border: 1px solid var(--color-gray-200) !important;
  overflow: hidden !important;
}

.mat-mdc-menu-item {
  font-family: var(--font-family-primary) !important;
  font-size: var(--font-size-sm) !important;
  padding: var(--spacing-3) var(--spacing-4) !important;

  &:hover {
    background-color: var(--color-primary-50) !important;
  }
}

.mat-mdc-form-field {
  font-family: var(--font-family-primary) !important;

  .mat-mdc-text-field-wrapper {
    border-radius: var(--radius-lg) !important;
  }
}

// Scrollbar Styling
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-gray-100);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb {
  background: var(--color-gray-400);
  border-radius: var(--radius-full);
  transition: var(--transition-fast);

  &:hover {
    background: var(--color-gray-500);
  }
}

// Loading States
.loading {
  opacity: 0.7;
  pointer-events: none;
  transition: var(--transition-base);
}

// Animation Classes
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.slide-up {
  animation: slideUp 0.5s ease-out;
}

.scale-in {
  animation: scaleIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

// Print Styles
@media print {
  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }

  a, a:visited {
    text-decoration: underline;
  }

  .no-print {
    display: none !important;
  }
}
