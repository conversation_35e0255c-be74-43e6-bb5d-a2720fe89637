# Abid Ansari AI Assistant - Production Ready v2.0

A production-ready Python desktop application with cascading API fallback, email-only authentication, session management with countdown timer, and optimized distribution for web hosting.

## 🚀 Production Features (v2.0)

### ✨ New Production Capabilities
- **🔄 Cascading API Fallback**: Gemini → Mistral → OpenRouter → OpenAI automatic failover
- **📧 Email-Only Authentication**: Secure Firebase login without passwords
- **⏰ Session Timer**: Prominent countdown display with automatic logout
- **📦 Optimized Distribution**: Under 50MB download with lazy loading
- **🎯 Professional UI**: Clean interface with real-time session tracking

### 🔧 Technical Improvements
- **Lazy Loading**: Heavy dependencies loaded only when needed
- **Size Optimization**: Minimal initial footprint for web distribution
- **Error Handling**: Graceful API failures with automatic switching
- **Session Management**: Real-time timer with visual warnings
- **Contact Integration**: Direct WhatsApp/Email support links

## 🌟 Overview

This project consists of two main components:

1. **Angular Website** - User management, API key configuration, documentation, and download portal
2. **Desktop Application** - AI-powered interview assistant with stealth mode capabilities

## ✨ Key Features

### Website Features
- 🔐 **Secure User Authentication** - Firebase-based login/registration system
- 🔑 **API Key Management** - Secure storage and management of AI service API keys
- 💰 **Transparent Pricing** - ₹200 per interview session with no hidden costs
- 📚 **Comprehensive Documentation** - Complete user guides and troubleshooting
- 📱 **Responsive Design** - Works perfectly on desktop and mobile devices
- 🛡️ **Security First** - Encrypted data storage and secure authentication

### Desktop Application Features
- 🤖 **Multi-AI Support** - Gemini 2.0, OpenAI GPT, Mistral AI, OpenRouter integration
- 📸 **Screenshot Analysis** - AI-powered visual analysis with computer vision
- 🥷 **Professional Stealth Mode** - Transparent overlay for discreet interview usage
- ⌨️ **Hotkey Controls** - Caps Lock for questions, Alt for screenshots, Ctrl+Space to toggle
- 🔒 **Secure Integration** - No hardcoded API keys, fetches from user profile
- ⏱️ **Session Management** - Automatic time tracking and billing integration
- 🎯 **Interview Optimized** - Specialized prompts for technical interview scenarios

## 🏗️ Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Production System                        │
├─────────────────────────────────────────────────────────────┤
│  Angular Website (Firebase Hosting)                        │
│  ├── User Authentication & Management                      │
│  ├── API Key Configuration Interface                       │
│  ├── Documentation & Support                               │
│  ├── Download Portal                                       │
│  └── Pricing & Billing Information                         │
├─────────────────────────────────────────────────────────────┤
│  Firebase Backend                                          │
│  ├── Firestore Database (User data, API keys, sessions)   │
│  ├── Authentication Service                                │
│  ├── Cloud Storage (Application downloads)                 │
│  └── Security Rules & Access Control                       │
├─────────────────────────────────────────────────────────────┤
│  Desktop Application (Python/PyQt5)                       │
│  ├── Firebase Authentication Integration                   │
│  ├── Dynamic API Key Loading                               │
│  ├── Multi-AI Service Integration                          │
│  ├── Screenshot Capture & Analysis                         │
│  ├── Stealth Mode Implementation                           │
│  └── Session Tracking & Time Management                    │
└─────────────────────────────────────────────────────────────┘
```

## 📁 Project Structure
```
├── website/                 # Angular website
│   ├── src/app/pages/      # Page components
│   ├── src/app/services/   # Firebase services
│   └── src/environments/   # Configuration
├── abidansari.py           # Main desktop application
├── build_app.py           # Build script for executable
├── requirements.txt       # Python dependencies
├── DEPLOYMENT_GUIDE.md    # Production deployment guide
└── README.md              # This documentation
```

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ and npm
- Python 3.8+
- Firebase account
- Angular CLI (`npm install -g @angular/cli`)

### 1. Website Setup

```bash
# Navigate to website directory
cd website

# Install dependencies
npm install

# Start development server
ng serve

# Build for production
ng build --configuration production
```

### 2. Desktop Application Setup

```bash
# Install Python dependencies
pip install -r requirements.txt

# Run the application
python abidansari.py

# Build executable
python build_app.py
```

### 3. Firebase Configuration

1. Create Firebase project at https://console.firebase.google.com
2. Enable Authentication, Firestore, and Hosting
3. Download service account key to `firebase_service_account.json`
4. Update environment configurations

## 📖 User Guide

### Getting Started
1. **Register** on the website with your email
2. **Configure API Keys** in your dashboard
3. **Download** the desktop application
4. **Login** with your website credentials
5. **Start** your interview session

### Hotkey Controls
- **Caps Lock** - Hold to ask questions, release to submit
- **Alt** - Capture screenshot for AI analysis
- **Ctrl+Space** - Toggle application visibility (stealth mode)
- **Esc** - Clear current response or cancel request

### Best Practices
- Test your setup before important interviews
- Keep API keys secure and don't share them
- Use stealth mode responsibly and ethically
- Have backup plans for technical issues

## 💰 Pricing Model

**Simple & Transparent:**
- ₹200 per interview session (up to 2 hours)
- No monthly subscriptions or hidden fees
- Users provide their own AI API keys
- One free trial session for new users
- 30-day money-back guarantee

**Why This Model:**
- Cost-effective for occasional users
- No markup on AI API costs
- Complete transparency
- Pay only when you use it

## 🔧 Configuration

### API Keys Required

Users need to configure their own API keys for:
- **Google Gemini** - Free tier available at https://aistudio.google.com
- **OpenAI** - Pay-per-use at https://platform.openai.com
- **Mistral AI** - Credits-based at https://console.mistral.ai
- **OpenRouter** - Access to multiple models at https://openrouter.ai

### Environment Variables

**Website (`src/environments/environment.ts`):**
```typescript
export const environment = {
  production: false,
  firebase: {
    // Firebase configuration
  },
  pricing: {
    currency: '₹',
    sessionPrice: 200,
    sessionDuration: 120
  },
  contact: {
    email: '<EMAIL>',
    whatsapp: '**********'
  }
};
```

**Application (`abidansari.py`):**
- Firebase service account configuration
- Dynamic API key loading from user profiles
- Secure credential management
- Session tracking integration

## 🛡️ Security Features

### Data Protection
- 🔐 **Encrypted API Keys** - All user API keys encrypted in Firestore
- 🚫 **No Conversation Logging** - Privacy-first approach
- 🔒 **Secure Authentication** - Firebase Auth with proper session management
- 🛡️ **Access Control** - Role-based permissions and device restrictions

### Application Security
- 🥷 **Stealth Mode** - Professional transparency for interview scenarios
- 🔑 **Dynamic Credentials** - No hardcoded API keys in application
- 🔐 **Secure Communication** - HTTPS/TLS for all data transmission
- 🛡️ **Code Protection** - Obfuscated executable with PyInstaller

## 🚀 Deployment

See [DEPLOYMENT_GUIDE.md](DEPLOYMENT_GUIDE.md) for complete production deployment instructions.

**Quick Deploy:**
```bash
# Website
cd website
ng build --configuration production
firebase deploy --only hosting

# Application
python build_app.py
# Upload executable to hosting/storage
```

## 📞 Support

**Get Help:**
- 📧 **Email**: <EMAIL>
- 💬 **WhatsApp**: +91 **********
- 🌐 **Website**: Contact form available
- 📚 **Documentation**: Comprehensive guides included

**Support Hours:**
- Monday - Friday: 9 AM - 6 PM IST
- Response time: 2-4 hours during business hours

## 🔧 Development

### Contributing
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

### Testing
- **Website**: `ng test` for unit tests
- **Application**: Manual testing with various AI services
- **Integration**: End-to-end testing with Firebase backend

## 📄 License

This project is proprietary software. All rights reserved.

**Usage Terms:**
- Licensed for personal and professional interview assistance
- No redistribution or modification without permission
- Commercial use requires separate licensing agreement

## 🙏 Acknowledgments

- **Firebase** - Backend infrastructure and authentication
- **Google Gemini** - Advanced AI capabilities
- **OpenAI** - Reliable AI assistance
- **Mistral AI** - Specialized coding assistance
- **Angular Team** - Excellent frontend framework
- **PyQt** - Cross-platform GUI framework

---

**Built with ❤️ by Abid Ansari**

*Empowering developers to ace their technical interviews with AI assistance*
