#!/usr/bin/env python3
"""
Test script for production features of Abid Ansari AI Assistant
Verifies all production-ready functionality
"""

import sys
import os

def test_production_config():
    """Test production configuration"""
    print("🧪 Testing Production Configuration...")
    
    try:
        # Import the main application
        import abidansari
        
        # Check production config exists
        if hasattr(abidansari, 'PRODUCTION_CONFIG'):
            config = abidansari.PRODUCTION_CONFIG
            print("✅ Production configuration found")
            
            # Check required fields
            required_fields = ['app_name', 'version', 'api_fallback_order', 'contact_email', 'contact_whatsapp']
            for field in required_fields:
                if field in config:
                    print(f"  ✅ {field}: {config[field]}")
                else:
                    print(f"  ❌ Missing: {field}")
            
            # Check API fallback order
            expected_order = ['gemini', 'mistral', 'openrouter', 'openai']
            if config.get('api_fallback_order') == expected_order:
                print("  ✅ API fallback order correct: Gemini → Mistral → OpenRouter → OpenAI")
            else:
                print(f"  ❌ API fallback order incorrect: {config.get('api_fallback_order')}")
                
        else:
            print("❌ Production configuration not found")
            return False
            
    except ImportError as e:
        print(f"❌ Failed to import main application: {e}")
        return False
    except Exception as e:
        print(f"❌ Error testing configuration: {e}")
        return False
    
    return True

def test_email_authentication():
    """Test email-only authentication"""
    print("\n🧪 Testing Email-Only Authentication...")
    
    try:
        import abidansari
        
        # Check if FirebaseManager has email-only auth method
        firebase_manager = abidansari.FirebaseManager()
        
        if hasattr(firebase_manager, 'authenticate_user_by_email'):
            print("✅ Email-only authentication method found")
            
            # Test email validation (without actual Firebase call)
            test_emails = [
                ("<EMAIL>", True),
                ("invalid-email", False),
                ("<EMAIL>", True),
                ("@domain.com", False),
                ("user@", False)
            ]
            
            for email, should_be_valid in test_emails:
                # Simple email validation test
                is_valid = "@" in email and "." in email and len(email.split("@")) == 2
                if is_valid == should_be_valid:
                    print(f"  ✅ Email validation: {email} -> {is_valid}")
                else:
                    print(f"  ❌ Email validation failed: {email} -> {is_valid} (expected {should_be_valid})")
        else:
            print("❌ Email-only authentication method not found")
            return False
            
    except Exception as e:
        print(f"❌ Error testing authentication: {e}")
        return False
    
    return True

def test_session_management():
    """Test session management and timer"""
    print("\n🧪 Testing Session Management...")
    
    try:
        import abidansari
        
        # Check if MainPopup has session management methods
        required_methods = [
            'start_time_tracking',
            'update_time_display', 
            'show_time_expired_popup',
            'get_remaining_time_display'
        ]
        
        # Create a mock MainPopup to test methods exist
        main_popup_class = abidansari.MainPopup
        
        for method in required_methods:
            if hasattr(main_popup_class, method):
                print(f"  ✅ Session method found: {method}")
            else:
                print(f"  ❌ Session method missing: {method}")
                return False
        
        # Check production config timer settings
        config = abidansari.PRODUCTION_CONFIG
        if 'timer_update_interval' in config and config['timer_update_interval'] == 1000:
            print("  ✅ Timer update interval: 1000ms (1 second)")
        else:
            print("  ❌ Timer update interval not configured correctly")
            
        if 'free_time_limit' in config and config['free_time_limit'] == 300:
            print("  ✅ Free time limit: 300 seconds (5 minutes)")
        else:
            print("  ❌ Free time limit not configured correctly")
            
    except Exception as e:
        print(f"❌ Error testing session management: {e}")
        return False
    
    return True

def test_api_fallback():
    """Test API cascading fallback system"""
    print("\n🧪 Testing API Cascading Fallback...")
    
    try:
        import abidansari
        
        # Check if AIService exists and has proper fallback
        if hasattr(abidansari, 'AIService'):
            ai_service_class = abidansari.AIService
            print("✅ AIService class found")
            
            # Check if it has the response method
            if hasattr(ai_service_class, '_get_ai_response_thread'):
                print("✅ AI response thread method found")
            else:
                print("❌ AI response thread method not found")
                return False
                
            # Check if it has model-specific response method
            if hasattr(ai_service_class, '_get_response_from_model'):
                print("✅ Model-specific response method found")
            else:
                print("❌ Model-specific response method not found")
                return False
                
        else:
            print("❌ AIService class not found")
            return False
            
        # Check API keys structure
        if hasattr(abidansari, 'API_KEYS'):
            api_keys = abidansari.API_KEYS
            expected_apis = ['gemini', 'mistral', 'openrouter', 'openai']
            
            for api in expected_apis:
                if api in api_keys:
                    print(f"  ✅ API key slot found: {api}")
                else:
                    print(f"  ❌ API key slot missing: {api}")
                    return False
        else:
            print("❌ API_KEYS structure not found")
            return False
            
    except Exception as e:
        print(f"❌ Error testing API fallback: {e}")
        return False
    
    return True

def test_build_optimization():
    """Test build system optimization"""
    print("\n🧪 Testing Build System...")
    
    try:
        # Check if build script exists
        if os.path.exists('build_app.py'):
            print("✅ Build script found")
            
            # Read build script and check for optimization flags
            with open('build_app.py', 'r', encoding='utf-8') as f:
                content = f.read()
                
            optimization_features = [
                '--optimize',
                '--strip',
                '--exclude-module',
                'target_size'
            ]
            
            for feature in optimization_features:
                if feature in content:
                    print(f"  ✅ Build optimization found: {feature}")
                else:
                    print(f"  ❌ Build optimization missing: {feature}")
                    
        else:
            print("❌ Build script not found")
            return False
            
        # Check requirements file
        if os.path.exists('requirements.txt'):
            print("✅ Requirements file found")
            
            with open('requirements.txt', 'r', encoding='utf-8') as f:
                content = f.read()
                
            required_deps = ['PyQt5', 'firebase-admin', 'requests', 'httpx']
            for dep in required_deps:
                if dep in content:
                    print(f"  ✅ Required dependency: {dep}")
                else:
                    print(f"  ❌ Missing dependency: {dep}")
        else:
            print("❌ Requirements file not found")
            return False
            
    except Exception as e:
        print(f"❌ Error testing build system: {e}")
        return False
    
    return True

def main():
    """Run all production tests"""
    print("🚀 Abid Ansari AI Assistant - Production Feature Tests")
    print("=" * 60)
    
    tests = [
        ("Production Configuration", test_production_config),
        ("Email-Only Authentication", test_email_authentication),
        ("Session Management", test_session_management),
        ("API Cascading Fallback", test_api_fallback),
        ("Build System Optimization", test_build_optimization)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"💥 {test_name}: ERROR - {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All production features are working correctly!")
        return 0
    else:
        print("⚠️ Some production features need attention.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
