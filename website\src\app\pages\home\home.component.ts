import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { environment } from '../../../environments/environment';

@Component({
  selector: 'app-home',
  standalone: true,
  imports: [CommonModule, RouterModule, MatButtonModule, MatIconModule, MatCardModule],
  template: `
    <div class="home-container">
      <!-- Hero Section -->
      <section class="hero-section">
        <div class="hero-content">
          <h1 class="hero-title">
            AI Assistant
          </h1>
          <p class="hero-subtitle">
            Professional AI-powered interview assistant with advanced features for technical interviews, coding challenges, and career development.
          </p>
          <div class="hero-actions">
            <a mat-raised-button color="primary" routerLink="/download" class="cta-button">
              <mat-icon>download</mat-icon>
              Download Now
            </a>
            <a mat-button routerLink="/documentation" class="secondary-button">
              <mat-icon>description</mat-icon>
              View Documentation
            </a>
          </div>
          <div class="pricing-highlight">
            <span class="pricing-text">
              Only {{ pricing.currency }}{{ pricing.sessionPrice }} per interview session
            </span>
          </div>
        </div>
        <div class="hero-image">
          <div class="app-preview">
            <mat-icon class="preview-icon">smart_toy</mat-icon>
            <p>AI Assistant Interface</p>
          </div>
        </div>
      </section>

      <!-- Features Section -->
      <section class="features-section">
        <div class="section-header">
          <h2>Powerful Features</h2>
          <p>Everything you need for successful technical interviews</p>
        </div>
        <div class="features-grid">
          <mat-card class="feature-card">
            <mat-card-header>
              <mat-icon mat-card-avatar>psychology</mat-icon>
              <mat-card-title>AI-Powered Assistance</mat-card-title>
            </mat-card-header>
            <mat-card-content>
              Multiple AI models including Gemini, Mistral, OpenAI, and OpenRouter for comprehensive technical support.
            </mat-card-content>
          </mat-card>

          <mat-card class="feature-card">
            <mat-card-header>
              <mat-icon mat-card-avatar>screenshot</mat-icon>
              <mat-card-title>Screen Analysis</mat-card-title>
            </mat-card-header>
            <mat-card-content>
              Advanced screenshot analysis with vision AI capabilities for coding problems and technical diagrams.
            </mat-card-content>
          </mat-card>

          <mat-card class="feature-card">
            <mat-card-header>
              <mat-icon mat-card-avatar>security</mat-icon>
              <mat-card-title>Secure & Private</mat-card-title>
            </mat-card-header>
            <mat-card-content>
              Your API keys are securely encrypted and stored. Complete privacy with stealth mode functionality.
            </mat-card-content>
          </mat-card>

          <mat-card class="feature-card">
            <mat-card-header>
              <mat-icon mat-card-avatar>keyboard</mat-icon>
              <mat-card-title>Hotkey Controls</mat-card-title>
            </mat-card-header>
            <mat-card-content>
              Quick access with Caps Lock for questions, Alt for screenshots, and Ctrl+Space for window toggle.
            </mat-card-content>
          </mat-card>

          <mat-card class="feature-card">
            <mat-card-header>
              <mat-icon mat-card-avatar>code</mat-icon>
              <mat-card-title>Technical Expertise</mat-card-title>
            </mat-card-header>
            <mat-card-content>
              Specialized for full-stack development, system design, algorithms, and technical interview scenarios.
            </mat-card-content>
          </mat-card>

          <mat-card class="feature-card">
            <mat-card-header>
              <mat-icon mat-card-avatar>vpn_key</mat-icon>
              <mat-card-title>API Key Management</mat-card-title>
            </mat-card-header>
            <mat-card-content>
              Secure management of your own AI API keys with encrypted storage and easy configuration.
            </mat-card-content>
          </mat-card>
        </div>
      </section>

      <!-- How It Works Section -->
      <section class="how-it-works-section">
        <div class="section-header">
          <h2>How It Works</h2>
          <p>Get started in just a few simple steps</p>
        </div>
        <div class="steps-container">
          <div class="step">
            <div class="step-number">1</div>
            <h3>Register & Setup</h3>
            <p>Create your account and configure your AI API keys securely</p>
          </div>
          <div class="step">
            <div class="step-number">2</div>
            <h3>Download & Install</h3>
            <p>Download the desktop application and install it on your system</p>
          </div>
          <div class="step">
            <div class="step-number">3</div>
            <h3>Start Interviewing</h3>
            <p>Use hotkeys during interviews for instant AI assistance and support</p>
          </div>
        </div>
      </section>

      <!-- CTA Section -->
      <section class="cta-section">
        <div class="cta-content">
          <h2>Ready to Ace Your Next Interview?</h2>
          <p>Join thousands of developers who trust Abid Ansari AI Assistant for their technical interviews.</p>
          <div class="cta-actions">
            <a mat-raised-button color="primary" routerLink="/register" class="cta-button">
              Get Started Now
            </a>
            <a mat-button routerLink="/pricing" class="secondary-button">
              View Pricing
            </a>
          </div>
        </div>
      </section>
    </div>
  `,
  styles: [`
    .home-container {
      min-height: 100vh;
      background: linear-gradient(135deg, var(--color-gray-50) 0%, var(--color-white) 100%);
    }

    .hero-section {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: var(--spacing-16);
      align-items: center;
      padding: var(--spacing-20) var(--spacing-6);
      max-width: 1200px;
      margin: 0 auto;
      min-height: 80vh;
    }

    .hero-content {
      max-width: 600px;
    }

    .hero-title {
      font-size: var(--font-size-6xl);
      font-weight: var(--font-weight-extrabold);
      line-height: var(--line-height-tight);
      margin-bottom: var(--spacing-6);
      background: linear-gradient(135deg, var(--color-primary-600), var(--color-primary-700), var(--color-accent-600));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      letter-spacing: var(--letter-spacing-tighter);
    }

    .hero-subtitle {
      font-size: var(--font-size-xl);
      color: var(--color-gray-600);
      line-height: var(--line-height-relaxed);
      margin-bottom: var(--spacing-8);
      font-weight: var(--font-weight-normal);
    }

    .hero-actions {
      display: flex;
      gap: var(--spacing-4);
      margin-bottom: var(--spacing-8);
      flex-wrap: wrap;
      align-items: center;
    }

    .cta-button {
      padding: var(--spacing-4) var(--spacing-8) !important;
      font-size: var(--font-size-lg) !important;
      font-weight: var(--font-weight-semibold) !important;
      border-radius: var(--radius-xl) !important;
      background: linear-gradient(135deg, var(--color-primary-600), var(--color-primary-700)) !important;
      box-shadow: var(--shadow-lg) !important;
      transition: var(--transition-base) !important;
      letter-spacing: var(--letter-spacing-wide) !important;

      &:hover {
        transform: translateY(-2px) !important;
        box-shadow: var(--shadow-xl) !important;
        background: linear-gradient(135deg, var(--color-primary-700), var(--color-primary-800)) !important;
      }
    }

    .secondary-button {
      padding: var(--spacing-4) var(--spacing-6) !important;
      font-size: var(--font-size-lg) !important;
      font-weight: var(--font-weight-medium) !important;
      border-radius: var(--radius-xl) !important;
      color: var(--color-gray-700) !important;
      border: 2px solid var(--color-gray-300) !important;
      transition: var(--transition-base) !important;

      &:hover {
        background-color: var(--color-gray-100) !important;
        border-color: var(--color-gray-400) !important;
        transform: translateY(-1px) !important;
      }
    }

    .pricing-highlight {
      padding: var(--spacing-5) var(--spacing-6);
      background: linear-gradient(135deg, var(--color-success-50), var(--color-primary-50));
      border-radius: var(--radius-xl);
      border: 1px solid var(--color-success-200);
      box-shadow: var(--shadow-sm);
    }

    .pricing-text {
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-semibold);
      color: var(--color-success-700);
      margin: 0;
    }

    .hero-image {
      display: flex;
      justify-content: center;
      align-items: center;
      position: relative;
    }

    .app-preview {
      width: 320px;
      height: 480px;
      background: linear-gradient(135deg, var(--color-primary-600), var(--color-primary-700), var(--color-accent-600));
      border-radius: var(--radius-3xl);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      color: white;
      box-shadow: var(--shadow-2xl);
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
        animation: shimmer 3s infinite;
      }
    }

    @keyframes shimmer {
      0% { transform: translateX(-100%); }
      100% { transform: translateX(100%); }
    }

    .preview-icon {
      font-size: 96px;
      width: 96px;
      height: 96px;
      margin-bottom: var(--spacing-4);
      filter: drop-shadow(0 4px 8px rgba(0,0,0,0.2));
    }

    .features-section, .how-it-works-section {
      padding: var(--spacing-20) var(--spacing-6);
      max-width: 1200px;
      margin: 0 auto;
    }

    .features-section {
      background: var(--color-white);
    }

    .how-it-works-section {
      background: var(--color-gray-50);
    }

    .section-header {
      text-align: center;
      margin-bottom: var(--spacing-16);
    }

    .section-header h2 {
      font-size: var(--font-size-4xl);
      font-weight: var(--font-weight-bold);
      margin-bottom: var(--spacing-4);
      color: var(--color-gray-900);
      letter-spacing: var(--letter-spacing-tight);
    }

    .section-header p {
      font-size: var(--font-size-xl);
      color: var(--color-gray-600);
      max-width: 600px;
      margin: 0 auto;
      line-height: var(--line-height-relaxed);
    }

    .features-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
      gap: var(--spacing-8);
    }

    .feature-card {
      transition: var(--transition-base);
      border-radius: var(--radius-2xl);
      overflow: hidden;

      &:hover {
        transform: translateY(-8px);
        box-shadow: var(--shadow-xl);
      }
    }

    .steps-container {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: var(--spacing-10);
    }

    .step {
      text-align: center;
      padding: var(--spacing-8);
      background: var(--color-white);
      border-radius: var(--radius-2xl);
      box-shadow: var(--shadow-sm);
      transition: var(--transition-base);

      &:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-lg);
      }
    }

    .step-number {
      width: 80px;
      height: 80px;
      border-radius: var(--radius-full);
      background: linear-gradient(135deg, var(--color-primary-600), var(--color-primary-700));
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: var(--font-size-2xl);
      font-weight: var(--font-weight-bold);
      margin: 0 auto var(--spacing-6);
      box-shadow: var(--shadow-lg);
    }

    .step h3 {
      font-size: var(--font-size-2xl);
      margin-bottom: var(--spacing-3);
      color: var(--color-gray-900);
      font-weight: var(--font-weight-semibold);
    }

    .step p {
      color: var(--color-gray-600);
      line-height: var(--line-height-relaxed);
      font-size: var(--font-size-base);
    }

    .cta-section {
      background: linear-gradient(135deg, var(--color-primary-600), var(--color-primary-700), var(--color-accent-600));
      color: white;
      text-align: center;
      padding: var(--spacing-20) var(--spacing-6);
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        opacity: 0.3;
      }
    }

    .cta-content {
      position: relative;
      z-index: 1;
      max-width: 800px;
      margin: 0 auto;
    }

    .cta-content h2 {
      font-size: var(--font-size-4xl);
      font-weight: var(--font-weight-bold);
      margin-bottom: var(--spacing-4);
      letter-spacing: var(--letter-spacing-tight);
    }

    .cta-content p {
      font-size: var(--font-size-xl);
      margin-bottom: var(--spacing-8);
      opacity: 0.95;
      line-height: var(--line-height-relaxed);
    }

    .cta-actions {
      display: flex;
      gap: var(--spacing-4);
      justify-content: center;
      flex-wrap: wrap;
      align-items: center;
    }

    .cta-actions .cta-button {
      background: var(--color-white) !important;
      color: var(--color-primary-700) !important;

      &:hover {
        background: var(--color-gray-100) !important;
        transform: translateY(-2px) !important;
      }
    }

    .cta-actions .secondary-button {
      border-color: rgba(255,255,255,0.3) !important;
      color: white !important;

      &:hover {
        background-color: rgba(255,255,255,0.1) !important;
        border-color: rgba(255,255,255,0.5) !important;
      }
    }

    // Responsive Design
    @media (max-width: 1024px) {
      .hero-section {
        gap: var(--spacing-12);
        padding: var(--spacing-16) var(--spacing-4);
      }

      .features-section, .how-it-works-section {
        padding: var(--spacing-16) var(--spacing-4);
      }

      .cta-section {
        padding: var(--spacing-16) var(--spacing-4);
      }
    }

    @media (max-width: 768px) {
      .hero-section {
        grid-template-columns: 1fr;
        text-align: center;
        padding: var(--spacing-12) var(--spacing-4);
        gap: var(--spacing-10);
      }

      .hero-title {
        font-size: var(--font-size-4xl);
      }

      .hero-subtitle {
        font-size: var(--font-size-lg);
      }

      .app-preview {
        width: 280px;
        height: 420px;
      }

      .features-section, .how-it-works-section {
        padding: var(--spacing-12) var(--spacing-4);
      }

      .section-header h2 {
        font-size: var(--font-size-3xl);
      }

      .section-header p {
        font-size: var(--font-size-lg);
      }

      .cta-section {
        padding: var(--spacing-12) var(--spacing-4);
      }

      .cta-content h2 {
        font-size: var(--font-size-3xl);
      }

      .cta-content p {
        font-size: var(--font-size-lg);
      }

      .cta-actions {
        flex-direction: column;
        align-items: stretch;
      }

      .cta-actions .cta-button,
      .cta-actions .secondary-button {
        width: 100%;
        max-width: 300px;
        margin: 0 auto;
      }
    }

    @media (max-width: 480px) {
      .hero-section {
        padding: var(--spacing-8) var(--spacing-3);
      }

      .hero-title {
        font-size: var(--font-size-3xl);
      }

      .app-preview {
        width: 240px;
        height: 360px;
      }

      .features-grid {
        grid-template-columns: 1fr;
      }

      .steps-container {
        grid-template-columns: 1fr;
      }
    }
  `]
})
export class HomeComponent {
  pricing = environment.pricing;
}
