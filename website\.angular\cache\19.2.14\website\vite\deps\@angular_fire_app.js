import {
  DEFAULT_ENTRY_NAME,
  FirebaseApp,
  FirebaseAppModule,
  FirebaseApps,
  FirebaseError,
  SDK_VERSION,
  _addComponent,
  _addOrOverwriteComponent,
  _apps,
  _clearComponents,
  _components,
  _getProvider,
  _isFirebaseApp,
  _isFirebaseServerApp,
  _registerComponent,
  _removeServiceInstance,
  _serverApps,
  deleteApp,
  firebaseApp$,
  getApp2 as getApp,
  getApps,
  initializeApp,
  initializeServerApp,
  onLog,
  provideFirebaseApp,
  registerVersion2 as registerVersion,
  setLogLevel
} from "./chunk-WB3LAIAR.js";
import "./chunk-VBEXP32T.js";
import "./chunk-FBVV7HBG.js";
import "./chunk-NUWUJFVC.js";
import "./chunk-JOIKPE53.js";
export {
  FirebaseApp,
  FirebaseAppModule,
  FirebaseA<PERSON>,
  FirebaseError,
  SDK_VERSION,
  DEFAULT_ENTRY_NAME as _DEFAULT_ENTRY_NAME,
  _addComponent,
  _addOrOverwriteComponent,
  _apps,
  _clearComponents,
  _components,
  _getProvider,
  _isFirebaseApp,
  _isFirebaseServerApp,
  _registerComponent,
  _removeServiceInstance,
  _serverApps,
  deleteApp,
  firebaseApp$,
  getApp,
  getApps,
  initializeApp,
  initializeServerApp,
  onLog,
  provideFirebaseApp,
  registerVersion,
  setLogLevel
};
